(()=>{var e={6746:(e,t,s)=>{"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a=i(s(9196)),n=i(s(9156)),o=i(s(6743));function i(e){return e&&e.__esModule?e:{default:e}}var l=void 0;function c(e,t){var s,o,i,d,p,u,m,g,h=[],y={};for(u=0;u<e.length;u++)if("string"!==(p=e[u]).type){if(!t.hasOwnProperty(p.value)||void 0===t[p.value])throw new Error("Invalid interpolation, missing component node: `"+p.value+"`");if("object"!==r(t[p.value]))throw new Error("Invalid interpolation, component node must be a ReactElement or null: `"+p.value+"`","\n> "+l);if("componentClose"===p.type)throw new Error("Missing opening component token: `"+p.value+"`");if("componentOpen"===p.type){s=t[p.value],i=u;break}h.push(t[p.value])}else h.push(p.value);return s&&(d=function(e,t){var s,r,a=t[e],n=0;for(r=e+1;r<t.length;r++)if((s=t[r]).value===a.value){if("componentOpen"===s.type){n++;continue}if("componentClose"===s.type){if(0===n)return r;n--}}throw new Error("Missing closing component token `"+a.value+"`")}(i,e),m=c(e.slice(i+1,d),t),o=a.default.cloneElement(s,{},m),h.push(o),d<e.length-1&&(g=c(e.slice(d+1),t),h=h.concat(g))),1===h.length?h[0]:(h.forEach((function(e,t){e&&(y["interpolation-child-"+t]=e)})),(0,n.default)(y))}t.Z=function(e){var t=e.mixedString,s=e.components,a=e.throwErrors;if(l=t,!s)return t;if("object"!==(void 0===s?"undefined":r(s))){if(a)throw new Error("Interpolation Error: unable to process `"+t+"` because components is not an object");return t}var n=(0,o.default)(t);try{return c(n,s)}catch(e){if(a)throw new Error("Interpolation Error: unable to process `"+t+"` because of error `"+e.message+"`");return t}}},6743:e=>{"use strict";function t(e){return e.match(/^\{\{\//)?{type:"componentClose",value:e.replace(/\W/g,"")}:e.match(/\/\}\}$/)?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.match(/^\{\{/)?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}e.exports=function(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(t)}},9156:(e,t,s)=>{"use strict";var r=s(9196),a="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,n=s(7942),o=s(9179),i=s(397),l=".",c=":",d="function"==typeof Symbol&&Symbol.iterator,p="@@iterator";function u(e,t){return e&&"object"==typeof e&&null!=e.key?(s=e.key,r={"=":"=0",":":"=2"},"$"+(""+s).replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var s,r}function m(e,t,s,r){var n,i=typeof e;if("undefined"!==i&&"boolean"!==i||(e=null),null===e||"string"===i||"number"===i||"object"===i&&e.$$typeof===a)return s(r,e,""===t?l+u(e,0):t),1;var g=0,h=""===t?l:t+c;if(Array.isArray(e))for(var y=0;y<e.length;y++)g+=m(n=e[y],h+u(n,y),s,r);else{var f=function(e){var t=e&&(d&&e[d]||e[p]);if("function"==typeof t)return t}(e);if(f)for(var w,b=f.call(e),E=0;!(w=b.next()).done;)g+=m(n=w.value,h+u(n,E++),s,r);else if("object"===i){var _=""+e;o(!1,"Objects are not valid as a React child (found: %s).%s","[object Object]"===_?"object with keys {"+Object.keys(e).join(", ")+"}":_,"")}}return g}var g=/\/+/g;function h(e){return(""+e).replace(g,"$&/")}var y,f,w=b,b=function(e){var t=this;if(t.instancePool.length){var s=t.instancePool.pop();return t.call(s,e),s}return new t(e)};function E(e,t,s,r){this.result=e,this.keyPrefix=t,this.func=s,this.context=r,this.count=0}function _(e,t,s){var a,o,i=e.result,l=e.keyPrefix,c=e.func,d=e.context,p=c.call(d,t,e.count++);Array.isArray(p)?v(p,i,s,n.thatReturnsArgument):null!=p&&(r.isValidElement(p)&&(a=p,o=l+(!p.key||t&&t.key===p.key?"":h(p.key)+"/")+s,p=r.cloneElement(a,{key:o},void 0!==a.props?a.props.children:void 0)),i.push(p))}function v(e,t,s,r,a){var n="";null!=s&&(n=h(s)+"/");var o=E.getPooled(t,n,r,a);!function(e,t,s){null==e||m(e,"",t,s)}(e,_,o),E.release(o)}E.prototype.destructor=function(){this.result=null,this.keyPrefix=null,this.func=null,this.context=null,this.count=0},y=function(e,t,s,r){var a=this;if(a.instancePool.length){var n=a.instancePool.pop();return a.call(n,e,t,s,r),n}return new a(e,t,s,r)},(f=E).instancePool=[],f.getPooled=y||w,f.poolSize||(f.poolSize=10),f.release=function(e){var t=this;o(e instanceof t,"Trying to release an instance into a pool of a different type."),e.destructor(),t.instancePool.length<t.poolSize&&t.instancePool.push(e)},e.exports=function(e){if("object"!=typeof e||!e||Array.isArray(e))return i(!1,"React.addons.createFragment only accepts a single object. Got: %s",e),e;if(r.isValidElement(e))return i(!1,"React.addons.createFragment does not accept a ReactElement without a wrapper object."),e;o(1!==e.nodeType,"React.addons.createFragment(...): Encountered an invalid child; DOM elements are not valid children of React components.");var t=[];for(var s in e)v(e[s],t,s,n.thatReturnsArgument);return t}},7942:e=>{"use strict";function t(e){return function(){return e}}var s=function(){};s.thatReturns=t,s.thatReturnsFalse=t(!1),s.thatReturnsTrue=t(!0),s.thatReturnsNull=t(null),s.thatReturnsThis=function(){return this},s.thatReturnsArgument=function(e){return e},e.exports=s},9179:e=>{"use strict";e.exports=function(e,t,s,r,a,n,o,i){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[s,r,a,n,o,i],d=0;(l=new Error(t.replace(/%s/g,(function(){return c[d++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}},397:(e,t,s)=>{"use strict";var r=s(7942);e.exports=r},4530:(e,t)=>{var s;!function(){"use strict";var r={}.hasOwnProperty;function a(){for(var e=[],t=0;t<arguments.length;t++){var s=arguments[t];if(s){var n=typeof s;if("string"===n||"number"===n)e.push(s);else if(Array.isArray(s)){if(s.length){var o=a.apply(null,s);o&&e.push(o)}}else if("object"===n){if(s.toString!==Object.prototype.toString&&!s.toString.toString().includes("[native code]")){e.push(s.toString());continue}for(var i in s)r.call(s,i)&&s[i]&&e.push(i)}}}return e.join(" ")}e.exports?(a.default=a,e.exports=a):void 0===(s=function(){return a}.apply(t,[]))||(e.exports=s)}()},9196:e=>{"use strict";e.exports=window.React}},t={};function s(r){var a=t[r];if(void 0!==a)return a.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,s),n.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var r in t)s.o(t,r)&&!s.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};s.r(e),s.d(e,{DISMISS_ALERT:()=>Fl,NEW_REQUEST:()=>Ml,SNIPPET_EDITOR_FIND_CUSTOM_FIELDS:()=>Dl,wistiaEmbedPermission:()=>Nl});var t={};s.r(t),s.d(t,{loadSnippetEditorData:()=>Wl,updateData:()=>Bl});var r={};s.r(r),s.d(r,{getSnippetEditorData:()=>Ql,getSnippetEditorSlug:()=>Zl});var a={};s.r(a),s.d(a,{getAnalysisData:()=>nc});var n={};s.r(n),s.d(n,{getWincherPermalink:()=>Ac});var o={};s.r(o),s.d(o,{authorFirstName:()=>Oc,authorLastName:()=>Fc,category:()=>qc,categoryTitle:()=>Uc,currentDate:()=>Dc,currentDay:()=>Mc,currentMonth:()=>Nc,currentYear:()=>$c,date:()=>Bc,excerpt:()=>Wc,focusKeyphrase:()=>Yc,id:()=>zc,modified:()=>Kc,name:()=>jc,page:()=>Hc,pageNumber:()=>Vc,pageTotal:()=>Gc,permalink:()=>Zc,postContent:()=>Qc,postDay:()=>Xc,postMonth:()=>Jc,postTypeNamePlural:()=>td,postTypeNameSingular:()=>sd,postYear:()=>ed,primaryCategory:()=>rd,searchPhrase:()=>ad,separator:()=>nd,siteDescription:()=>od,siteName:()=>id,tag:()=>ld,term404:()=>cd,termDescription:()=>dd,termHierarchy:()=>pd,termTitle:()=>ud,title:()=>md,userDescription:()=>gd});const i=window.wp.data,l=window.wp.hooks;var c=s(9196),d=s.n(c);const p=window.wp.components,u=window.wp.element,m=window.wp.i18n,g=window.yoast.uiLibrary,h=window.yoast.propTypes;var y=s.n(h);const f=window.lodash,w=(e,t)=>{try{return(0,u.createInterpolateElement)(e,t)}catch(t){return console.error("Error in translation for:",e,t),e}};y().string.isRequired;const b=c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"}))})),E=c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{fillRule:"evenodd",d:"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),_=({learnMoreLink:e,thumbnail:t,wistiaEmbedPermission:s,upsellLink:r,isProductCopy:a,title:n,upsellLabel:o,newToText:i,bundleNote:l,ctbId:d})=>{const{onClose:p,initialFocus:u}=(0,g.useModalContext)(),h={a:(0,c.createElement)(U,{href:e,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,c.createElement)(E,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"})};return(0,c.createElement)(c.Fragment,null,(0,c.createElement)("div",{className:"yst-px-10 yst-pt-10 yst-introduction-gradient yst-text-center"},(0,c.createElement)("div",{className:"yst-relative yst-w-full"},(0,c.createElement)(ge,{videoId:"vmrahpfjxp",thumbnail:t,wistiaEmbedPermission:s}),(0,c.createElement)(g.Badge,{className:"yst-absolute yst-top-2 yst-end-4",variant:"info"},"Beta")),(0,c.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium yst-flex yst-flex-col yst-items-center"},(0,c.createElement)("span",{className:"yst-introduction-modal-uppercase yst-flex yst-gap-2 yst-items-center"},(0,c.createElement)("span",{className:"yst-logo-icon"}),i))),(0,c.createElement)("div",{className:"yst-px-10 yst-pb-4 yst-flex yst-flex-col yst-items-center"},(0,c.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,c.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},n),(0,c.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},w(a?(0,m.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,m.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for product titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"):(0,m.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,m.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"),h))),(0,c.createElement)("div",{className:"yst-w-full yst-flex yst-mt-10"},(0,c.createElement)(g.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:r,target:"_blank",ref:u,"data-action":"load-nfd-ctb","data-ctb-id":d},(0,c.createElement)(b,{className:"yst--ms-1 yst-me-2 yst-h-5 yst-w-5"}),o,(0,c.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,m.__)("(Opens in a new browser tab)","wordpress-seo")))),l,(0,c.createElement)(g.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:p},(0,m.__)("Close","wordpress-seo"))))};_.propTypes={learnMoreLink:y().string.isRequired,upsellLink:y().string.isRequired,thumbnail:y().shape({src:y().string.isRequired,width:y().string,height:y().string}).isRequired,wistiaEmbedPermission:y().shape({value:y().bool.isRequired,status:y().string.isRequired,set:y().func.isRequired}).isRequired,title:y().string,upsellLabel:y().string,newToText:y().string,isProductCopy:y().bool,bundleNote:y().oneOfType([y().string,y().element]),ctbId:y().string},_.defaultProps={title:(0,m.__)("Use AI to write your titles & meta descriptions!","wordpress-seo"),upsellLabel:(0,m.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,m.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:"Yoast SEO Premium",isProductCopy:!1,bundleNote:"",ctbId:"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"};const v=({learnMoreLink:e,thumbnail:t,wistiaEmbedPermission:s,upsellLink:r,upsellLabel:a,newToText:n,bundleNote:o,ctbId:i})=>{const{onClose:l,initialFocus:d}=(0,g.useModalContext)(),p={a:(0,c.createElement)(U,{href:e,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,c.createElement)(E,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"}),br:(0,c.createElement)("br",null)};return(0,c.createElement)(c.Fragment,null,(0,c.createElement)("div",{className:"yst-px-10 yst-pt-10 yst-introduction-gradient yst-text-center"},(0,c.createElement)("div",{className:"yst-relative yst-w-full"},(0,c.createElement)(ge,{videoId:"vun9z1dpfh",thumbnail:t,wistiaEmbedPermission:s}),(0,c.createElement)(g.Badge,{className:"yst-absolute yst-end-4 yst-text-center yst-justify-center",variant:"info",style:{top:"-8px"}},(0,m.__)("Beta","wordpress-seo-premium"))),(0,c.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium yst-flex yst-flex-col yst-items-center"},(0,c.createElement)("span",{className:"yst-introduction-modal-uppercase yst-flex yst-gap-2 yst-items-center"},(0,c.createElement)("span",{className:"yst-logo-icon"}),n))),(0,c.createElement)("div",{className:"yst-px-10 yst-pb-4 yst-flex yst-flex-col yst-items-center"},(0,c.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,c.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},(0,m.sprintf)(/* translators: %s: Expands to "Yoast AI" */
(0,m.__)("Optimize your SEO content with %s","wordpress-seo"),"Yoast AI")),(0,c.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},w((0,m.sprintf)(/* translators: %1$s is a break tag; %2$s and %3$s are anchor tags; %4$s is the arrow icon. */
(0,m.__)("Make content editing a breeze! Optimize your SEO content with quick, actionable suggestions at the click of a button.%1$s%2$sLearn more%3$s%4$s","wordpress-seo"),"<br/>","<a>","<ArrowNarrowRightIcon />","</a>"),p))),(0,c.createElement)("div",{className:"yst-w-full yst-flex yst-mt-6"},(0,c.createElement)(g.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:r,target:"_blank",ref:d,"data-action":"load-nfd-ctb","data-ctb-id":i},(0,c.createElement)(b,{className:"yst--ms-1 yst-me-2 yst-h-5 yst-w-5"}),a,(0,c.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,m.__)("(Opens in a new browser tab)","wordpress-seo")))),o,(0,c.createElement)(g.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:l},(0,m.__)("Close","wordpress-seo"))))};v.propTypes={learnMoreLink:y().string.isRequired,upsellLink:y().string.isRequired,thumbnail:y().shape({src:y().string.isRequired,width:y().string,height:y().string}).isRequired,wistiaEmbedPermission:y().shape({value:y().bool.isRequired,status:y().string.isRequired,set:y().func.isRequired}).isRequired,upsellLabel:y().string,newToText:y().string,bundleNote:y().oneOfType([y().string,y().element]),ctbId:y().string},v.defaultProps={upsellLabel:(0,m.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,m.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:"Yoast SEO Premium",bundleNote:"",ctbId:"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"};const k=({handleRefreshClick:e,supportLink:t})=>(0,c.createElement)("div",{className:"yst-flex yst-gap-2"},(0,c.createElement)(g.Button,{onClick:e},(0,m.__)("Refresh this page","wordpress-seo")),(0,c.createElement)(g.Button,{variant:"secondary",as:"a",href:t,target:"_blank",rel:"noopener"},(0,m.__)("Contact support","wordpress-seo")));k.propTypes={handleRefreshClick:y().func.isRequired,supportLink:y().string.isRequired};const x=({handleRefreshClick:e,supportLink:t})=>(0,c.createElement)("div",{className:"yst-grid yst-grid-cols-1 yst-gap-y-2"},(0,c.createElement)(g.Button,{className:"yst-order-last",onClick:e},(0,m.__)("Refresh this page","wordpress-seo")),(0,c.createElement)(g.Button,{variant:"secondary",as:"a",href:t,target:"_blank",rel:"noopener"},(0,m.__)("Contact support","wordpress-seo")));x.propTypes={handleRefreshClick:y().func.isRequired,supportLink:y().string.isRequired};const S=({error:e,children:t})=>(0,c.createElement)("div",{role:"alert",className:"yst-max-w-screen-sm yst-p-8 yst-space-y-4"},(0,c.createElement)(g.Title,null,(0,m.__)("Something went wrong. An unexpected error occurred.","wordpress-seo")),(0,c.createElement)("p",null,(0,m.__)("We're very sorry, but it seems like the following error has interrupted our application:","wordpress-seo")),(0,c.createElement)(g.Alert,{variant:"error"},(null==e?void 0:e.message)||(0,m.__)("Undefined error message.","wordpress-seo")),(0,c.createElement)("p",null,(0,m.__)("Unfortunately, this means that any unsaved changes in this section will be lost. You can try and refresh this page to resolve the problem. If this error still occurs, please get in touch with our support team, and we'll get you all the help you need!","wordpress-seo")),t);S.propTypes={error:y().object.isRequired,children:y().node},S.VerticalButtons=x,S.HorizontalButtons=k;var T;function R(){return R=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},R.apply(this,arguments)}y().string,y().node.isRequired,y().node.isRequired,y().node,y().oneOf(Object.keys({lg:{grid:"yst-grid lg:yst-grid-cols-3 lg:yst-gap-12",col1:"yst-col-span-1",col2:"lg:yst-mt-0 lg:yst-col-span-2"},xl:{grid:"yst-grid xl:yst-grid-cols-3 xl:yst-gap-12",col1:"yst-col-span-1",col2:"xl:yst-mt-0 xl:yst-col-span-2"},"2xl":{grid:"yst-grid 2xl:yst-grid-cols-3 2xl:yst-gap-12",col1:"yst-col-span-1",col2:"2xl:yst-mt-0 2xl:yst-col-span-2"}}));const C=e=>c.createElement("svg",R({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 1000 1000"},e),T||(T=c.createElement("path",{fill:"#fff",d:"M500 0C223.9 0 0 223.9 0 500s223.9 500 500 500 500-223.9 500-500S776.1 0 500 0Zm87.2 412.4c0-21.9 4.3-40.2 13.1-54.4s24-27.1 45.9-38.2l10.1-4.9c17.8-9 22.4-16.7 22.4-26 0-11.1-9.5-19.1-25-19.1-18.3 0-32.2 9.5-41.8 28.9l-24.7-24.8c5.4-11.6 14.1-20.9 25.8-28.1a70.8 70.8 0 0 1 38.9-11.1c17.8 0 33.3 4.6 45.9 14.2s19.4 22.7 19.4 39.4c0 26.6-15 42.9-43.1 57.3l-15.7 8c-16.8 8.5-25.1 16-27.4 29.4h85.4v35.4H587.2Zm-82.1 373.3c-157.8 0-285.7-127.9-285.7-285.7s127.9-285.7 285.7-285.7a286.4 286.4 0 0 1 55.9 5.5l-55.9 116.9c-90 0-163.3 73.3-163.3 163.3s73.3 163.3 163.3 163.3a162.8 162.8 0 0 0 106.4-39.6l61.8 107.2a283.9 283.9 0 0 1-168.2 54.8ZM705 704.1l-70.7-122.5H492.9l70.7-122.4H705l70.7 122.4Z"}))),I=window.ReactDOM;var P,L,A;(L=P||(P={})).Pop="POP",L.Push="PUSH",L.Replace="REPLACE",function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(A||(A={})),new Set(["lazy","caseSensitive","path","id","index","children"]),Error;const O=["post","put","patch","delete"],F=(new Set(O),["get",...O]);new Set(F),new Set([301,302,303,307,308]),new Set([307,308]),Symbol("deferred"),c.Component,c.startTransition,new Promise((()=>{})),c.Component,new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);try{window.__reactRouterVersion="6"}catch(e){}var D,M,N,q;new Map,c.startTransition,I.flushSync,c.useId,"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement,(q=D||(D={})).UseScrollRestoration="useScrollRestoration",q.UseSubmit="useSubmit",q.UseSubmitFetcher="useSubmitFetcher",q.UseFetcher="useFetcher",q.useViewTransitionState="useViewTransitionState",(N=M||(M={})).UseFetcher="useFetcher",N.UseFetchers="useFetchers",N.UseScrollRestoration="useScrollRestoration",y().string.isRequired,y().string;const U=({href:e,children:t,...s})=>(0,c.createElement)(g.Link,{target:"_blank",rel:"noopener noreferrer",...s,href:e},t,(0,c.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,m.__)("(Opens in a new browser tab)","wordpress-seo")));U.propTypes={href:y().string.isRequired,children:y().node},U.defaultProps={children:null};const $=c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17 8l4 4m0 0l-4 4m4-4H3"}))}));var B,W,Y;function z(){return z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},z.apply(this,arguments)}const K=e=>c.createElement("svg",z({xmlns:"http://www.w3.org/2000/svg",id:"star-rating-half_svg__Layer_1","data-name":"Layer 1",viewBox:"0 0 500 475.53"},e),B||(B=c.createElement("defs",null,c.createElement("style",null,".star-rating-half_svg__cls-1{fill:#fbbf24}"))),W||(W=c.createElement("path",{d:"M250 392.04 98.15 471.87l29-169.09L4.3 183.03l169.77-24.67L250 4.52l75.93 153.84 169.77 24.67-122.85 119.75 29 169.09L250 392.04z",className:"star-rating-half_svg__cls-1"})),Y||(Y=c.createElement("path",{d:"m250 9.04 73.67 149.27.93 1.88 2.08.3 164.72 23.94-119.19 116.19-1.51 1.47.36 2.07 28.14 164.06-147.34-77.46-1.86-1-1.86 1-147.34 77.46 28.14-164.06.36-2.07-1.51-1.47L8.6 184.43l164.72-23.9 2.08-.3.93-1.88L250 9.04m0-9-77.25 156.49L0 181.64l125 121.89-29.51 172L250 394.3l154.51 81.23-29.51-172 125-121.89-172.75-25.11L250 0Z",className:"star-rating-half_svg__cls-1"})),c.createElement("path",{d:"m500 181.64-172.75-25.11L250 0v394.3l154.51 81.23L375 303.48l125-121.84z",style:{fill:"#f3f4f6"}}));function j(){return j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},j.apply(this,arguments)}const H=e=>c.createElement("svg",j({xmlns:"http://www.w3.org/2000/svg","data-name":"Layer 1",viewBox:"0 0 500 475.53"},e),c.createElement("path",{d:"m250 0 77.25 156.53L500 181.64 375 303.48l29.51 172.05L250 394.3 95.49 475.53 125 303.48 0 181.64l172.75-25.11L250 0z",style:{fill:"#fbbf24"}}));var V,G,Z,Q,X,J,ee,te,se;function re(){return re=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},re.apply(this,arguments)}const ae=e=>c.createElement("svg",re({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 500 500"},e),V||(V=c.createElement("path",{fill:"#a4286a",d:"M80 0h340a80 80 0 0 1 80 80v420H80a80 80 0 0 1-80-80V80A80 80 0 0 1 80 0z"})),G||(G=c.createElement("path",{fill:"#6c2548",d:"M437.61 2 155.89 500H500V80a80 80 0 0 0-62.39-78z"})),Z||(Z=c.createElement("path",{fill:"#fff",d:"M74.4 337.3v34.9c21.6-.9 38.5-8 52.8-22.5s27.4-38 39.9-72.9l92.6-248h-44.8L140.3 236l-37-116.2h-41l54.4 139.8a57.54 57.54 0 0 1 0 41.8c-5.5 14.2-15.4 30.9-42.3 35.9z"})),Q||(Q=c.createElement("circle",{cx:368.33,cy:124.68,r:97.34,fill:"#9fda4f",transform:"rotate(-45 368.335 124.68)"})),X||(X=c.createElement("path",{fill:"#77b227",d:"m416.2 39.93-95.74 169.51A97.34 97.34 0 1 0 416.2 39.93z"})),J||(J=c.createElement("path",{fill:"#fec228",d:"m294.78 254.75-.15-.08-.13-.07a63.6 63.6 0 0 0-62.56 110.76h.13a63.6 63.6 0 0 0 62.71-110.67z"})),ee||(ee=c.createElement("path",{fill:"#f49a00",d:"m294.5 254.59-62.56 110.76a63.6 63.6 0 1 0 62.56-110.76z"})),te||(te=c.createElement("path",{fill:"#ff4e47",d:"M222.31 450.07A38.16 38.16 0 0 0 203 416.83a38.18 38.18 0 1 0 19.41 33.27z"})),se||(se=c.createElement("path",{fill:"#ed261f",d:"m202.9 416.8-37.54 66.48a38.17 38.17 0 0 0 37.54-66.48z"}))),ne=({link:e,linkProps:t,isPromotionActive:s})=>{let r=(0,u.useMemo)((()=>(0,m.__)("Use AI to generate titles and meta descriptions, automatically redirect deleted pages, get 24/7 support, and much, much more!","wordpress-seo")),[]),a=w((0,m.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,m.__)("%1$sGet%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,c.createElement)("span",{className:"yst-whitespace-nowrap"})});const n=s("black-friday-2024-promotion");return n&&(r=(0,u.useMemo)((()=>(0,m.__)("If you were thinking about upgrading, now's the time! 30% OFF ends 3rd Dec 11am (CET)","wordpress-seo")),[]),a=w((0,m.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,m.__)("%1$sBuy%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,c.createElement)("span",{className:"yst-whitespace-nowrap"})})),(0,c.createElement)("div",{className:"yst-p-6 yst-rounded-lg yst-text-white yst-bg-primary-500 yst-shadow"},(0,c.createElement)("figure",{className:"yst-logo-square yst-w-16 yst-h-16 yst-mx-auto yst-overflow-hidden yst-border yst-border-white yst-rounded-xl yst-rounded-br-none yst-relative yst-z-10 yst-mt-[-2.6rem]"},(0,c.createElement)(ae,null)),n&&(0,c.createElement)("div",{className:"sidebar__sale_banner_container"},(0,c.createElement)("div",{className:"sidebar__sale_banner"},(0,c.createElement)("span",{className:"banner_text"},(0,m.__)("30% OFF - BLACK FRIDAY","wordpress-seo")))),(0,c.createElement)(g.Title,{as:"h2",className:"yst-mt-6 yst-text-base yst-font-extrabold yst-text-white"},a),(0,c.createElement)("p",{className:"yst-mt-2"},r),(0,c.createElement)(g.Button,{as:"a",variant:"upsell",href:e,target:"_blank",rel:"noopener",className:"yst-flex yst-justify-center yst-gap-2 yst-mt-4 focus:yst-ring-offset-primary-500",...t},(0,c.createElement)("span",null,n?(0,m.__)("Buy now","wordpress-seo"):a),(0,c.createElement)($,{className:"yst-w-4 yst-h-4 yst-icon-rtl"})),(0,c.createElement)("p",{className:"yst-text-center yst-text-xs yst-mx-2 yst-font-light yst-leading-5 yst-mt-2"},!n&&(0,c.createElement)(c.Fragment,null,(0,m.__)("Only $/€/£99 per year (ex VAT)","wordpress-seo"),(0,c.createElement)("br",null)),(0,m.__)("30-day money back guarantee.","wordpress-seo")),(0,c.createElement)("hr",{className:"yst-border-t yst-border-primary-300 yst-my-4"}),(0,c.createElement)("a",{className:"yst-block yst-mt-4 yst-no-underline",href:"https://www.g2.com/products/yoast-yoast/reviews",target:"_blank",rel:"noopener noreferrer"},(0,c.createElement)("span",{className:"yst-font-medium yst-text-white hover:yst-underline"},(0,m.__)("Read reviews from real users","wordpress-seo")),(0,c.createElement)("span",{className:"yst-flex yst-gap-2 yst-mt-2 yst-items-center"},(0,c.createElement)(C,{className:"yst-w-5 yst-h-5"}),(0,c.createElement)("span",{className:"yst-flex yst-gap-1"},(0,c.createElement)(H,{className:"yst-w-5 yst-h-5"}),(0,c.createElement)(H,{className:"yst-w-5 yst-h-5"}),(0,c.createElement)(H,{className:"yst-w-5 yst-h-5"}),(0,c.createElement)(H,{className:"yst-w-5 yst-h-5"}),(0,c.createElement)(K,{className:"yst-w-5 yst-h-5"})),(0,c.createElement)("span",{className:"yst-text-sm yst-font-semibold yst-text-white"},"4.6 / 5"))))};ne.propTypes={link:y().string.isRequired,linkProps:y().object,isPromotionActive:y().func},ne.defaultProps={linkProps:{},isPromotionActive:f.noop};const oe=()=>[(0,m.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,m.__)("%1$sAI%2$s: Better SEO titles and meta descriptions, faster.","wordpress-seo"),"<strong>","</strong>"),(0,m.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,m.__)("%1$sMultiple keywords%2$s: Rank higher for more searches.","wordpress-seo"),"<strong>","</strong>"),(0,m.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,m.__)("%1$sSuper fast%2$s internal linking suggestions.","wordpress-seo"),"<strong>","</strong>"),(0,m.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,m.__)("%1$sNo more broken links%2$s: Automatic redirect manager.","wordpress-seo"),"<strong>","</strong>"),(0,m.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,m.__)("%1$sAppealing social previews%2$s people actually want to click on.","wordpress-seo"),"<strong>","</strong>"),(0,m.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,m.__)("%1$s24/7 support%2$s: Also on evenings and weekends.","wordpress-seo"),"<strong>","</strong>")],ie=({premiumLink:e,premiumUpsellConfig:t,isPromotionActive:s})=>{const r=s("black-friday-2024-promotion");return(0,c.createElement)(g.Paper,{as:"div",className:"xl:yst-max-w-3xl"},r&&(0,c.createElement)("div",{className:"yst-rounded-t-lg yst-h-9 yst-flex yst-justify-between yst-items-center yst-bg-black yst-text-amber-300 yst-px-4 yst-text-lg yst-border-b yst-border-amber-300 yst-border-solid yst-font-semibold"},(0,c.createElement)("div",null,(0,m.__)("30% OFF","wordpress-seo")),(0,c.createElement)("div",null,(0,m.__)("BLACK FRIDAY","wordpress-seo"))),(0,c.createElement)("div",{className:"yst-p-6 yst-flex yst-flex-col"},(0,c.createElement)(g.Title,{as:"h2",size:"4",className:"yst-text-xl yst-text-primary-500"},(0,m.sprintf)(/* translators: %s expands to "Yoast SEO" Premium */
(0,m.__)("Upgrade to %s","wordpress-seo"),"Yoast SEO Premium")),(0,c.createElement)("ul",{className:"yst-grid yst-grid-cols-1 sm:yst-grid-cols-2 yst-gap-x-6 yst-list-disc yst-ps-[1em] yst-list-outside yst-text-slate-800 yst-mt-6"},oe().map(((e,t)=>(0,c.createElement)("li",{key:`upsell-benefit-${t}`},w(e,{strong:(0,c.createElement)("span",{className:"yst-font-semibold"})}))))),(0,c.createElement)(g.Button,{as:"a",variant:"upsell",size:"extra-large",href:e,className:"yst-gap-2 yst-mt-4",target:"_blank",rel:"noopener",...t},r?(0,m.__)("Claim your 30% off now!","wordpress-seo"):(0,m.sprintf)(/* translators: %s expands to "Yoast SEO" Premium */
(0,m.__)("Explore %s now!","wordpress-seo"),"Yoast SEO Premium"),(0,c.createElement)($,{className:"yst-w-4 yst-h-4 yst-icon-rtl"}))))};ie.propTypes={premiumLink:y().string.isRequired,premiumUpsellConfig:y().object,isPromotionActive:y().func},ie.defaultProps={premiumUpsellConfig:{},isPromotionActive:f.noop},y().string.isRequired,y().object.isRequired,y().string.isRequired,y().func.isRequired,c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"}))})),y().bool.isRequired,y().func,y().func,y().string.isRequired,y().string.isRequired,y().string.isRequired,y().string.isRequired;const le=window.yoast.reactHelmet,ce="idle",de="loading",pe="showPlay",ue="askPermission",me="isPlaying",ge=({videoId:e,thumbnail:t,wistiaEmbedPermission:s})=>{const[r,a]=(0,u.useState)(s.value?me:pe),n=(0,u.useCallback)((()=>a(me)),[a]),o=(0,u.useCallback)((()=>{s.value?n():a(ue)}),[s.value,n,a]),i=(0,u.useCallback)((()=>a(pe)),[a]),l=(0,u.useCallback)((()=>{s.set(!0),n()}),[s.set,n]);return(0,c.createElement)(c.Fragment,null,s.value&&(0,c.createElement)(le.Helmet,null,(0,c.createElement)("script",{src:"https://fast.wistia.com/assets/external/E-v1.js",async:!0})),(0,c.createElement)("div",{className:"yst-relative yst-w-full yst-h-0 yst-pt-[56.25%] yst-overflow-hidden yst-rounded-md yst-drop-shadow-md yst-bg-white"},r===pe&&(0,c.createElement)("button",{type:"button",className:"yst-absolute yst-inset-0 yst-button yst-p-0 yst-border-none yst-bg-white yst-transition-opacity yst-duration-1000 yst-opacity-100",onClick:o},(0,c.createElement)("img",{className:"yst-w-full yst-h-auto",alt:"",loading:"lazy",decoding:"async",...t})),r===ue&&(0,c.createElement)("div",{className:"yst-absolute yst-inset-0 yst-flex yst-flex-col yst-items-center yst-justify-center yst-bg-white"},(0,c.createElement)("p",{className:"yst-max-w-xs yst-mx-auto yst-text-center"},s.status===de&&(0,c.createElement)(g.Spinner,null),s.status!==de&&(0,m.sprintf)(/* translators: %1$s expands to Yoast SEO. %2$s expands to Wistia. */
(0,m.__)("To see this video, you need to allow %1$s to load embedded videos from %2$s.","wordpress-seo"),"Yoast SEO","Wistia")),(0,c.createElement)("div",{className:"yst-flex yst-mt-6 yst-gap-x-4"},(0,c.createElement)(g.Button,{type:"button",variant:"secondary",onClick:i,disabled:s.status===de},(0,m.__)("Deny","wordpress-seo")),(0,c.createElement)(g.Button,{type:"button",variant:"primary",onClick:l,disabled:s.status===de},(0,m.__)("Allow","wordpress-seo")))),s.value&&r===me&&(0,c.createElement)("div",{className:"yst-absolute yst-w-full yst-h-full yst-top-0 yst-right-0"},null===e&&(0,c.createElement)(g.Spinner,{className:"yst-h-full yst-mx-auto"}),null!==e&&(0,c.createElement)("div",{className:`wistia_embed wistia_async_${e} videoFoam=true`}))))};ge.propTypes={videoId:y().string.isRequired,thumbnail:y().shape({src:y().string.isRequired,width:y().string,height:y().string}).isRequired,wistiaEmbedPermission:y().shape({value:y().bool.isRequired,status:y().string.isRequired,set:y().func.isRequired}).isRequired},c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),y().bool.isRequired,y().func.isRequired,y().func,y().string;const he="yoast-seo/editor",ye=()=>{const e=(e=>{const t=(0,i.useSelect)((e=>e(he).getIsPremium()),[]),s=(0,i.useSelect)((e=>e(he).getIsWooSeoActive()),[]),r=(0,i.useSelect)((e=>e(he).getIsWooCommerceActive()),[]),a=(0,i.useSelect)((e=>e(he).getIsProduct()),[]),n=(0,i.useSelect)((e=>e(he).getIsProductTerm()),[]),o={upsellLink:e.premium};if(r&&(a||n)){const r=(0,m.sprintf)(/* translators: %1$s expands to Yoast SEO Premium, %2$s expands to Yoast WooCommerce SEO. */
(0,m.__)("%1$s + %2$s","wordpress-seo"),"Yoast SEO Premium","Yoast WooCommerce SEO");o.newToText=(0,m.sprintf)(/* translators: %1$s expands to Yoast SEO Premium and Yoast WooCommerce SEO. */
(0,m.__)("New in %1$s","wordpress-seo"),r),t?(o.upsellLabel=(0,m.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,m.__)("Unlock with %1$s","wordpress-seo"),"Yoast WooCommerce SEO"),o.upsellLink=e.woo,o.ctbId="5b32250e-e6f0-44ae-ad74-3cefc8e427f9"):s||(o.upsellLabel=`${(0,m.sprintf)(/* translators: %1$s expands to Woo Premium bundle. */
(0,m.__)("Unlock with the %1$s","wordpress-seo"),"Woo Premium bundle")}*`,o.bundleNote=(0,c.createElement)("div",{className:"yst-text-xs yst-text-slate-500 yst-mt-2"},`*${r}`),o.upsellLink=e.bundle,o.ctbId="c7e7baa1-2020-420c-a427-89701700b607")}return o})({premium:(0,i.useSelect)((e=>e(he).selectLink("https://yoa.st/ai-generator-upsell")),[]),bundle:(0,i.useSelect)((e=>e(he).selectLink("https://yoa.st/ai-generator-upsell-woo-seo-premium-bundle")),[]),woo:(0,i.useSelect)((e=>e(he).selectLink("https://yoa.st/ai-generator-upsell-woo-seo")),[])}),t=(0,i.useSelect)((e=>e(he).getIsWooCommerceActive()),[]),s=(0,i.useSelect)((e=>e(he).getIsProduct()),[]);t&&s&&(e.title=(0,m.__)("Generate product titles & descriptions with AI!","wordpress-seo"),e.isProductCopy=!0);const r=(0,i.useSelect)((e=>e(he).selectLink("https://yoa.st/ai-generator-learn-more")),[]),a=(0,i.useSelect)((e=>e(he).selectImageLink("ai-generator-preview.png")),[]),n=(0,u.useMemo)((()=>({src:a,width:"432",height:"244"})),[a]),o=(0,i.useSelect)((e=>e(he).selectWistiaEmbedPermissionValue()),[]),l=(0,i.useSelect)((e=>e(he).selectWistiaEmbedPermissionStatus()),[]),{setWistiaEmbedPermission:d}=(0,i.useDispatch)(he),p=(0,u.useMemo)((()=>({value:o,status:l,set:d})),[o,l,d]);return(0,c.createElement)(_,{learnMoreLink:r,thumbnail:n,wistiaEmbedPermission:p,...e})},fe=({fieldId:e})=>{const[t,,,s,r]=(0,g.useToggleState)(!1),a=(0,u.useCallback)((()=>{s()}),[s]),n=(0,u.useRef)(null);return(0,c.createElement)(c.Fragment,null,(0,c.createElement)("button",{type:"button",id:`yst-replacevar__use-ai-button__${e}`,className:"yst-replacevar__use-ai-button-upsell",onClick:a},(0,m.__)("Use AI","wordpress-seo")),(0,c.createElement)(g.Modal,{className:"yst-introduction-modal",isOpen:t,onClose:r,initialFocus:n},(0,c.createElement)(g.Modal.Panel,{className:"yst-max-w-lg yst-p-0 yst-rounded-3xl"},(0,c.createElement)(ye,{onClose:r,focusElementRef:n}))))};fe.propTypes={fieldId:y().string.isRequired};const we="yoast-seo/editor",be=window.yoast.analysis;function Ee(){}const _e=window.yoast.externals.redux;function ve(e){return e.sort(((e,t)=>e._identifier.localeCompare(t._identifier)))}function ke(){return(0,f.get)(window,"wpseoScriptData.metabox",{intl:{},isRtl:!1})}function xe(){const e=ke();return(0,f.get)(e,"contentLocale","en_US")}function Se(){const e=ke();return!0===(0,f.get)(e,"contentAnalysisActive",!1)}function Te(){const e=ke();return!0===(0,f.get)(e,"keywordAnalysisActive",!1)}function Re(){const e=ke();return!0===(0,f.get)(e,"inclusiveLanguageAnalysisActive",!1)}const Ce=window.yoast.featureFlag;class Ie{constructor(e){this.refresh=e,this.loaded=!1,this.preloadThreshold=3e3,this.plugins={},this.modifications={},this._registerPlugin=this._registerPlugin.bind(this),this._ready=this._ready.bind(this),this._reloaded=this._reloaded.bind(this),this._registerModification=this._registerModification.bind(this),this._registerAssessment=this._registerAssessment.bind(this),this._applyModifications=this._applyModifications.bind(this),setTimeout(this._pollLoadingPlugins.bind(this),1500)}_registerPlugin(e,t){return(0,f.isString)(e)?(0,f.isUndefined)(t)||(0,f.isObject)(t)?!1===this._validateUniqueness(e)?(console.error("Failed to register plugin. Plugin with name "+e+" already exists"),!1):(this.plugins[e]=t,!0):(console.error("Failed to register plugin "+e+". Expected parameters `options` to be a object."),!1):(console.error("Failed to register plugin. Expected parameter `pluginName` to be a string."),!1)}_ready(e){return(0,f.isString)(e)?(0,f.isUndefined)(this.plugins[e])?(console.error("Failed to modify status for plugin "+e+". The plugin was not properly registered."),!1):(this.plugins[e].status="ready",!0):(console.error("Failed to modify status for plugin "+e+". Expected parameter `pluginName` to be a string."),!1)}_reloaded(e){return(0,f.isString)(e)?(0,f.isUndefined)(this.plugins[e])?(console.error("Failed to reload Content Analysis for plugin "+e+". The plugin was not properly registered."),!1):(this.refresh(),!0):(console.error("Failed to reload Content Analysis for "+e+". Expected parameter `pluginName` to be a string."),!1)}_registerModification(e,t,s,r){if(!(0,f.isString)(e))return console.error("Failed to register modification for plugin "+s+". Expected parameter `modification` to be a string."),!1;if(!(0,f.isFunction)(t))return console.error("Failed to register modification for plugin "+s+". Expected parameter `callable` to be a function."),!1;if(!(0,f.isString)(s))return console.error("Failed to register modification for plugin "+s+". Expected parameter `pluginName` to be a string."),!1;if(!1===this._validateOrigin(s))return console.error("Failed to register modification for plugin "+s+". The integration has not finished loading yet."),!1;const a={callable:t,origin:s,priority:(0,f.isNumber)(r)?r:10};return(0,f.isUndefined)(this.modifications[e])&&(this.modifications[e]=[]),this.modifications[e].push(a),!0}_registerAssessment(e,t,s,r){return(0,f.isString)(t)?(0,f.isObject)(s)?(0,f.isString)(r)?(t=r+"-"+t,e.addAssessment(t,s),!0):(console.error("Failed to register assessment for plugin "+r+". Expected parameter `pluginName` to be a string."),!1):(console.error("Failed to register assessment for plugin "+r+". Expected parameter `assessment` to be a function."),!1):(console.error("Failed to register test for plugin "+r+". Expected parameter `name` to be a string."),!1)}_applyModifications(e,t,s){let r=this.modifications[e];return!(0,f.isArray)(r)||r.length<1||(r=this._stripIllegalModifications(r),r.sort(((e,t)=>e.priority-t.priority)),(0,f.forEach)(r,(function(r){const a=r.callable(t,s);typeof a==typeof t?t=a:console.error("Modification with name "+e+" performed by plugin with name "+r.origin+" was ignored because the data that was returned by it was of a different type than the data we had passed it.")}))),t}_pollLoadingPlugins(e){e=(0,f.isUndefined)(e)?0:e,!0===this._allReady()?(this.loaded=!0,this.refresh()):e>=this.preloadThreshold?(this._pollTimeExceeded(),this.loaded=!0,this.refresh()):(e+=50,setTimeout(this._pollLoadingPlugins.bind(this,e),50))}_allReady(){return(0,f.reduce)(this.plugins,(function(e,t){return e&&"ready"===t.status}),!0)}_pollTimeExceeded(){(0,f.forEach)(this.plugins,(function(e,t){(0,f.isUndefined)(e.options)||"ready"===e.options.status||(console.error("Error: Plugin "+t+". did not finish loading in time."),delete this.plugins[t])}))}_stripIllegalModifications(e){return(0,f.forEach)(e,((t,s)=>{!1===this._validateOrigin(t.origin)&&delete e[s]})),e}_validateOrigin(e){return"ready"===this.plugins[e].status}_validateUniqueness(e){return(0,f.isUndefined)(this.plugins[e])}}let Pe=null;const Le=()=>{if(null===Pe){const e=(0,i.dispatch)("yoast-seo/editor").runAnalysis;Pe=window.YoastSEO.app&&window.YoastSEO.app.pluggable?window.YoastSEO.app.pluggable:new Ie(e)}return Pe},Ae=e=>Le()._ready(e),Oe=e=>Le()._reloaded(e),Fe=(e,t,s,r)=>Le()._registerModification(e,t,s,r),De=(e,t)=>Le()._registerPlugin(e,t),Me=(e,t,s)=>Le().loaded?Le()._applyModifications(e,t,s):t,Ne="yoastmark";function qe(e,t){return e._properties.position.startOffset>t.length||e._properties.position.endOffset>t.length}function Ue(e,t,s){const r=e.dom;let a=e.getContent();if(a=be.markers.removeMarks(a),(0,f.isEmpty)(s))return void e.setContent(a);a=s[0].hasPosition()?function(e,t){if(!t)return"";for(let s=(e=(0,f.orderBy)(e,(e=>e._properties.position.startOffset),["asc"])).length-1;s>=0;s--){const r=e[s];qe(r,t)||(t=r.applyWithPosition(t))}return t}(s,a):function(e,t,s,r){const{fieldsToMark:a,selectedHTML:n}=be.languageProcessing.getFieldsToMark(s,r);return(0,f.forEach)(s,(function(t){"acf_content"!==e.id&&(t._properties.marked=be.languageProcessing.normalizeHTML(t._properties.marked),t._properties.original=be.languageProcessing.normalizeHTML(t._properties.original)),a.length>0?n.forEach((e=>{const s=t.applyWithReplace(e);r=r.replace(e,s)})):r=t.applyWithReplace(r)})),r}(e,0,s,a),e.setContent(a),function(e){let t=e.getContent();t=t.replace(new RegExp("&lt;yoastmark.+?&gt;","g"),"").replace(new RegExp("&lt;/yoastmark&gt;","g"),""),e.setContent(t)}(e);const n=r.select(Ne);(0,f.forEach)(n,(function(e){e.setAttribute("data-mce-bogus","1")}))}function Be(e){return window.test=e,Ue.bind(null,e)}f.noop,f.noop,f.noop;const We="content";function Ye(e){if("undefined"==typeof tinyMCE||void 0===tinyMCE.editors||0===tinyMCE.editors.length)return!1;const t=tinyMCE.get(e);return null!==t&&!t.isHidden()}window.wp.annotations;const ze=function(e){return(0,f.uniq)((0,f.flatten)(e.map((e=>{if(!(0,f.isUndefined)(e.getFieldsToMark()))return e.getFieldsToMark()}))))},Ke=window.wp.richText,je=/(<([a-z]|\/)[^<>]+>)/gi,{htmlEntitiesRegex:He}=be.helpers.htmlEntities,Ve=e=>{let t=0;return(0,f.forEachRight)(e,(e=>{const[s]=e;let r=s.length;/^<\/?br/.test(s)&&(r-=1),t+=r})),t},Ge="<yoastmark class='yoast-text-mark'>",Ze="</yoastmark>",Qe='<yoastmark class="yoast-text-mark">';function Xe(e,t,s,r,a){const n=r.clientId,o=(0,Ke.create)({html:e,multilineTag:s.multilineTag,multilineWrapperTag:s.multilineWrapperTag}).text;return(0,f.flatMap)(a,(s=>{let a;return a=s.hasBlockPosition&&s.hasBlockPosition()?function(e,t,s,r,a){if(t===e.getBlockClientId()){let t=e.getBlockPositionStart(),n=e.getBlockPositionEnd();if(e.isMarkForFirstBlockSection()){const e=((e,t,s)=>{const r="yoast/faq-block"===s?'<strong class="schema-faq-question">':'<strong class="schema-how-to-step-name">';return{blockStartOffset:e-=r.length,blockEndOffset:t-=r.length}})(t,n,s);t=e.blockStartOffset,n=e.blockEndOffset}if(r.slice(t,n)===a.slice(t,n))return[{startOffset:t,endOffset:n}];const o=((e,t,s)=>{const r=s.slice(0,e),a=s.slice(0,t),n=((e,t,s,r)=>{const a=[...e.matchAll(je)];s-=Ve(a);const n=[...t.matchAll(je)];return{blockStartOffset:s,blockEndOffset:r-=Ve(n)}})(r,a,e,t),o=((e,t,s,r)=>{let a=[...e.matchAll(He)];return(0,f.forEachRight)(a,(e=>{const[,t]=e;s-=t.length})),a=[...t.matchAll(He)],(0,f.forEachRight)(a,(e=>{const[,t]=e;r-=t.length})),{blockStartOffset:s,blockEndOffset:r}})(r,a,e=n.blockStartOffset,t=n.blockEndOffset);return{blockStartOffset:e=o.blockStartOffset,blockEndOffset:t=o.blockEndOffset}})(t,n,r);return[{startOffset:o.blockStartOffset,endOffset:o.blockEndOffset}]}return[]}(s,n,r.name,e,o):function(e,t){const s=t.getOriginal().replace(/(<([^>]+)>)/gi,""),r=t.getMarked().replace(/(<(?!\/?yoastmark)[^>]+>)/gi,""),a=function(e,t,s=!0){const r=[];if(0===e.length)return r;let a,n=0;for(s||(t=t.toLowerCase(),e=e.toLowerCase());(a=e.indexOf(t,n))>-1;)r.push(a),n=a+t.length;return r}(e,s);if(0===a.length)return[];const n=function(e){let t=e.indexOf(Ge);const s=t>=0;s||(t=e.indexOf(Qe));let r=null;const a=[];for(;t>=0;){if(r=(e=s?e.replace(Ge,""):e.replace(Qe,"")).indexOf(Ze),r<t)return[];e=e.replace(Ze,""),a.push({startOffset:t,endOffset:r}),t=s?e.indexOf(Ge):e.indexOf(Qe),r=null}return a}(r),o=[];return n.forEach((e=>{a.forEach((r=>{const a=r+e.startOffset;let n=r+e.endOffset;0===e.startOffset&&e.endOffset===t.getOriginal().length&&(n=r+s.length),o.push({startOffset:a,endOffset:n})}))})),o}(o,s),a?a.map((e=>({...e,block:n,richTextIdentifier:t}))):[]}))}const Je=e=>e[0].toUpperCase()+e.slice(1),et=(e,t,s,r,a)=>(e=e.map((e=>{const n=`${e.id}-${a[0]}`,o=`${e.id}-${a[1]}`,i=Je(a[0]),l=Je(a[1]),c=e[`json${i}`],d=e[`json${l}`],{marksForFirstSection:p,marksForSecondSection:u}=((e,t)=>({marksForFirstSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&e.isMarkForFirstBlockSection():e)),marksForSecondSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&!e.isMarkForFirstBlockSection():e))}))(t,e),m=Xe(c,n,s,r,p),g=Xe(d,o,s,r,u);return m.concat(g)})),(0,f.flattenDeep)(e)),tt="yoast";let st=[];const rt={"core/paragraph":[{key:"content"}],"core/list":[{key:"values",multilineTag:"li",multilineWrapperTag:["ul","ol"]}],"core/list-item":[{key:"content"}],"core/heading":[{key:"content"}],"core/audio":[{key:"caption"}],"core/embed":[{key:"caption"}],"core/gallery":[{key:"caption"}],"core/image":[{key:"caption"}],"core/table":[{key:"caption"}],"core/video":[{key:"caption"}],"yoast/faq-block":[{key:"questions"}],"yoast/how-to-block":[{key:"steps"},{key:"jsonDescription"}]};function at(){const e=st.shift();e&&((0,i.dispatch)("core/annotations").__experimentalAddAnnotation(e),nt())}function nt(){(0,f.isFunction)(window.requestIdleCallback)?window.requestIdleCallback(at,{timeout:1e3}):setTimeout(at,150)}const ot=(e,t)=>{return(0,f.flatMap)((s=e.name,rt.hasOwnProperty(s)?rt[s]:[]),(s=>"yoast/faq-block"===e.name?((e,t,s)=>{const r=t.attributes[e.key];return 0===r.length?[]:et(r,s,e,t,["question","answer"])})(s,e,t):"yoast/how-to-block"===e.name?((e,t,s)=>{const r=t.attributes[e.key];if(r&&0===r.length)return[];const a=[];return"steps"===e.key&&a.push(et(r,s,e,t,["name","text"])),"jsonDescription"===e.key&&(s=s.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?!e.getBlockAttributeId():e)),a.push(Xe(r,"description",e,t,s))),(0,f.flattenDeep)(a)})(s,e,t):function(e,t,s){const r=e.key,a=((e,t)=>{const s=e.attributes[t];return"string"==typeof s?s:(s||"").toString()})(t,r);return Xe(a,r,e,t,s)}(s,e,t)));var s};function it(e,t){return(0,f.flatMap)(e,(e=>{const s=function(e){return e.innerBlocks.length>0}(e)?it(e.innerBlocks,t):[];return ot(e,t).concat(s)}))}function lt(e){st=[],(0,i.dispatch)("core/annotations").__experimentalRemoveAnnotationsBySource(tt);const t=ze(e);if(0===e.length)return;let s=(0,i.select)("core/block-editor").getBlocks();var r;t.length>0&&(s=s.filter((e=>t.some((t=>"core/"+t===e.name))))),r=it(s,e),st=r.map((e=>({blockClientId:e.block,source:tt,richTextIdentifier:e.richTextIdentifier,range:{start:e.startOffset,end:e.endOffset}}))),nt()}function ct(e,t){let s;Ye(We)&&((0,f.isUndefined)(s)&&(s=Be(tinyMCE.get(We))),s(e,t)),(0,i.select)("core/block-editor")&&(0,f.isFunction)((0,i.select)("core/block-editor").getBlocks)&&(0,i.select)("core/annotations")&&(0,f.isFunction)((0,i.dispatch)("core/annotations").__experimentalAddAnnotation)&&(function(e,t){tinyMCE.editors.map((e=>Be(e))).forEach((s=>s(e,t)))}(e,t),lt(t)),(0,l.doAction)("yoast.analysis.applyMarks",t)}function dt(){const e=(0,i.select)("yoast-seo/editor").isMarkingAvailable(),t=(0,i.select)("yoast-seo/editor").getMarkerPauseStatus();return!e||t?f.noop:ct}const pt=(0,f.debounce)((async function(e,t){const{text:s,...r}=t,a=new be.Paper(s,r);try{const t=await e.analyze(a),{seo:s,readability:r,inclusiveLanguage:n}=t.result;if(s){const e=s[""];e.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(a,e.marks)})),e.results=ve(e.results),(0,i.dispatch)("yoast-seo/editor").setSeoResultsForKeyword(a.getKeyword(),e.results),(0,i.dispatch)("yoast-seo/editor").setOverallSeoScore(e.score,a.getKeyword())}r&&(r.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(a,e.marks)})),r.results=ve(r.results),(0,i.dispatch)("yoast-seo/editor").setReadabilityResults(r.results),(0,i.dispatch)("yoast-seo/editor").setOverallReadabilityScore(r.score)),n&&(n.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(a,e.marks)})),n.results=ve(n.results),(0,i.dispatch)("yoast-seo/editor").setInclusiveLanguageResults(n.results),(0,i.dispatch)("yoast-seo/editor").setOverallInclusiveLanguageScore(n.score)),(0,l.doAction)("yoast.analysis.run",t,{paper:a})}catch(e){}}),500);function ut(){const{getAnalysisData:e,getEditorDataTitle:t,getIsFrontPage:s}=(0,i.select)("yoast-seo/editor");let r=e();r={...r,textTitle:t(),isFrontPage:s()};const a=function(e){return e.title=Me("data_page_title",e.title),e.title=Me("title",e.title),e.description=Me("data_meta_desc",e.description),e.text=Me("content",e.text),e}(r);return(0,l.applyFilters)("yoast.analysis.data",a)}const mt=()=>{const{getContentLocale:e}=(0,i.select)("yoast-seo/editor"),t=((...e)=>()=>e.map((e=>e())))(e,ut),s=(()=>{const{setEstimatedReadingTime:e,setFleschReadingEase:t,setTextLength:s}=(0,i.dispatch)("yoast-seo/editor"),r=(0,f.get)(window,"YoastSEO.analysis.worker.runResearch",f.noop);return()=>{const a=be.Paper.parse(ut());r("readingTime",a).then((t=>e(t.result))),r("getFleschReadingScore",a).then((e=>{e.result&&t(e.result)})),r("wordCountInText",a).then((e=>s(e.result)))}})();return setTimeout(s,1500),((e,t)=>{let s=e();return()=>{const r=e();(0,f.isEqual)(r,s)||(s=r,t((0,f.clone)(r)))}})(t,s)},gt=window.yoast.styledComponents;var ht=s.n(gt);const yt=window.yoast.externals.contexts,ft=({theme:e,location:t,children:s})=>(0,c.createElement)(yt.LocationProvider,{value:t},(0,c.createElement)(gt.ThemeProvider,{theme:e},s));ft.propTypes={theme:y().object.isRequired,location:y().oneOf(["sidebar","metabox","modal"]).isRequired,children:y().element.isRequired};const wt=ft,bt=[];let Et=null;class _t extends u.Component{constructor(e){super(e),this.state={registeredComponents:[]}}registerComponent(e,t){this.setState({registeredComponents:[...this.state.registeredComponents,{key:e,Component:t}]})}render(){return this.state.registeredComponents.map((({Component:e,key:t})=>(0,c.createElement)(e,{key:t})))}}function vt(e,t){null===Et||null===Et.current?bt.push({key:e,Component:t}):Et.current.registerComponent(e,t)}const kt=()=>!0;class xt extends $e.modules.hookUI.Base{constructor(e,t,s,r=kt){super(),this.command=e,this.id=t,this.callback=s,this.conditions=r}getCommand(){return this.command}getId(){return this.id}getConditions(...e){return this.conditions(...e)}apply(...e){return this.callback(...e)}}class St extends $e.modules.hookData.Base{constructor(e,t,s,r=kt){super(),this.command=e,this.id=t,this.callback=s,this.conditions=r.bind(this)}getCommand(){return this.command}getId(){return this.id}getConditions(...e){return this.conditions(...e)}apply(...e){return this.callback(...e)}}function Tt(e,t,s,r=kt){return $e.hooks.registerUIAfter(new xt(e,t,s,r))}function Rt(e,t,s,r=kt){return $e.hooks.registerUIBefore(new xt(e,t,s,r))}function Ct(e,t,s,r=kt){return $e.hooks.registerDataAfter(new St(e,t,s,r))}const It=e=>{return parseInt(null===(t=document.getElementById("post_ID"))||void 0===t?void 0:t.value,10)===e;var t},Pt=()=>{var e;return It(null===(e=elementor.documents.getCurrent())||void 0===e?void 0:e.id)},Lt=["yoast_wpseo_linkdex","yoast_wpseo_content_score","yoast_wpseo_inclusive_language_score","yoast_wpseo_words_for_linking","yoast_wpseo_estimated-reading-time-minutes"],At=["yoast_wpseo_focuskeywords","hidden_wpseo_focuskeywords"],Ot=e=>{let t="";e&&(t=(0,m.sprintf)(/* translators: %1$s translates to the Post Label in singular form */
(0,m.__)("Unfortunately we cannot save changes to your SEO settings while you are working on a draft of an already-published %1$s. If you want to save your SEO changes, make sure to click 'Update', or wait to make your SEO changes until you are ready to update the %1$s.","wordpress-seo"),wpseoAdminL10n.postTypeNameSingular.toLowerCase())),"draft"===elementor.settings.page.model.get("post_status")&&(t=""),(0,i.select)("yoast-seo/editor").getWarningMessage()!==t&&(0,i.dispatch)("yoast-seo/editor").setWarningMessage(t)},Ft=(e,t,s)=>null===t?null:(0,u.createPortal)(e,t,s),Dt=({id:e,children:t})=>{const s=(0,u.useRef)(document.getElementById(e)),[r,a]=(0,u.useState)((()=>Ft(t,s.current,e))),n=(0,u.useCallback)((()=>{const r=document.getElementById(e);r!==s.current&&(s.current=r,a(Ft(t,r,e)))}),[e,t]);return((e,t,s={childList:!0,subtree:!0})=>{(0,u.useEffect)((()=>{const r=new MutationObserver(t);return r.observe(e,s),()=>r.disconnect()}),[e,t])})(document.body,n),r},Mt=({error:e})=>{const t=(0,u.useCallback)((()=>{var e,t;return null===(e=window)||void 0===e||null===(t=e.location)||void 0===t?void 0:t.reload()}),[]),s=(0,i.useSelect)((e=>e("yoast-seo/editor").selectLink("https://yoa.st/elementor-error-support")),[]),r=(0,i.useSelect)((e=>e("yoast-seo/editor").getPreference("isRtl",!1)),[]);return(0,c.createElement)(g.Root,{context:{isRtl:r}},(0,c.createElement)(S,{error:e},(0,c.createElement)(S.VerticalButtons,{supportLink:s,handleRefreshClick:t})))};function Nt(){return(0,c.createElement)(g.ErrorBoundary,{FallbackComponent:Mt},(0,c.createElement)(p.Slot,{name:"YoastElementor"},(e=>{return void 0===(t=e).length?t:(0,f.flatten)(t).sort(((e,t)=>void 0===e.props.renderPriority?1:e.props.renderPriority-t.props.renderPriority));var t})))}Mt.propTypes={error:y().object.isRequired};const qt=window.wp.compose,Ut=c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{fillRule:"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",clipRule:"evenodd"}))})),$t=window.wp.url;var Bt=s(4530),Wt=s.n(Bt);const Yt=({className:e,...t})=>(0,c.createElement)("span",{className:Wt()("yst-grow yst-overflow-hidden yst-overflow-ellipsis yst-whitespace-nowrap yst-font-wp","yst-text-[#555] yst-text-base yst-leading-[normal] yst-subpixel-antialiased yst-text-start",e),...t});Yt.displayName="MetaboxButton.Text",Yt.propTypes={className:y().string},Yt.defaultProps={className:""};const zt=({className:e,...t})=>(0,c.createElement)("button",{type:"button",className:Wt()("yst-flex yst-items-center yst-w-full yst-pt-4 yst-pb-4 yst-pe-4 yst-ps-6 yst-space-x-2 rtl:yst-space-x-reverse","yst-border-t yst-border-t-[rgb(0,0,0,0.2)] yst-rounded-none yst-transition-all hover:yst-bg-[#f0f0f0]","focus:yst-outline focus:yst-outline-[1px] focus:yst-outline-[color:#0066cd] focus:-yst-outline-offset-1 focus:yst-shadow-[0_0_3px_rgba(8,74,103,0.8)]",e),...t});zt.propTypes={className:y().string},zt.defaultProps={className:""},zt.Text=Yt;const Kt=window.yoast.componentsNew,jt=e=>(0,c.createElement)("div",{className:"yoast components-panel__body"},(0,c.createElement)("h2",{className:"components-panel__body-title"},(0,c.createElement)("button",{id:e.id,onClick:e.onClick,className:"components-button components-panel__body-toggle",type:"button"},e.prefixIcon&&(0,c.createElement)("span",{className:"yoast-icon-span",style:{fill:`${e.prefixIcon&&e.prefixIcon.color||""}`}},(0,c.createElement)(Kt.SvgIcon,{size:e.prefixIcon.size,icon:e.prefixIcon.icon})),(0,c.createElement)("span",{className:"yoast-title-container"},(0,c.createElement)("div",{className:"yoast-title"},e.title),(0,c.createElement)("div",{className:"yoast-subtitle"},e.subTitle)),e.children,e.suffixIcon&&(0,c.createElement)(Kt.SvgIcon,{size:e.suffixIcon.size,icon:e.suffixIcon.icon}),e.SuffixHeroIcon))),Ht=jt;jt.propTypes={onClick:y().func.isRequired,title:y().string.isRequired,id:y().string,subTitle:y().string,suffixIcon:y().object,SuffixHeroIcon:y().object,prefixIcon:y().object,children:y().node},jt.defaultProps={id:"",suffixIcon:null,SuffixHeroIcon:null,prefixIcon:null,subTitle:"",children:null};const Vt=window.yoast.helpers,Gt=ht().div`
  padding: 25px 32px 32px;
  color: #303030;
`,Zt=ht().ul`
  margin: 0;
  padding: 0;

  li {
    list-style-image: var(--yoast-svg-icon-check);
    margin: 0.5rem 0 0 1.5rem;
    line-height: 1.4em;

    &::marker {
      font-size: 1.5rem;
    }
  }
`,Qt=ht().span`
  display: block;
  margin-top: 4px;
`,Xt=ht().h2`
  margin-top: 0;
  margin-bottom: 0.25rem;
  color: #303030;
  font-size: 0.8125rem;
  font-weight: 600;
`,Jt=ht().p`
  display: block;
  margin: 0.25rem 0 1rem 0 !important;
  max-width: 420px;
`,es=ht().hr`
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  border-top: 0;
  border-bottom: 1px solid #E2E8F0;
`,ts=ht().div`
  text-align: center;
`,ss=ht().a`
  width: 100%;
`,rs=(0,Vt.makeOutboundLink)(ss);class as extends u.Component{constructor(e){super(e),this.state={defaultPrice:"99"}}createBenefitsList(e){return e.length>0&&(0,c.createElement)(Zt,{role:"list"},e.map(((e,t)=>(0,c.createElement)("li",{key:`upsell-benefit-${t}`},w(e,{strong:(0,c.createElement)("strong",null)})))))}render(){const e=(0,i.select)("yoast-seo/editor").isPromotionActive("black-friday-2024-promotion"),{defaultPrice:t}=this.state,s=e?"69.30":null,r=s||t;return(0,c.createElement)(u.Fragment,null,e&&(0,c.createElement)("div",{className:"yst-flex  yst-items-center yst-text-lg yst-content-between yst-bg-black yst-text-amber-300 yst-h-9 yst-border-amber-300 yst-border-y yst-border-x-0 yst-border-solid yst-px-6"},(0,c.createElement)("div",{className:"yst-mx-auto"},(0,m.__)("30% OFF - BLACK FRIDAY","wordpress-seo"))),(0,c.createElement)(Gt,null,(0,c.createElement)(Xt,null,this.props.title),(0,c.createElement)(Jt,null,this.props.description),(0,c.createElement)(ts,null,(0,c.createElement)(rs,{...this.props.upsellButton},this.props.upsellButtonText,this.props.upsellButtonHasCaret&&(0,c.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"})),(0,c.createElement)("div",{className:"yst-text-slate-600 yst-my-4"},s&&(0,c.createElement)(u.Fragment,null,(0,c.createElement)("span",{className:"yst-text-slate-500 yst-line-through"},t)," "),(0,c.createElement)("span",{className:"yst-text-slate-900 yst-text-2xl yst-font-bold"},r)," ",(0,m.__)("$ USD / € EUR / £ GBP per year (ex. VAT)","wordpress-seo")),(0,c.createElement)(Qt,{id:this.props.upsellButton["aria-describedby"]},this.props.upsellButtonLabel)),(0,c.createElement)(es,null),(0,c.createElement)(Xt,null,this.props.benefitsTitle),this.createBenefitsList(this.props.benefits)))}}as.propTypes={title:y().node,benefits:y().array,benefitsTitle:y().node,description:y().node,upsellButton:y().object,upsellButtonText:y().string.isRequired,upsellButtonLabel:y().string,upsellButtonHasCaret:y().bool},as.defaultProps={title:null,description:null,benefits:[],benefitsTitle:null,upsellButton:{href:"",className:"button button-primary"},upsellButtonLabel:"",upsellButtonHasCaret:!0};const ns=as,os=ht().div`
	min-width: 600px;

	@media screen and ( max-width: 680px ) {
		min-width: 0;
		width: 86vw;
	}
`,is=ht().div`
	@media screen and ( min-width: 600px ) {
		max-width: 420px;
	}
`,ls=(ht()(Kt.Icon)`
	float: ${(0,Vt.getDirectionalStyle)("right","left")};
	margin: ${(0,Vt.getDirectionalStyle)("0 0 16px 16px","0 16px 16px 0")};

	&& {
		width: 150px;
		height: 150px;

		@media screen and ( max-width: 680px ) {
			width: 80px;
			height: 80px;
		}
	}
`,"yoast yoast-gutenberg-modal"),cs=e=>{const{title:t,className:s,showYoastIcon:r,additionalClassName:a,...n}=e,o=r?(0,c.createElement)("span",{className:"yoast-icon"}):null;return(0,c.createElement)(p.Modal,{title:t,className:`${s} ${a}`,icon:o,...n},e.children)};cs.propTypes={title:y().string,className:y().string,showYoastIcon:y().bool,children:y().oneOfType([y().node,y().arrayOf(y().node)]),additionalClassName:y().string},cs.defaultProps={title:"Yoast SEO",className:ls,showYoastIcon:!0,children:null,additionalClassName:""};const ds=cs,ps=()=>{const[e,,,t,s]=(0,g.useToggleState)(!1),{locationContext:r}=(0,yt.useRootContext)(),a=(0,g.useSvgAria)(),n=r.includes("sidebar"),o=r.includes("metabox"),i=wpseoAdminL10n[n?"shortlinks.upsell.sidebar.internal_linking_suggestions":"shortlinks.upsell.metabox.internal_linking_suggestions"];return(0,c.createElement)(c.Fragment,null,e&&(0,c.createElement)(ds,{title:(0,m.__)("Get internal linking suggestions","wordpress-seo"),onRequestClose:s,additionalClassName:"",id:"yoast-internal-linking-suggestions-upsell",className:`${ls} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,shouldCloseOnClickOutside:!0},(0,c.createElement)(is,null,(0,c.createElement)(ns,{title:(0,m.__)("Rank higher by connecting your content","wordpress-seo"),description:(0,m.sprintf)(/* translators: %s expands to Yoast SEO Premium. */
(0,m.__)("%s automatically suggests to what content you can link with easy drag-and-drop functionality, which is good for your SEO!","wordpress-seo"),"Yoast SEO Premium"),benefitsTitle:(0,m.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,m.__)("%s also gives you:","wordpress-seo"),"Yoast SEO Premium"),benefits:oe(),upsellButtonText:(0,m.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,m.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:(0,$t.addQueryArgs)(i,{context:r}),className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,m.__)("1 year free support and updates included!","wordpress-seo")}))),n&&(0,c.createElement)(Ht,{id:"yoast-internal-linking-suggestions-sidebar-modal-open-button",title:(0,m.__)("Internal linking suggestions","wordpress-seo"),onClick:t},(0,c.createElement)("div",{className:"yst-root"},(0,c.createElement)(g.Badge,{size:"small",variant:"upsell"},(0,c.createElement)(Ut,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...a})))),o&&(0,c.createElement)("div",{className:"yst-root"},(0,c.createElement)(zt,{id:"yoast-internal-linking-suggestions-metabox-modal-open-button",onClick:t},(0,c.createElement)(zt.Text,null,(0,m.__)("Internal linking suggestions","wordpress-seo")),(0,c.createElement)(g.Badge,{size:"small",variant:"upsell"},(0,c.createElement)(Ut,{className:"yst-w-2.5 yst-h-2.5 yst-me-1 yst-shrink-0",...a}),(0,c.createElement)("span",null,"Premium")))))},us=window.yoast.externals.components;function ms(){return(0,qt.createHigherOrderComponent)((function(e){return(0,qt.pure)((function(t){const s=(0,u.useContext)(yt.LocationContext);return(0,u.createElement)(e,{...t,location:s})}))}),"withLocation")}const gs=(0,qt.compose)([(0,i.withSelect)((e=>{const{isCornerstoneContent:t}=e("yoast-seo/editor");return{isCornerstone:t(),learnMoreUrl:wpseoAdminL10n["shortlinks.cornerstone_content_info"]}})),(0,i.withDispatch)((e=>{const{toggleCornerstoneContent:t}=e("yoast-seo/editor");return{onChange:t}})),ms()])(us.CollapsibleCornerstone),hs=(0,qt.compose)([(0,i.withSelect)(((e,t)=>{const{isAlertDismissed:s}=e(t.store||"yoast-seo/editor");return{isAlertDismissed:s(t.alertKey)}})),(0,i.withDispatch)(((e,t)=>{const{dismissAlert:s}=e(t.store||"yoast-seo/editor");return{onDismissed:()=>s(t.alertKey)}}))]),ys=({children:e,id:t,hasIcon:s=!0,title:r,image:a=null,isAlertDismissed:n,onDismissed:o})=>n?null:(0,c.createElement)("div",{id:t,className:"notice-yoast yoast is-dismissible yoast-webinar-dashboard yoast-general-page-notices"},(0,c.createElement)("div",{className:"notice-yoast__container"},(0,c.createElement)("div",null,(0,c.createElement)("div",{className:"notice-yoast__header"},s&&(0,c.createElement)("span",{className:"yoast-icon"}),(0,c.createElement)("h2",{className:"notice-yoast__header-heading yoast-notice-migrated-header"},r)),(0,c.createElement)("div",{className:"notice-yoast-content"},(0,c.createElement)("p",null,e))),a&&(0,c.createElement)(a,{height:"60"})),(0,c.createElement)("button",{type:"button",className:"notice-dismiss",onClick:o},(0,c.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,m.__)("Dismiss this notice.","wordpress-seo"))));ys.propTypes={children:y().node.isRequired,id:y().string.isRequired,hasIcon:y().bool,title:y().any.isRequired,image:y().elementType,isAlertDismissed:y().bool.isRequired,onDismissed:y().func.isRequired};const fs=hs(ys),ws=({store:e="yoast-seo/editor",image:t=null,title:s,promoId:r,alertKey:a,children:n,...o})=>(0,i.select)(e).isPromotionActive(r)&&(0,c.createElement)(fs,{alertKey:a,store:e,id:a,title:s,image:t,...o},n);ws.propTypes={store:y().string,image:y().elementType,title:y().any.isRequired,promoId:y().string.isRequired,alertKey:y().string.isRequired,children:y().node};const bs=({store:e="yoast-seo/editor",location:t="sidebar",...s})=>{const r=(0,i.useSelect)((t=>t(e).getIsPremium()),[e]),a=(0,i.useSelect)((t=>t(e).selectLinkParams()),[e]),n="sidebar"===t?(0,m.sprintf)(/* translators: %1$s expands to Yoast SEO Premium */
(0,m.__)("Now with 30%% OFF: %1$s","wordpress-seo"),"Yoast SEO Premium"):w((0,m.sprintf)(/* translators: %1$s expands to Yoast SEO Premium, %2$s expands to a link on yoast.com, %3$s expands to the anchor end tag. */
(0,m.__)("Now with 30%% OFF: %1$s %2$sBuy now!%3$s","wordpress-seo"),"Yoast SEO Premium","<a>","</a>"),{a:(0,c.createElement)("a",{href:(0,$t.addQueryArgs)("https://yoa.st/black-friday-sale",a),target:"_blank",rel:"noreferrer"})});return r?null:(0,c.createElement)(ws,{id:`black-friday-2024-promotion-${t}`,promoId:"black-friday-2024-promotion",alertKey:"black-friday-2024-promotion",store:e,title:n,...s},(0,c.createElement)("span",{className:"yoast-bf-sale-badge"},(0,m.__)("BLACK FRIDAY SALE","wordpress-seo")," "),"sidebar"===t&&(0,c.createElement)("a",{className:"yst-block yst--mb-[1em]",href:(0,$t.addQueryArgs)("https://yoa.st/black-friday-sale",a),target:"_blank",rel:"noreferrer"},(0,m.__)("Buy now!","wordpress-seo")))};bs.propTypes={store:y().string,location:y().oneOf(["sidebar","metabox"])};const Es="trustpilot-review-notification",_s="yoast-seo/editor";const vs=()=>{const e=(0,i.useSelect)((e=>e(_s).getIsPremium()),[]),t=(0,i.useSelect)((e=>e(_s).isAlertDismissed(Es)),[]),{overallScore:s}=(0,i.useSelect)((e=>e(_s).getResultsForFocusKeyword()),[]),{dismissAlert:r}=(0,i.useDispatch)(_s),a=(0,u.useCallback)((()=>r(Es)),[r]),[n,o]=(0,u.useState)(!1);return(0,u.useEffect)((()=>{var e,t;"good"===(null===(t=s,(0,f.isNil)(t)||(t/=10),e=function(e){switch(e){case"feedback":return{className:"na",screenReaderText:(0,m.__)("Not available","wordpress-seo"),screenReaderReadabilityText:(0,m.__)("Not available","wordpress-seo"),screenReaderInclusiveLanguageText:(0,m.__)("Not available","wordpress-seo")};case"bad":return{className:"bad",screenReaderText:(0,m.__)("Needs improvement","wordpress-seo"),screenReaderReadabilityText:(0,m.__)("Needs improvement","wordpress-seo"),screenReaderInclusiveLanguageText:(0,m.__)("Needs improvement","wordpress-seo")};case"ok":return{className:"ok",screenReaderText:(0,m.__)("OK SEO score","wordpress-seo"),screenReaderReadabilityText:(0,m.__)("OK","wordpress-seo"),screenReaderInclusiveLanguageText:(0,m.__)("Potentially non-inclusive","wordpress-seo")};case"good":return{className:"good",screenReaderText:(0,m.__)("Good SEO score","wordpress-seo"),screenReaderReadabilityText:(0,m.__)("Good","wordpress-seo"),screenReaderInclusiveLanguageText:(0,m.__)("Good","wordpress-seo")};default:return{className:"loading",screenReaderText:"",screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""}}}(be.interpreters.scoreToRating(t)))||void 0===e?void 0:e.className)&&o(!0)}),[s]),{shouldShow:!e&&!t&&n,dismiss:a}},ks=(0,Vt.makeOutboundLink)(),xs=()=>{const{shouldShow:e,dismiss:t}=vs(),{locationContext:s}=(0,yt.useRootContext)(),r=(0,i.useSelect)((e=>e(_s).selectLink("https://yoa.st/trustpilot-review",{context:s})),[s]);return(0,c.createElement)(ys,{alertKey:Es,store:_s,id:Es,title:(0,m.__)("Show Yoast SEO some love!","wordpress-seo"),hasIcon:!1,isAlertDismissed:!e,onDismissed:t},(0,m.__)("Happy with the plugin?","wordpress-seo")," ",(0,c.createElement)(ks,{href:r,rel:"noopener noreferrer"},(0,m.__)("Leave a quick review","wordpress-seo")),".")};var Ss,Ts,Rs,Cs,Is,Ps,Ls,As,Os,Fs,Ds,Ms,Ns,qs,Us,$s,Bs,Ws,Ys,zs,Ks,js,Hs,Vs,Gs,Zs,Qs,Xs,Js,er,tr,sr,rr,ar,nr,or,ir,lr,cr,dr,pr,ur,mr,gr,hr,yr,fr;function wr(){return wr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},wr.apply(this,arguments)}const br=e=>c.createElement("svg",wr({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 448 360"},e),Ss||(Ss=c.createElement("circle",{cx:226,cy:211,r:149,fill:"#f0ecf0"})),Ts||(Ts=c.createElement("path",{fill:"#fbd2a6",d:"M173.53 189.38s-35.47-5.3-41.78-11c-9.39-24.93-29.61-48-35.47-66.21-.71-2.24 3.72-11.39 3.53-15.41s-5.34-11.64-5.23-14-.09-15.27-.09-15.27l-4.75-.72s-5.13 6.07-3.56 9.87c-1.73-4.19 4.3 7.93.5 9.35 0 0-6-5.94-11.76-8.27s-19.57-3.65-19.57-3.65L43.19 73l-4.42.6L31 69.7l-2.85 5.12 7.53 5.29L40.86 92l17.19 10.2 10.2 10.56 9.86 3.56s26.49 79.67 45 92c17 11.33 37.23 15.92 37.23 15.92z"})),Rs||(Rs=c.createElement("path",{fill:"#a4286a",d:"M270.52 345.13c2.76-14.59 15.94-35.73 30.24-54.58 16.22-21.39 14-79.66-33.19-91.46-17.3-4.32-52.25-1-59.85-3.41C186.54 189 170 187 168 190.17c-5 10.51-7.73 27.81-5.51 36.26 1.18 4.73 3.54 5.91 20.49 13.4-5.12 15-16.35 26.3-22.86 37s7.88 27.2 7.1 33.51c-.48 3.8-4.26 21.13-7.18 34.25a149.47 149.47 0 0 0 110.3 8.66 25.66 25.66 0 0 1 .18-8.12z"})),Cs||(Cs=c.createElement("path",{fill:"#9a5815",d:"M206.76 66.43c-5 14.4-1.42 25.67-3.93 40.74-10 60.34-24.08 43.92-31.44 93.6 7.24-14.19 14.32-15.82 20.63-23.11-.83 3.09-10.25 13.75-8.05 34.81 9.85-8.51 6.35-8.75 11.86-8.54.36 3.25 3.53 3.22-3.59 10.53 2.52.69 17.42-14.32 20.16-12.66s0 5.72-6 7.76c2.15 2.2 30.47-3.87 43.81-14.71 4.93-4 10-13.16 13.38-18.2 7.17-10.62 12.38-24.77 17.71-36.6 8.94-19.87 15.09-39.34 16.11-61.31.53-10.44-3.41-18.44-4.41-28.86-2.57-27.8-67.63-37.26-86.24 16.55z"})),Is||(Is=c.createElement("path",{fill:"#efb17c",d:"M277.74 179.06c.62-.79 1.24-1.59 1.84-2.39-.85 2.59-1.52 3.73-1.84 2.39z"})),Ps||(Ps=c.createElement("path",{fill:"#fbd2a6",d:"M216.1 206.72c3.69-5.42 8.28-3.35 15.57-8.28 3.76-3.06 1.57-9.46 1.77-11.82 18.25 4.56 37.38-1.18 49.07-16 .62 5.16-2.77 22.27-.2 27 4.73 8.67 13.4 18.92 13.4 18.92-35.47-2.76-63.45 39-89.86 44.54 5.52-28.74-2.36-35.84 10.25-54.36z"})),Ls||(Ls=c.createElement("path",{fill:"#f6b488",d:"m235.21 167.9 53.21-25.23s-3.65 24-6.5 32.72c-64.05 62.66-46.47-7.33-46.71-7.49z"})),As||(As=c.createElement("path",{fill:"#fbd2a6",d:"M226.86 50.64C215 59.31 206.37 93.21 204 95.57c-19.46 19.47-3.59 41.39-3.94 51.24-.2 5.52-4.14 25.42 5.72 29.36 22.22 8.89 60-3.48 67.19-12.61 13.28-16.75 40.89-94.78 17.74-108.19-7.92-4.58-42.78-20.18-63.85-4.73z"})),Os||(Os=c.createElement("path",{fill:"#e5766c",d:"M243.69 143.66c-10.7-6.16-8.56-6.73-19.76-12.71-3.86-2.07-3.94.64-6.32 0-2.91-.79-1.39-2.74-5.37-3.48-6.52-1.21-3.67 3.63-3.15 6 1.32 6.15-8.17 17.3 3.26 21.42 12.65 4.55 21.38-9.41 31.34-11.23z"})),Fs||(Fs=c.createElement("path",{fill:"#fff",d:"M240.68 143.9c-11.49-5.53-11.65-8.17-24.64-11.69-8.6-2.32-5.53 1-5.69 4.42-.2 4.16-1.26 9.87 4.9 12.66 9 4.09 18.16-6.02 25.43-5.39zm.7-40.9c-.16 1.26-.06 4.9 5.46 8.25 11.43-4.73 16.36-2.56 17-3.33 1.48-1.76-2-8.87-7.88-9.85-5.58-.94-14.14 1.24-14.58 4.93z"})),Ds||(Ds=c.createElement("path",{fill:"#000001",d:"M263.53 108.19c-4.32-4.33-6.85-6.24-12.26-8.21-2.77-1-6.18.18-8.65 1.67a3.65 3.65 0 0 0-1.24 1.23h-.12a3.73 3.73 0 0 1 1-1.52 12.53 12.53 0 0 1 11.93-3c4.73 1 9.43 4.63 9.42 9.82z"})),Ms||(Ms=c.createElement("circle",{cx:254.13,cy:104.05,r:4.19,fill:"#000001"})),Ns||(Ns=c.createElement("path",{fill:"#fff",d:"M225.26 99.22c-.29 1-6.6 3.45-10.92 1.48-1.15-3.24-5-6.43-5.25-6.71-.5-2.86 5.55-8 10.06-6.3a10.21 10.21 0 0 1 6.11 11.53z"})),qs||(qs=c.createElement("path",{fill:"#000001",d:"M209.29 94.21c-.19-2.34 1.84-4.1 3.65-5.2 7-3.87 13.18 3 12.43 10h-.12c-.14-4-2.38-8.44-6.47-9.11a3.19 3.19 0 0 0-2.42.31c-1.37.85-2.38 2-3.89 2.56-1 .45-1.92.42-3 1.4h-.22z"})),Us||(Us=c.createElement("circle",{cx:219.55,cy:95.28,r:4,fill:"#000001"})),$s||($s=c.createElement("path",{fill:"#efb17c",d:"M218.66 120.27a27.32 27.32 0 0 0 4.54 3.45c-2.29-.72-4.28-.69-6.32-2.27-2.53-2-3.39-5.16-.73-7.72 10.24-9.82 12.56-13.82 14.77-24.42-1 12.37-6 17.77-10.63 23.18-2.53 2.97-4.68 5.06-1.63 7.78z"})),Bs||(Bs=c.createElement("path",{fill:"#a57c52",d:"M231.22 69.91c-.67-3.41-8.78-2.83-11.06-1.93-3.48 1.39-6.08 5.22-7.13 8.53 2.9-4.3 6.74-8.12 12.46-6 1.16.42 3.18 2.35 4.48 1.85s1.03-2.2 1.25-2.45zm32.16 8.56c-2.75-1.66-12.24-5.08-12.18.82 2.56.24 5-.19 7.64.95 11.22 4.76 12.77 17.61 12.85 17.86.2-.53.1 1.26.23.7-.02.2.95-12.12-8.54-20.33z"})),Ws||(Ws=c.createElement("path",{fill:"#fbd2a6",d:"M53.43 250.73c6.29 0-.6-.17 7.34 0 1.89.05-2.38-.7 0-.69 4.54-4.2 12.48-.74 20.6-2.45 4.55.35 3.93 1.35 5.59 4.19 4.89 8.38 4.78 14.21 14 19.56 16.42 8.38 66 12.92 88.49 18.86 5.52.83 42.64-20.15 61-23.75 6.51 10.74 11.46 28.68 8.39 34.93-6.54 13.3-57.07 25.4-75.91 25.15C156.47 326.18 94 294 92.2 293c-.94-.57.7-.7-7.68 0s-10.15.72-17.47-1.4c-3-.87-4.61-1.33-6.33-3.54-2 .22-3.39.2-4.78-1-3.15-2.74-4.84-6.61-2.73-10.06h-.12c-3.35-2.48-6.54-7.69-3.08-11.72 1-1.18 6.06-1.94 7.77-2.28-1.58-.29-6.37.19-7.49-.72-3.06-2.5-4.96-11.55 3.14-11.55z"})),Ys||(Ys=c.createElement("path",{fill:"#a4286a",d:"M303.22 237.52c-9.87-11.88-41.59 8.19-47.8 12.34s-14.89 17.95-14.89 17.95c6 9.43 8.36 31 5.65 46.34l30.51-3s18-15.62 22.59-28.7 6.3-42.54 6.3-42.54"})),zs||(zs=c.createElement("path",{fill:"#cb9833",d:"M278.63 31.67c-6.08 0-22.91 4.07-22.93 12.91 0 11 47.9 38.38 16.14 85.85 10.21-.79 10.79-8.12 14.92-14.93-3.66 77-49.38 93.58-40.51 142.25 7.68-25.81 20.3-11.62 38.13-33.84 3.45 4.88 9 18.28-9.46 33.78 50-31.26 57.31-56.6 51.92-95C319.93 113.53 348.7 42 278.63 31.67z"})),Ks||(Ks=c.createElement("path",{fill:"#fbd2a6",d:"M283.64 126.83c-2.42 9.67-8 15.76-1.48 16.46A21.26 21.26 0 0 0 302 132.6c5.17-8.52 3.93-16.44-2.46-18s-13.48 2.56-15.9 12.23z"})),js||(js=c.createElement("path",{fill:"#efb17c",d:"M38 73.45c1.92 2 4.25 9.21 6.32 10.91 2.25 1.85 5.71 2.12 8.1 4.45 3.66-2 6-8.72 10-9.31-2.59 1.31-4.42 3.5-6.93 4.88-1.42.8-3 1.31-4.38 2.25-2.16-1.46-4.27-1.77-6.26-3.38-2.52-2.02-5.31-8-6.85-9.8z"})),Hs||(Hs=c.createElement("path",{fill:"#efb17c",d:"M39 74.4c4.83 1.1 12.52 6.44 15.89 10-3.22-1.34-14.73-6.15-15.89-10zm.62-1.5c6.71-.79 18 1.54 23.29 5.9-3.85-.2-5.42-1.48-9-2.94-4.08-1.69-8.83-2.03-14.29-2.96zm46.43 14.58c-3.72-1.32-10.52-1.13-13.22 3.52 2-1.16 1.84-2.11 4.18-1.72-3.81-4.15 8.16-.74 11.6-.24m-2.78 13.15c.56-3.29-8-7.81-10.58-9.17-6.25-3.29-12.16 1.36-19.33-4.53 5.94 6.1 14.23 2.5 19.55 5.76 3.06 1.88 8.65 6.09 9.35 9.38-.23-.4 1.29-1.44 1.01-1.44z"})),Vs||(Vs=c.createElement("circle",{cx:38.13,cy:30.03,r:3.14,fill:"#b89ac8"})),Gs||(Gs=c.createElement("circle",{cx:60.26,cy:39.96,r:3.14,fill:"#e31e0c"})),Zs||(Zs=c.createElement("circle",{cx:50.29,cy:25.63,r:3.14,fill:"#3baa45"})),Qs||(Qs=c.createElement("circle",{cx:22.19,cy:19.21,r:3.14,fill:"#2ca9e1"})),Xs||(Xs=c.createElement("circle",{cx:22.19,cy:30.03,r:3.14,fill:"#e31e0c"})),Js||(Js=c.createElement("circle",{cx:26.86,cy:8.28,r:3.14,fill:"#3baa45"})),er||(er=c.createElement("circle",{cx:49.32,cy:39.99,r:3.14,fill:"#e31e0c"})),tr||(tr=c.createElement("circle",{cx:63.86,cy:59.52,r:3.14,fill:"#f8ad39"})),sr||(sr=c.createElement("circle",{cx:50.88,cy:50.72,r:3.14,fill:"#3baa45"})),rr||(rr=c.createElement("circle",{cx:63.47,cy:76.17,r:3.14,fill:"#e31e0c"})),ar||(ar=c.createElement("circle",{cx:38.34,cy:14.83,r:3.14,fill:"#2ca9e1"})),nr||(nr=c.createElement("circle",{cx:44.44,cy:5.92,r:3.14,fill:"#f8ad39"})),or||(or=c.createElement("circle",{cx:57.42,cy:10.24,r:3.14,fill:"#e31e0c"})),ir||(ir=c.createElement("circle",{cx:66.81,cy:12.4,r:3.14,fill:"#2ca9e1"})),lr||(lr=c.createElement("circle",{cx:77.95,cy:5.14,r:3.14,fill:"#b89ac8"})),cr||(cr=c.createElement("circle",{cx:77.95,cy:30.34,r:3.14,fill:"#e31e0c"})),dr||(dr=c.createElement("circle",{cx:80.97,cy:16.55,r:3.14,fill:"#f8ad39"})),pr||(pr=c.createElement("circle",{cx:62.96,cy:27.27,r:3.14,fill:"#3baa45"})),ur||(ur=c.createElement("circle",{cx:75.36,cy:48.67,r:3.14,fill:"#2ca9e1"})),mr||(mr=c.createElement("circle",{cx:76.11,cy:65.31,r:3.14,fill:"#3baa45"})),gr||(gr=c.createElement("path",{fill:"#71b026",d:"M78.58 178.43C54.36 167.26 32 198.93 5 198.93c19.56 20.49 63.53 1.52 69 15.5 1.48-14.01 4.11-30.9 4.58-36z"})),hr||(hr=c.createElement("path",{fill:"#074a67",d:"M67.75 251.08c0-4.65 10.13-72.65 10.13-72.65h2.8l-9.09 72.3z"})),yr||(yr=c.createElement("ellipse",{cx:255.38,cy:103.18,fill:"#fff",rx:1.84,ry:1.77})),fr||(fr=c.createElement("ellipse",{cx:221.24,cy:94.75,fill:"#fff",rx:1.84,ry:1.77}))),Er=({store:e="yoast-seo/editor",image:t=br,url:s,...r})=>(0,i.useSelect)((t=>t(e).getIsPremium()))?null:(0,c.createElement)(fs,{alertKey:"webinar-promo-notification",store:e,id:"webinar-promo-notification",title:(0,m.__)("Join our FREE webinar for SEO success","wordpress-seo"),image:t,url:s,...r},(0,m.__)("Feeling lost when it comes to optimizing your site for the search engines? Join our FREE webinar to gain the confidence that you need in order to start optimizing like a pro! You'll obtain the knowledge and tools to start effectively implementing SEO.","wordpress-seo")," ",(0,c.createElement)("a",{href:s,target:"_blank",rel:"noreferrer"},(0,m.__)("Sign up today!","wordpress-seo")));Er.propTypes={store:y().string,image:y().elementType,url:y().string.isRequired};const _r=Er,vr=(e="yoast-seo/editor")=>{const t=(0,i.select)(e).isPromotionActive("black-friday-2024-promotion"),s=(0,i.select)(e).isAlertDismissed("black-friday-2024-promotion");return t?s:((e="yoast-seo/editor")=>{const t=(0,i.select)(e).isPromotionActive("black-friday-2023-checklist"),s=(0,i.select)(e).isAlertDismissed("black-friday-2023-sidebar-checklist");return!t||s})(e)},kr=c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{d:"M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z"}))})),xr=(e=null)=>(0,c.useMemo)((()=>{const t={role:"img","aria-hidden":"true"};return null!==e&&(t.focusable=e?"true":"false"),t}),[e]),Sr=({id:e,postTypeName:t,children:s,title:r,isOpen:a,close:n,open:o,shouldCloseOnClickOutside:i,showChangesWarning:l,SuffixHeroIcon:d})=>(0,c.createElement)(u.Fragment,null,a&&(0,c.createElement)(yt.LocationProvider,{value:"modal"},(0,c.createElement)(ds,{title:r,onRequestClose:n,additionalClassName:"yoast-collapsible-modal yoast-post-settings-modal",id:"id",shouldCloseOnClickOutside:i},(0,c.createElement)("div",{className:"yoast-content-container"},(0,c.createElement)("div",{className:"yoast-modal-content"},s)),(0,c.createElement)("div",{className:"yoast-notice-container"},(0,c.createElement)("hr",null),(0,c.createElement)("div",{className:"yoast-button-container"},l&&(0,c.createElement)("p",null,/* Translators: %s translates to the Post Label in singular form */
(0,m.sprintf)((0,m.__)("Make sure to save your %s for changes to take effect","wordpress-seo"),t)),(0,c.createElement)("button",{className:"yoast-button yoast-button--primary yoast-button--post-settings-modal",type:"button",onClick:n},/* Translators: %s translates to the Post Label in singular form */
(0,m.sprintf)((0,m.__)("Return to your %s","wordpress-seo"),t)))))),(0,c.createElement)(Ht,{id:e+"-open-button",title:r,SuffixHeroIcon:d,suffixIcon:d?null:{size:"20px",icon:"pencil-square"},onClick:o}));Sr.propTypes={id:y().string.isRequired,postTypeName:y().string.isRequired,children:y().oneOfType([y().node,y().arrayOf(y().node)]).isRequired,title:y().string.isRequired,isOpen:y().bool.isRequired,open:y().func.isRequired,close:y().func.isRequired,shouldCloseOnClickOutside:y().bool,showChangesWarning:y().bool,SuffixHeroIcon:y().object},Sr.defaultProps={shouldCloseOnClickOutside:!0,showChangesWarning:!0};const Tr=Sr,Rr=(0,qt.compose)([(0,i.withSelect)(((e,t)=>{const{getPostOrPageString:s,getIsModalOpen:r}=e("yoast-seo/editor");return{postTypeName:s(),isOpen:r(t.id)}})),(0,i.withDispatch)(((e,t)=>{const{openEditorModal:s,closeEditorModal:r}=e("yoast-seo/editor");return{open:()=>s(t.id),close:r}}))])(Tr),Cr=()=>{const e=(0,i.useSelect)((e=>e("yoast-seo/editor").getEstimatedReadingTime()),[]),t=(0,u.useMemo)((()=>(0,f.get)(window,"wpseoAdminL10n.shortlinks-insights-estimated_reading_time","")),[]);return(0,c.createElement)(Kt.InsightsCard,{amount:e,unit:(0,m._n)("minute","minutes",e,"wordpress-seo"),title:(0,m.__)("Reading time","wordpress-seo"),linkTo:t
/* translators: Hidden accessibility text. */,linkText:(0,m.__)("Learn more about reading time","wordpress-seo")})},Ir=(0,Vt.makeOutboundLink)();function Pr(e,t,s){const r=function(e){switch(e){case be.DIFFICULTY.FAIRLY_DIFFICULT:case be.DIFFICULTY.DIFFICULT:case be.DIFFICULTY.VERY_DIFFICULT:return(0,m.__)("Try to make shorter sentences, using less difficult words to improve readability","wordpress-seo");case be.DIFFICULTY.NO_DATA:return(0,m.__)("Continue writing to get insight into the readability of your text!","wordpress-seo");default:return(0,m.__)("Good job!","wordpress-seo")}}(t);return(0,c.createElement)("span",null,function(e,t){return-1===e?(0,m.__)("Your text should be slightly longer to calculate your Flesch reading ease score.","wordpress-seo"):(0,m.sprintf)(
/* Translators: %1$s expands to the numeric Flesch reading ease score,
  %2$s expands to the easiness of reading (e.g. 'easy' or 'very difficult') */
(0,m.__)("The copy scores %1$s in the test, which is considered %2$s to read.","wordpress-seo"),e,function(e){switch(e){case be.DIFFICULTY.NO_DATA:return(0,m.__)("no data","wordpress-seo");case be.DIFFICULTY.VERY_EASY:return(0,m.__)("very easy","wordpress-seo");case be.DIFFICULTY.EASY:return(0,m.__)("easy","wordpress-seo");case be.DIFFICULTY.FAIRLY_EASY:return(0,m.__)("fairly easy","wordpress-seo");case be.DIFFICULTY.OKAY:return(0,m.__)("okay","wordpress-seo");case be.DIFFICULTY.FAIRLY_DIFFICULT:return(0,m.__)("fairly difficult","wordpress-seo");case be.DIFFICULTY.DIFFICULT:return(0,m.__)("difficult","wordpress-seo");case be.DIFFICULTY.VERY_DIFFICULT:return(0,m.__)("very difficult","wordpress-seo")}}(t))}(e,t)," ",t>=be.DIFFICULTY.FAIRLY_DIFFICULT?(0,c.createElement)(Ir,{href:s},r+"."):r)}const Lr=()=>{let e=(0,i.useSelect)((e=>e("yoast-seo/editor").getFleschReadingEaseScore()),[]);const t=(0,u.useMemo)((()=>(0,f.get)(window,"wpseoAdminL10n.shortlinks-insights-flesch_reading_ease","")),[]),s=(0,i.useSelect)((e=>e("yoast-seo/editor").getFleschReadingEaseDifficulty()),[e]),r=(0,u.useMemo)((()=>{const t=(0,f.get)(window,"wpseoAdminL10n.shortlinks-insights-flesch_reading_ease_article","");return Pr(e,s,t)}),[e,s]);return-1===e&&(e="?"),(0,c.createElement)(Kt.InsightsCard,{amount:e,unit:(0,m.__)("out of 100","wordpress-seo"),title:(0,m.__)("Flesch reading ease","wordpress-seo"),linkTo:t
/* translators: Hidden accessibility text. */,linkText:(0,m.__)("Learn more about Flesch reading ease","wordpress-seo"),description:r})},Ar=({data:e,itemScreenReaderText:t,className:s,...r})=>{const a=(0,u.useMemo)((()=>{var t,s;return null!==(t=null===(s=(0,f.maxBy)(e,"number"))||void 0===s?void 0:s.number)&&void 0!==t?t:0}),[e]);return(0,c.createElement)("ul",{className:Wt()("yoast-data-model",s),...r},e.map((({name:e,number:s})=>(0,c.createElement)("li",{key:`${e}_dataItem`,style:{"--yoast-width":s/a*100+"%"}},e,(0,c.createElement)("span",null,s),t&&(0,c.createElement)("span",{className:"screen-reader-text"},(0,m.sprintf)(t,s))))))};Ar.propTypes={data:y().arrayOf(y().shape({name:y().string.isRequired,number:y().number.isRequired})),itemScreenReaderText:y().string,className:y().string},Ar.defaultProps={data:[],itemScreenReaderText:"",className:""};const Or=Ar,Fr=(0,Vt.makeOutboundLink)(),Dr=({location:e})=>{const t=(0,i.useSelect)((e=>{var t,s;return null===(t=null===(s=e("yoast-seo-premium/editor"))||void 0===s?void 0:s.getPreference("isProminentWordsAvailable",!1))||void 0===t||t}),[]),s=(0,i.useSelect)((e=>e("yoast-seo/editor").getPreference("shouldUpsell",!1)),[]),r=(0,u.useMemo)((()=>(0,f.get)(window,`wpseoAdminL10n.shortlinks-insights-upsell-${e}-prominent_words`,"")),[e]),a=(0,u.useMemo)((()=>{const e=(0,f.get)(window,"wpseoAdminL10n.shortlinks-insights-keyword_research_link","");return w((0,m.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags.
(0,m.__)("Read our %1$sultimate guide to keyword research%2$s to learn more about keyword research and keyword strategy.","wordpress-seo"),"<a>","</a>"),{a:(0,c.createElement)(Fr,{href:e})})}),[]),n=(0,u.useMemo)((()=>w((0,m.sprintf)(
// translators: %1$s expands to a starting `b` tag, %1$s expands to a closing `b` tag and %3$s expands to `Yoast SEO Premium`.
(0,m.__)("With %1$s%3$s%2$s, this section will show you which words occur most often in your text. By checking these prominent words against your intended keyword(s), you'll know how to edit your text to be more focused.","wordpress-seo"),"<b>","</b>","Yoast SEO Premium"),{b:(0,c.createElement)("b",null)})),[]),o=(0,i.useSelect)((e=>{var t,s;return null!==(t=null===(s=e("yoast-seo-premium/editor"))||void 0===s?void 0:s.getProminentWords())&&void 0!==t?t:[]}),[]),l=(0,u.useMemo)((()=>{const e=(0,m.sprintf)(
// translators: %1$s expands to Yoast SEO Premium.
(0,m.__)("Get %s to enjoy the benefits of prominent words","wordpress-seo"),"Yoast SEO Premium").split(/\s+/);return e.map(((t,s)=>({name:t,number:e.length-s})))}),[]),d=(0,u.useMemo)((()=>s?l:o.map((({word:e,occurrence:t})=>({name:e,number:t})))),[o,l]);if(!t)return null;const{locationContext:p}=(0,yt.useRootContext)();return(0,c.createElement)("div",{className:"yoast-prominent-words"},(0,c.createElement)("div",{className:"yoast-field-group__title"},(0,c.createElement)("b",null,(0,m.__)("Prominent words","wordpress-seo"))),!s&&(0,c.createElement)("p",null,0===d.length?(0,m.__)("Once you add a bit more copy, we'll give you a list of words that occur the most in the content. These give an indication of what your content focuses on.","wordpress-seo"):(0,m.__)("The following words occur the most in the content. These give an indication of what your content focuses on. If the words differ a lot from your topic, you might want to rewrite your content accordingly.","wordpress-seo")),s&&(0,c.createElement)("p",null,n),s&&(0,c.createElement)(Fr,{href:(0,$t.addQueryArgs)(r,{context:p}),"data-action":"load-nfd-ctb","data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2",className:"yoast-button yoast-button-upsell"},(0,m.sprintf)(
// translators: %s expands to `Premium` (part of add-on name).
(0,m.__)("Unlock with %s","wordpress-seo"),"Premium"),(0,c.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"})),(0,c.createElement)("p",null,a),(0,c.createElement)(Or,{data:d,itemScreenReaderText:/* translators: Hidden accessibility text; %d expands to the number of occurrences. */
(0,m.__)("%d occurrences","wordpress-seo"),"aria-label":(0,m.__)("Prominent words","wordpress-seo"),className:s?"yoast-data-model--upsell":null}))};Dr.propTypes={location:y().string.isRequired};const Mr=Dr,Nr=(0,Vt.makeOutboundLink)(),qr=({location:e})=>{const t=(0,u.useMemo)((()=>(0,f.get)(window,`wpseoAdminL10n.shortlinks-insights-upsell-${e}-text_formality`,"")),[e]),s=(0,u.useMemo)((()=>w((0,m.sprintf)(
// Translators: %1$s expands to a starting `b` tag, %2$s expands to a closing `b` tag and %3$s expands to `Yoast SEO Premium`.
(0,m.__)("%1$s%3$s%2$s will help you assess the formality level of your text.","wordpress-seo"),"<b>","</b>","Yoast SEO Premium"),{b:(0,c.createElement)("b",null)})),[]);return(0,c.createElement)(u.Fragment,null,(0,c.createElement)("div",null,(0,c.createElement)("p",null,s),(0,c.createElement)(Nr,{href:t,className:"yoast-button yoast-button-upsell"},(0,m.sprintf)(
// Translators: %s expands to `Premium` (part of add-on name).
(0,m.__)("Unlock with %s","wordpress-seo"),"Premium"),(0,c.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"}))))};qr.propTypes={location:y().string.isRequired};const Ur=qr,$r=({location:e,name:t})=>{const s=(0,i.useSelect)((e=>e("yoast-seo/editor").isFormalitySupported()),[]),r=ke().isPremium,a=r?(0,f.get)(window,"wpseoAdminL10n.shortlinks-insights-text_formality_info_premium",""):(0,f.get)(window,"wpseoAdminL10n.shortlinks-insights-text_formality_info_free",""),n=(0,m.__)("Read more about text formality.","wordpress-seo");return s?(0,c.createElement)("div",{className:"yoast-text-formality"},(0,c.createElement)("div",{className:"yoast-field-group__title"},(0,c.createElement)("b",null,(0,m.__)("Text formality","wordpress-seo")),(0,c.createElement)(Kt.HelpIcon,{linkTo:a,linkText:n})),r?(0,c.createElement)(p.Slot,{name:t}):(0,c.createElement)(Ur,{location:e})):null};$r.propTypes={location:y().string.isRequired,name:y().string.isRequired};const Br=$r,Wr=()=>{const e=(0,i.useSelect)((e=>e("yoast-seo/editor").getTextLength()),[]),t=(0,u.useMemo)((()=>(0,f.get)(window,"wpseoAdminL10n.shortlinks-insights-word_count","")),[]);let s=(0,m._n)("word","words",e.count,"wordpress-seo"),r=(0,m.__)("Word count","wordpress-seo"),a=(0,m.__)("Learn more about word count","wordpress-seo");return"character"===e.unit&&(s=(0,m._n)("character","characters",e.count,"wordpress-seo"),r=(0,m.__)("Character count","wordpress-seo"),
/* translators: Hidden accessibility text. */
a=(0,m.__)("Learn more about character count","wordpress-seo")),(0,c.createElement)(Kt.InsightsCard,{amount:e.count,unit:s,title:r,linkTo:t,linkText:a})},Yr=ht()(kr)`
	width: 18px;
	height: 18px;
	margin: 3px;
`,zr=({location:e})=>{const t=(0,i.useSelect)((e=>e("yoast-seo/editor").getIsElementorEditor()),[]),s=(0,i.useSelect)((e=>e("yoast-seo/editor").isFleschReadingEaseAvailable()),[]),r=xr();return(0,c.createElement)(Rr,{title:(0,m.__)("Insights","wordpress-seo"),id:`yoast-insights-modal-${e}`,shouldCloseOnClickOutside:!t,showChangesWarning:!1,SuffixHeroIcon:(0,c.createElement)(Yr,{className:"yst-text-slate-500",...r})},(0,c.createElement)("div",{className:"yoast-insights yoast-modal-content--columns"},(0,c.createElement)(Mr,{location:e}),(0,c.createElement)("div",null,s&&(0,c.createElement)("div",{className:"yoast-insights-row"},(0,c.createElement)(Lr,null)),(0,c.createElement)("div",{className:"yoast-insights-row yoast-insights-row--columns"},(0,c.createElement)(Cr,null),(0,c.createElement)(Wr,null)),(0,Ce.isFeatureEnabled)("TEXT_FORMALITY")&&(0,c.createElement)(Br,{location:e,name:"YoastTextFormalityMetabox"}))))};zr.propTypes={location:y().string},zr.defaultProps={location:"sidebar"};const Kr=zr;function jr(e){return 0===e.message.length?null:(0,c.createElement)(Kt.Alert,{type:e.type},e.message)}jr.propTypes={message:y().oneOfType([y().array,y().string]).isRequired,type:y().string.isRequired};const Hr=(0,i.withSelect)((e=>{const{getWarningMessage:t}=e("yoast-seo/editor");return{message:t(),type:"info"}}))(jr),Vr=({children:e})=>(0,c.createElement)("div",null,e);Vr.propTypes={renderPriority:y().number.isRequired,children:y().node.isRequired};const Gr=Vr,Zr=c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{fillRule:"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",clipRule:"evenodd"}))})),Qr=window.yoast.searchMetadataPreviews,Xr=ht()(Kt.StyledSection)`
	&${Kt.StyledSectionBase} {
		padding: 0;

		& ${Kt.StyledHeading} {
			${(0,Vt.getDirectionalStyle)("padding-left","padding-right")}: 20px;
			margin-left: ${(0,Vt.getDirectionalStyle)("0","20px")};
		}
	}
`,Jr=({children:e,title:t,icon:s,hasPaperStyle:r,shoppingData:a})=>(0,c.createElement)(Xr,{headingLevel:3,headingText:t,headingIcon:s,headingIconColor:"#555",hasPaperStyle:r,shoppingData:a},e);Jr.propTypes={children:y().element,title:y().string,icon:y().string,hasPaperStyle:y().bool,shoppingData:y().object},Jr.defaultProps={hasPaperStyle:!0,shoppingData:null};const ea=Jr,ta=window.wp.sanitize,{stripHTMLTags:sa}=Vt.strings;function ra(e,t=156){return(e=(e=(0,ta.stripTags)(e)).trim()).length<=t||(e=e.substring(0,t),/\s/.test(e)&&(e=e.substring(0,e.lastIndexOf(" ")))),e}const aa=(0,f.memoize)(((e,t)=>0===e?f.noop:(0,f.debounce)((s=>t(s,e)),500))),na=({link:e,text:t})=>(0,c.createElement)(g.Root,null,(0,c.createElement)("p",null,t),(0,c.createElement)(g.Button,{href:e,as:"a",className:"yst-gap-2 yst-mb-5 yst-mt-2",variant:"upsell",target:"_blank",rel:"noopener"},(0,c.createElement)(b,{className:"yst-w-4 yst-h-4 yst--ms-1 yst-shrink-0"}),(0,m.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,m.__)("Unlock with %1$s","wordpress-seo"),"Yoast WooCommerce SEO")));na.propTypes={link:y().string.isRequired,text:y().string.isRequired};const oa=na,ia=function(e,t){let s=0;return t.shortenedBaseUrl&&"string"==typeof t.shortenedBaseUrl&&(s=t.shortenedBaseUrl.length),e.url=e.url.replace(/\s+/g,"-"),"-"===e.url[e.url.length-1]&&(e.url=e.url.slice(0,-1)),"-"===e.url[s]&&(e.url=e.url.slice(0,s)+e.url.slice(s+1)),function(e){const t=(0,f.get)(window,["YoastSEO","app","pluggable"],!1);if(!t||!(0,f.get)(window,["YoastSEO","app","pluggable","loaded"],!1))return function(e){const t=(0,f.get)(window,["YoastSEO","wp","replaceVarsPlugin","replaceVariables"],f.identity);return{url:e.url,title:sa(t(e.title)),description:sa(t(e.description)),filteredSEOTitle:e.filteredSEOTitle?sa(t(e.filteredSEOTitle)):""}}(e);const s=t._applyModifications.bind(t);return{url:e.url,title:sa(s("data_page_title",e.title)),description:sa(s("data_meta_desc",e.description)),filteredSEOTitle:e.filteredSEOTitle?sa(s("data_page_title",e.filteredSEOTitle)):""}}(e)},la=(0,qt.compose)([(0,i.withSelect)((function(e){const{getBaseUrlFromSettings:t,getDateFromSettings:s,getFocusKeyphrase:r,getRecommendedReplaceVars:a,getReplaceVars:n,getShoppingData:o,getSiteIconUrlFromSettings:i,getSnippetEditorData:l,getSnippetEditorMode:c,getSnippetEditorPreviewImageUrl:d,getSnippetEditorWordsToHighlight:p,isCornerstoneContent:u,getIsTerm:m,getContentLocale:g,getSiteName:h}=e("yoast-seo/editor"),y=n();return y.forEach((e=>{""!==e.value||["title","excerpt","excerpt_only"].includes(e.name)||(e.value="%%"+e.name+"%%")})),{baseUrl:t(),data:l(),date:s(),faviconSrc:i(),keyword:r(),mobileImageSrc:d(),mode:c(),recommendedReplacementVariables:a(),replacementVariables:y,shoppingData:o(),wordsToHighlight:p(),isCornerstone:u(),isTaxonomy:m(),locale:g(),siteName:h()}})),(0,i.withDispatch)((function(e,t,{select:s}){const{updateData:r,switchMode:a,updateAnalysisData:n,findCustomFields:o}=e("yoast-seo/editor"),i=e("core/editor"),l=s("yoast-seo/editor").getPostId();return{onChange:(e,t)=>{switch(e){case"mode":a(t);break;case"slug":r({slug:t}),i&&i.editPost({slug:t});break;default:r({[e]:t})}},onChangeAnalysisData:n,onReplacementVariableSearchChange:aa(l,o)}}))])((e=>{const t=(0,i.useSelect)((e=>e("yoast-seo/editor").selectLink("https://yoa.st/product-google-preview-metabox")),[]),s=(0,i.useSelect)((e=>e("yoast-seo/editor").getIsWooSeoUpsell()),[]),r=(0,m.__)("Want an enhanced Google preview of how your WooCommerce products look in the search results?","wordpress-seo");return(0,c.createElement)(yt.LocationConsumer,null,(a=>(0,c.createElement)(ea,{icon:"eye",hasPaperStyle:e.hasPaperStyle},(0,c.createElement)(c.Fragment,null,s&&(0,c.createElement)(oa,{link:t,text:r}),(0,c.createElement)(Qr.SnippetEditor,{...e,descriptionPlaceholder:(0,m.__)("Please provide a meta description by editing the snippet below.","wordpress-seo"),mapEditorDataToPreview:ia,showCloseButton:!1,idSuffix:a})))))})),{stripHTMLTags:ca}=Vt.strings,da=(e,t)=>{const s=(0,i.select)("yoast-seo/editor").getSnippetEditorTemplates();""===e.title&&(e.title=s.title),""===e.description&&(e.description=s.description);let r=0;return t.shortenedBaseUrl&&"string"==typeof t.shortenedBaseUrl&&(r=t.shortenedBaseUrl.length),e.url=e.url.replace(/\s+/g,"-"),"-"===e.url[e.url.length-1]&&(e.url=e.url.slice(0,-1)),"-"===e.url[r]&&(e.url=e.url.slice(0,r)+e.url.slice(r+1)),{url:e.url,title:ca(Me("data_page_title",e.title)),description:ca(Me("data_meta_desc",e.description)),filteredSEOTitle:ca(Me("data_page_title",e.filteredSEOTitle))}},pa=({isLoading:e,onLoad:t,location:s,...r})=>((0,u.useEffect)((()=>{setTimeout((()=>{e&&t()}))})),e?null:(0,c.createElement)(ea,{icon:"eye",hasPaperStyle:r.hasPaperStyle},(0,c.createElement)(Qr.SnippetEditor,{...r,descriptionPlaceholder:(0,m.__)("Please provide a meta description by editing the snippet below.","wordpress-seo"),mapEditorDataToPreview:da,showCloseButton:!1,idSuffix:s})));pa.propTypes={isLoading:y().bool.isRequired,onLoad:y().func.isRequired,hasPaperStyle:y().bool.isRequired,location:y().string.isRequired};const ua=(0,qt.compose)([(0,i.withSelect)((e=>{const{getBaseUrlFromSettings:t,getDateFromSettings:s,getEditorDataImageUrl:r,getFocusKeyphrase:a,getRecommendedReplaceVars:n,getSiteIconUrlFromSettings:o,getSnippetEditorData:i,getSnippetEditorIsLoading:l,getSnippetEditorMode:c,getSnippetEditorWordsToHighlight:d,isCornerstoneContent:p,getContentLocale:u,getSiteName:m,getReplaceVars:g}=e("yoast-seo/editor");return{baseUrl:t(),data:i(),date:s(),faviconSrc:o(),isLoading:l(),keyword:a(),mobileImageSrc:r(),mode:c(),recommendedReplacementVariables:n(),replacementVariables:g(),wordsToHighlight:d(),isCornerstone:p(),locale:u(),siteName:m()}})),(0,i.withDispatch)((e=>{const{updateData:t,switchMode:s,updateAnalysisData:r,loadSnippetEditorData:a}=e("yoast-seo/editor");return{onChange:(e,r)=>{switch(e){case"mode":s(r);break;case"slug":t({slug:r});break;default:t({[e]:r})}},onChangeAnalysisData:r,onLoad:a}})),ms()])(pa),ma=ht()(Zr)`
	width: 18px;
	height: 18px;
	margin: 3px;
`,ga=()=>{const e=xr(),t=(0,i.useSelect)((e=>e("yoast-seo/editor").getIsElementorEditor()),[]);return(0,c.createElement)(Rr,{title:(0,m.__)("Search appearance","wordpress-seo"),id:"yoast-search-appearance-modal",shouldCloseOnClickOutside:!1,SuffixHeroIcon:(0,c.createElement)(ma,{className:"yst-text-slate-500",...e})},!0===t&&(0,c.createElement)(ua,{showCloseButton:!1,hasPaperStyle:!1}),!1===t&&(0,c.createElement)(la,{showCloseButton:!1,hasPaperStyle:!1}))},ha=c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{d:"M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"}))})),ya=ht().p`
	color: #606770;
	flex-shrink: 0;
	font-size: 12px;
	line-height: 16px;
	overflow: hidden;
	padding: 0;
	text-overflow: ellipsis;
	text-transform: uppercase;
	white-space: nowrap;
	margin: 0;
	position: ${e=>"landscape"===e.mode?"relative":"static"};
`,fa=e=>{const{siteUrl:t}=e;return(0,c.createElement)(c.Fragment,null,(0,c.createElement)("span",{className:"screen-reader-text"},t),(0,c.createElement)(ya,{"aria-hidden":"true"},(0,c.createElement)("span",null,t)))};fa.propTypes={siteUrl:y().string.isRequired};const wa=fa,ba=window.yoast.socialMetadataForms,Ea=window.yoast.styleGuide,_a=ht().img`
	&& {
		max-width: ${e=>e.width}px;
		height: ${e=>e.height}px;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		max-width: none;
	}
`,va=ht().img`
	&&{
		height: 100%;
		position: absolute;
		width: 100%;
		object-fit: cover;
	}
`,ka=ht().div`
	padding-bottom: ${e=>e.aspectRatio}%;
`,xa=e=>{const{imageProps:t,width:s,height:r,imageMode:a}=e;return"landscape"===a?(0,c.createElement)(ka,{aspectRatio:t.aspectRatio},(0,c.createElement)(va,{src:t.src,alt:t.alt})):(0,c.createElement)(_a,{src:t.src,alt:t.alt,width:s,height:r,imageProperties:t})};function Sa(e,t,s){return"landscape"===s?{widthRatio:t.width/e.landscapeWidth,heightRatio:t.height/e.landscapeHeight}:"portrait"===s?{widthRatio:t.width/e.portraitWidth,heightRatio:t.height/e.portraitHeight}:{widthRatio:t.width/e.squareWidth,heightRatio:t.height/e.squareHeight}}function Ta(e,t){return t.widthRatio<=t.heightRatio?{width:Math.round(e.width/t.widthRatio),height:Math.round(e.height/t.widthRatio)}:{width:Math.round(e.width/t.heightRatio),height:Math.round(e.height/t.heightRatio)}}async function Ra(e,t,s=!1){const r=await function(e){return new Promise(((t,s)=>{const r=new Image;r.onload=()=>{t({width:r.width,height:r.height})},r.onerror=s,r.src=e}))}(e);let a=s?"landscape":"square";"Facebook"===t&&(a=(0,ba.determineFacebookImageMode)(r));const n=function(e){return"Twitter"===e?ba.TWITTER_IMAGE_SIZES:ba.FACEBOOK_IMAGE_SIZES}(t),o=function(e,t,s){return"square"===s&&t.width===t.height?{width:e.squareWidth,height:e.squareHeight}:Ta(t,Sa(e,t,s))}(n,r,a);return{mode:a,height:o.height,width:o.width}}async function Ca(e,t,s=!1){try{return{imageProperties:await Ra(e,t,s),status:"loaded"}}catch(e){return{imageProperties:null,status:"errored"}}}xa.propTypes={imageProps:y().shape({src:y().string.isRequired,alt:y().string.isRequired,aspectRatio:y().number.isRequired}).isRequired,width:y().number.isRequired,height:y().number.isRequired,imageMode:y().string},xa.defaultProps={imageMode:"landscape"};const Ia=ht().div`
	position: relative;
	${e=>"landscape"===e.mode?`max-width: ${e.dimensions.width}`:`min-width: ${e.dimensions.width}; height: ${e.dimensions.height}`};
	overflow: hidden;
	background-color: ${Ea.colors.$color_white};
`,Pa=ht().div`
	box-sizing: border-box;
	max-width: ${ba.FACEBOOK_IMAGE_SIZES.landscapeWidth}px;
	height: ${ba.FACEBOOK_IMAGE_SIZES.landscapeHeight}px;
	background-color: ${Ea.colors.$color_grey};
	border-style: dashed;
	border-width: 1px;
	// We're not using standard colors to increase contrast for accessibility.
	color: #006DAC;
	// We're not using standard colors to increase contrast for accessibility.
	background-color: #f1f1f1;
	display: flex;
	justify-content: center;
	align-items: center;
	text-decoration: underline;
	font-size: 14px;
	cursor: pointer;
`;class La extends c.Component{constructor(e){super(e),this.state={imageProperties:null,status:"loading"},this.socialMedium="Facebook",this.handleFacebookImage=this.handleFacebookImage.bind(this),this.setState=this.setState.bind(this)}async handleFacebookImage(){try{const e=await Ca(this.props.src,this.socialMedium);this.setState(e),this.props.onImageLoaded(e.imageProperties.mode||"landscape")}catch(e){this.setState(e),this.props.onImageLoaded("landscape")}}componentDidUpdate(e){e.src!==this.props.src&&this.handleFacebookImage()}componentDidMount(){this.handleFacebookImage()}retrieveContainerDimensions(e){switch(e){case"square":return{height:ba.FACEBOOK_IMAGE_SIZES.squareHeight+"px",width:ba.FACEBOOK_IMAGE_SIZES.squareWidth+"px"};case"portrait":return{height:ba.FACEBOOK_IMAGE_SIZES.portraitHeight+"px",width:ba.FACEBOOK_IMAGE_SIZES.portraitWidth+"px"};case"landscape":return{height:ba.FACEBOOK_IMAGE_SIZES.landscapeHeight+"px",width:ba.FACEBOOK_IMAGE_SIZES.landscapeWidth+"px"}}}render(){const{imageProperties:e,status:t}=this.state;if("loading"===t||""===this.props.src||"errored"===t)return(0,c.createElement)(Pa,{onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,m.__)("Select image","wordpress-seo"));const s=this.retrieveContainerDimensions(e.mode);return(0,c.createElement)(Ia,{mode:e.mode,dimensions:s,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave,onClick:this.props.onImageClick},(0,c.createElement)(xa,{imageProps:{src:this.props.src,alt:this.props.alt,aspectRatio:ba.FACEBOOK_IMAGE_SIZES.aspectRatio},width:e.width,height:e.height,imageMode:e.mode}))}}La.propTypes={src:y().string,alt:y().string,onImageLoaded:y().func,onImageClick:y().func,onMouseEnter:y().func,onMouseLeave:y().func},La.defaultProps={src:"",alt:"",onImageLoaded:f.noop,onImageClick:f.noop,onMouseEnter:f.noop,onMouseLeave:f.noop};const Aa=La,Oa=ht().span`
	line-height: ${20}px;
	min-height : ${20}px;
	color: #1d2129;
	font-weight: 600;
	overflow: hidden;
	font-size: 16px;
	margin: 3px 0 0;
	letter-spacing: normal;
	white-space: normal;
	flex-shrink: 0;
	cursor: pointer;
	display: -webkit-box;
	-webkit-line-clamp: ${e=>e.lineCount};
	-webkit-box-orient: vertical;
	overflow: hidden;
`,Fa=ht().p`
	line-height: ${16}px;
	min-height : ${16}px;
	color: #606770;
	font-size: 14px;
	padding: 0;
	text-overflow: ellipsis;
	margin: 3px 0 0 0;
	display: -webkit-box;
	cursor: pointer;
	-webkit-line-clamp: ${e=>e.lineCount};
	-webkit-box-orient: vertical;
	overflow: hidden;

	@media all and ( max-width: ${e=>e.maxWidth} ) {
		display: none;
	}
`,Da=e=>{switch(e){case"landscape":return"527px";case"square":case"portrait":return"369px";default:return"476px"}},Ma=ht().div`
	box-sizing: border-box;
	display: flex;
	flex-direction: ${e=>"landscape"===e.mode?"column":"row"};
	background-color: #f2f3f5;
	max-width: 527px;
`,Na=ht().div`
	box-sizing: border-box;
	background-color: #f2f3f5;
	margin: 0;
	padding: 10px 12px;
	position: relative;
	border-bottom: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border-top: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border-right: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border: ${e=>"landscape"===e.mode?"1px solid #dddfe2":""};
	display: flex;
	flex-direction: column;
	flex-grow: 1;
	justify-content: ${e=>"landscape"===e.mode?"flex-start":"center"};
	font-size: 12px;
	overflow: hidden;
`;class qa extends c.Component{constructor(e){super(e),this.state={imageMode:null,maxLineCount:0,descriptionLineCount:0},this.facebookTitleRef=d().createRef(),this.onImageLoaded=this.onImageLoaded.bind(this),this.onImageEnter=this.props.onMouseHover.bind(this,"image"),this.onTitleEnter=this.props.onMouseHover.bind(this,"title"),this.onDescriptionEnter=this.props.onMouseHover.bind(this,"description"),this.onLeave=this.props.onMouseHover.bind(this,""),this.onSelectTitle=this.props.onSelect.bind(this,"title"),this.onSelectDescription=this.props.onSelect.bind(this,"description")}onImageLoaded(e){this.setState({imageMode:e})}getTitleLineCount(){return this.facebookTitleRef.current.offsetHeight/20}maybeSetMaxLineCount(){const{imageMode:e,maxLineCount:t}=this.state,s="landscape"===e?2:5;s!==t&&this.setState({maxLineCount:s})}maybeSetDescriptionLineCount(){const{descriptionLineCount:e,maxLineCount:t,imageMode:s}=this.state,r=this.getTitleLineCount();let a=t-r;"portrait"===s&&(a=5===r?0:4),a!==e&&this.setState({descriptionLineCount:a})}componentDidUpdate(){this.maybeSetMaxLineCount(),this.maybeSetDescriptionLineCount()}render(){const{imageMode:e,maxLineCount:t,descriptionLineCount:s}=this.state;return(0,c.createElement)(Ma,{id:"facebookPreview",mode:e},(0,c.createElement)(Aa,{src:this.props.imageUrl||this.props.imageFallbackUrl,alt:this.props.alt,onImageLoaded:this.onImageLoaded,onImageClick:this.props.onImageClick,onMouseEnter:this.onImageEnter,onMouseLeave:this.onLeave}),(0,c.createElement)(Na,{mode:e},(0,c.createElement)(wa,{siteUrl:this.props.siteUrl,mode:e}),(0,c.createElement)(Oa,{ref:this.facebookTitleRef,onMouseEnter:this.onTitleEnter,onMouseLeave:this.onLeave,onClick:this.onSelectTitle,lineCount:t},this.props.title),s>0&&(0,c.createElement)(Fa,{maxWidth:Da(e),onMouseEnter:this.onDescriptionEnter,onMouseLeave:this.onLeave,onClick:this.onSelectDescription,lineCount:s},this.props.description)))}}qa.propTypes={siteUrl:y().string.isRequired,title:y().string.isRequired,description:y().string,imageUrl:y().string,imageFallbackUrl:y().string,alt:y().string,onSelect:y().func,onImageClick:y().func,onMouseHover:y().func},qa.defaultProps={description:"",alt:"",imageUrl:"",imageFallbackUrl:"",onSelect:()=>{},onImageClick:()=>{},onMouseHover:()=>{}};const Ua=qa,$a=ht().div`
	text-transform: lowercase;
	color: rgb(83, 100, 113);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	fill: currentcolor;
	display: flex;
	flex-direction: row;
	align-items: flex-end;
`,Ba=e=>(0,c.createElement)($a,null,(0,c.createElement)("span",null,e.siteUrl));Ba.propTypes={siteUrl:y().string.isRequired};const Wa=Ba,Ya=(e,t=!0)=>e?`\n\t\t\tmax-width: ${ba.TWITTER_IMAGE_SIZES.landscapeWidth}px;\n\t\t\t${t?"border-bottom: 1px solid #E1E8ED;":""}\n\t\t\tborder-radius: 14px 14px 0 0;\n\t\t\t`:`\n\t\twidth: ${ba.TWITTER_IMAGE_SIZES.squareWidth}px;\n\t\t${t?"border-right: 1px solid #E1E8ED;":""}\n\t\tborder-radius: 14px 0 0 14px;\n\t\t`,za=ht().div`
	position: relative;
	box-sizing: content-box;
	overflow: hidden;
	background-color: #e1e8ed;
	flex-shrink: 0;
	${e=>Ya(e.isLarge)}
`,Ka=ht().div`
	display: flex;
	justify-content: center;
	align-items: center;
	box-sizing: border-box;
	max-width: 100%;
	margin: 0;
	padding: 1em;
	text-align: center;
	font-size: 1rem;
	${e=>Ya(e.isLarge,!1)}
`,ja=ht()(Ka)`
	${e=>e.isLarge&&`height: ${ba.TWITTER_IMAGE_SIZES.landscapeHeight}px;`}
	border-top-left-radius: 14px;
	${e=>e.isLarge?"border-top-right-radius":"border-bottom-left-radius"}: 14px;
	border-style: dashed;
	border-width: 1px;
	// We're not using standard colors to increase contrast for accessibility.
	color: #006DAC;
	// We're not using standard colors to increase contrast for accessibility.
	background-color: #f1f1f1;
	text-decoration: underline;
	font-size: 14px;
	cursor: pointer;
`;class Ha extends d().Component{constructor(e){super(e),this.state={status:"loading"},this.socialMedium="Twitter",this.handleTwitterImage=this.handleTwitterImage.bind(this),this.setState=this.setState.bind(this)}async handleTwitterImage(){if(null===this.props.src)return;const e=await Ca(this.props.src,this.socialMedium,this.props.isLarge);this.setState(e)}componentDidUpdate(e){e.src!==this.props.src&&this.handleTwitterImage()}componentDidMount(){this.handleTwitterImage()}render(){const{status:e,imageProperties:t}=this.state;return"loading"===e||""===this.props.src||"errored"===e?(0,c.createElement)(ja,{isLarge:this.props.isLarge,onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,m.__)("Select image","wordpress-seo")):(0,c.createElement)(za,{isLarge:this.props.isLarge,onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,c.createElement)(xa,{imageProps:{src:this.props.src,alt:this.props.alt,aspectRatio:ba.TWITTER_IMAGE_SIZES.aspectRatio},width:t.width,height:t.height,imageMode:t.mode}))}}Ha.propTypes={isLarge:y().bool.isRequired,src:y().string,alt:y().string,onImageClick:y().func,onMouseEnter:y().func,onMouseLeave:y().func},Ha.defaultProps={src:"",alt:"",onMouseEnter:f.noop,onImageClick:f.noop,onMouseLeave:f.noop};const Va=ht().div`
	display: flex;
	flex-direction: column;
	padding: 12px;
	justify-content: center;
	margin: 0;
	box-sizing: border-box;
	flex: auto;
	min-width: 0px;
	gap:2px;
	> * {
		line-height:20px;
		min-height:20px;
		font-size:15px;
    }
`,Ga=e=>(0,c.createElement)(Va,null,e.children);Ga.propTypes={children:y().array.isRequired};const Za=Ga,Qa=ht().p`
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	color: rgb(15, 20, 25);
	cursor: pointer;
`,Xa=ht().p`
	max-height: 55px;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	color: rgb(83, 100, 113);
	display: -webkit-box;
	cursor: pointer;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;

	@media all and ( max-width: ${ba.TWITTER_IMAGE_SIZES.landscapeWidth}px ) {
		display: none;
	}
`,Ja=ht().div`
	font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Ubuntu, "Helvetica Neue", sans-serif;
	font-size: 15px;
	font-weight: 400;
	line-height: 20px;
	max-width: 507px;
	border: 1px solid #E1E8ED;
	box-sizing: border-box;
	border-radius: 14px;
	color: #292F33;
	background: #FFFFFF;
	text-overflow: ellipsis;
	display: flex;

	&:hover {
		background: #f5f8fa;
		border: 1px solid rgba(136,153,166,.5);
	}
`,en=ht()(Ja)`
	flex-direction: column;
	max-height: 370px;
`,tn=ht()(Ja)`
	flex-direction: row;
	height: 125px;
`;class sn extends c.Component{constructor(e){super(e),this.onImageEnter=this.props.onMouseHover.bind(this,"image"),this.onTitleEnter=this.props.onMouseHover.bind(this,"title"),this.onDescriptionEnter=this.props.onMouseHover.bind(this,"description"),this.onLeave=this.props.onMouseHover.bind(this,""),this.onSelectTitle=this.props.onSelect.bind(this,"title"),this.onSelectDescription=this.props.onSelect.bind(this,"description")}render(){const{isLarge:e,imageUrl:t,imageFallbackUrl:s,alt:r,title:a,description:n,siteUrl:o}=this.props,i=e?en:tn;return(0,c.createElement)(i,{id:"twitterPreview"},(0,c.createElement)(Ha,{src:t||s,alt:r,isLarge:e,onImageClick:this.props.onImageClick,onMouseEnter:this.onImageEnter,onMouseLeave:this.onLeave}),(0,c.createElement)(Za,null,(0,c.createElement)(Wa,{siteUrl:o}),(0,c.createElement)(Qa,{onMouseEnter:this.onTitleEnter,onMouseLeave:this.onLeave,onClick:this.onSelectTitle},a),(0,c.createElement)(Xa,{onMouseEnter:this.onDescriptionEnter,onMouseLeave:this.onLeave,onClick:this.onSelectDescription},n)))}}sn.propTypes={siteUrl:y().string.isRequired,title:y().string.isRequired,description:y().string,isLarge:y().bool,imageUrl:y().string,imageFallbackUrl:y().string,alt:y().string,onSelect:y().func,onImageClick:y().func,onMouseHover:y().func},sn.defaultProps={description:"",alt:"",imageUrl:"",imageFallbackUrl:"",onSelect:()=>{},onImageClick:()=>{},onMouseHover:()=>{},isLarge:!0};const rn=sn,an=window.yoast.replacementVariableEditor;class nn extends c.Component{constructor(e){super(e),this.state={activeField:"",hoveredField:""},this.SocialPreview="Social"===e.socialMediumName?Ua:rn,this.setHoveredField=this.setHoveredField.bind(this),this.setActiveField=this.setActiveField.bind(this),this.setEditorRef=this.setEditorRef.bind(this),this.setEditorFocus=this.setEditorFocus.bind(this)}setHoveredField(e){e!==this.state.hoveredField&&this.setState({hoveredField:e})}setActiveField(e){e!==this.state.activeField&&this.setState({activeField:e},(()=>this.setEditorFocus(e)))}setEditorFocus(e){switch(e){case"title":this.titleEditorRef.focus();break;case"description":this.descriptionEditorRef.focus()}}setEditorRef(e,t){switch(e){case"title":this.titleEditorRef=t;break;case"description":this.descriptionEditorRef=t}}render(){const{onDescriptionChange:e,onTitleChange:t,onSelectImageClick:s,onRemoveImageClick:r,socialMediumName:a,imageWarnings:n,siteUrl:o,description:i,descriptionInputPlaceholder:l,descriptionPreviewFallback:p,imageUrl:u,imageFallbackUrl:m,alt:g,title:h,titleInputPlaceholder:y,titlePreviewFallback:f,replacementVariables:w,recommendedReplacementVariables:b,applyReplacementVariables:E,onReplacementVariableSearchChange:_,isPremium:v,isLarge:k,socialPreviewLabel:x,idSuffix:S,activeMetaTabId:T}=this.props,R=E({title:h||f,description:i||p});return(0,c.createElement)(d().Fragment,null,x&&(0,c.createElement)(Kt.SimulatedLabel,null,x),(0,c.createElement)(this.SocialPreview,{onMouseHover:this.setHoveredField,onSelect:this.setActiveField,onImageClick:s,siteUrl:o,title:R.title,description:R.description,imageUrl:u,imageFallbackUrl:m,alt:g,isLarge:k,activeMetaTabId:T}),(0,c.createElement)(ba.SocialMetadataPreviewForm,{onDescriptionChange:e,socialMediumName:a,title:h,titleInputPlaceholder:y,onRemoveImageClick:r,imageSelected:!!u,imageUrl:u,imageFallbackUrl:m,onTitleChange:t,onSelectImageClick:s,description:i,descriptionInputPlaceholder:l,imageWarnings:n,replacementVariables:w,recommendedReplacementVariables:b,onReplacementVariableSearchChange:_,onMouseHover:this.setHoveredField,hoveredField:this.state.hoveredField,onSelect:this.setActiveField,activeField:this.state.activeField,isPremium:v,setEditorRef:this.setEditorRef,idSuffix:S}))}}nn.propTypes={title:y().string.isRequired,onTitleChange:y().func.isRequired,description:y().string.isRequired,onDescriptionChange:y().func.isRequired,imageUrl:y().string.isRequired,imageFallbackUrl:y().string.isRequired,onSelectImageClick:y().func.isRequired,onRemoveImageClick:y().func.isRequired,socialMediumName:y().string.isRequired,alt:y().string,isPremium:y().bool,imageWarnings:y().array,isLarge:y().bool,siteUrl:y().string,descriptionInputPlaceholder:y().string,titleInputPlaceholder:y().string,descriptionPreviewFallback:y().string,titlePreviewFallback:y().string,replacementVariables:an.replacementVariablesShape,recommendedReplacementVariables:an.recommendedReplacementVariablesShape,applyReplacementVariables:y().func,onReplacementVariableSearchChange:y().func,socialPreviewLabel:y().string,idSuffix:y().string,activeMetaTabId:y().string},nn.defaultProps={imageWarnings:[],recommendedReplacementVariables:[],replacementVariables:[],isPremium:!1,isLarge:!0,siteUrl:"",descriptionInputPlaceholder:"",titleInputPlaceholder:"",descriptionPreviewFallback:"",titlePreviewFallback:"",alt:"",applyReplacementVariables:e=>e,onReplacementVariableSearchChange:null,socialPreviewLabel:"",idSuffix:"",activeMetaTabId:""};const on={},ln=(e,t,{log:s=console.warn}={})=>{on[e]||(on[e]=!0,s(t))},cn=(e,t=f.noop)=>{const s={};for(const r in e)Object.hasOwn(e,r)&&Object.defineProperty(s,r,{set:s=>{e[r]=s,t("set",r,s)},get:()=>(t("get",r),e[r])});return s};cn({squareWidth:125,squareHeight:125,landscapeWidth:506,landscapeHeight:265,aspectRatio:50.2},((e,t)=>ln(`@yoast/social-metadata-previews/TWITTER_IMAGE_SIZES/${e}/${t}`,`[@yoast/social-metadata-previews] "TWITTER_IMAGE_SIZES.${t}" is deprecated and will be removed in the future, please use this from @yoast/social-metadata-forms instead.`))),cn({squareWidth:158,squareHeight:158,landscapeWidth:527,landscapeHeight:273,portraitWidth:158,portraitHeight:237,aspectRatio:52.2,largeThreshold:{width:446,height:233}},((e,t)=>ln(`@yoast/social-metadata-previews/FACEBOOK_IMAGE_SIZES/${e}/${t}`,`[@yoast/social-metadata-previews] "FACEBOOK_IMAGE_SIZES.${t}" is deprecated and will be removed in the future, please use this from @yoast/social-metadata-forms instead.`)));const dn=ht().div`
	max-width: calc(527px + 1.5rem);
`,pn=e=>{const t="X"===e.socialMediumName?(0,m.__)("X share preview","wordpress-seo"):(0,m.__)("Social share preview","wordpress-seo"),{locationContext:s}=(0,g.useRootContext)();return(0,c.createElement)(g.Root,null,(0,c.createElement)(dn,null,(0,c.createElement)(g.FeatureUpsell,{shouldUpsell:!0,variant:"card",cardLink:(0,$t.addQueryArgs)(wpseoAdminL10n["shortlinks.upsell.social_preview."+e.socialMediumName.toLowerCase()],{context:s}),cardText:(0,m.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,m.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),"data-action":"load-nfd-ctb","data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"},(0,c.createElement)("div",{className:"yst-grayscale yst-opacity-50"},(0,c.createElement)(g.Label,null,t),(0,c.createElement)(Ua,{title:"",description:"",siteUrl:"",imageUrl:"",imageFallbackUrl:"",alt:"",onSelect:f.noop,onImageClick:f.noop,onMouseHover:f.noop})))))};pn.propTypes={socialMediumName:y().oneOf(["Social","Twitter","X"]).isRequired};const un=pn;class mn extends u.Component{constructor(e){super(e),this.state={activeField:"",hoveredField:""},this.setHoveredField=this.setHoveredField.bind(this),this.setActiveField=this.setActiveField.bind(this),this.setEditorRef=this.setEditorRef.bind(this),this.setEditorFocus=this.setEditorFocus.bind(this)}setHoveredField(e){e!==this.state.hoveredField&&this.setState({hoveredField:e})}setActiveField(e){e!==this.state.activeField&&this.setState({activeField:e},(()=>this.setEditorFocus(e)))}setEditorFocus(e){switch(e){case"title":this.titleEditorRef.focus();break;case"description":this.descriptionEditorRef.focus()}}setEditorRef(e,t){switch(e){case"title":this.titleEditorRef=t;break;case"description":this.descriptionEditorRef=t}}render(){const{onDescriptionChange:e,onTitleChange:t,onSelectImageClick:s,onRemoveImageClick:r,socialMediumName:a,imageWarnings:n,description:o,descriptionInputPlaceholder:i,imageUrl:l,imageFallbackUrl:d,alt:p,title:m,titleInputPlaceholder:g,replacementVariables:h,recommendedReplacementVariables:y,onReplacementVariableSearchChange:f,isPremium:w,location:b}=this.props;return(0,c.createElement)(u.Fragment,null,(0,c.createElement)(un,{socialMediumName:a}),(0,c.createElement)(ba.SocialMetadataPreviewForm,{onDescriptionChange:e,socialMediumName:a,title:m,titleInputPlaceholder:g,onRemoveImageClick:r,imageSelected:!!l,imageUrl:l,imageFallbackUrl:d,imageAltText:p,onTitleChange:t,onSelectImageClick:s,description:o,descriptionInputPlaceholder:i,imageWarnings:n,replacementVariables:h,recommendedReplacementVariables:y,onReplacementVariableSearchChange:f,onMouseHover:this.setHoveredField,hoveredField:this.state.hoveredField,onSelect:this.setActiveField,activeField:this.state.activeField,isPremium:w,setEditorRef:this.setEditorRef,idSuffix:b}))}}mn.propTypes={title:y().string.isRequired,onTitleChange:y().func.isRequired,description:y().string.isRequired,onDescriptionChange:y().func.isRequired,imageUrl:y().string.isRequired,imageFallbackUrl:y().string,onSelectImageClick:y().func.isRequired,onRemoveImageClick:y().func.isRequired,socialMediumName:y().string.isRequired,isPremium:y().bool,imageWarnings:y().array,descriptionInputPlaceholder:y().string,titleInputPlaceholder:y().string,replacementVariables:an.replacementVariablesShape,recommendedReplacementVariables:an.recommendedReplacementVariablesShape,onReplacementVariableSearchChange:y().func,location:y().string,alt:y().string},mn.defaultProps={imageWarnings:[],imageFallbackUrl:"",recommendedReplacementVariables:[],replacementVariables:[],isPremium:!1,descriptionInputPlaceholder:"",titleInputPlaceholder:"",onReplacementVariableSearchChange:null,location:"",alt:""};const gn=mn,hn=(e,t,s)=>{const[r,a]=(0,u.useState)(!1),n=(0,m.sprintf)(
/* Translators: %1$s expands to the jpg format, %2$s expands to the png format,
  %3$s expands to the webp format, %4$s expands to the gif format. */
(0,m.__)("No image was found that we can automatically set as your social image. Please use %1$s, %2$s, %3$s or %4$s formats to ensure it displays correctly on social media.","wordpress-seo"),"JPG","PNG","WEBP","GIF");return(0,u.useEffect)((()=>{a(""===t&&e.toLowerCase().endsWith(".avif"))}),[e,t]),r?[n]:s},yn=e=>{const[t,s]=(0,u.useState)(""),r=hn(e.imageFallbackUrl,e.imageUrl,e.imageWarnings),a=(0,u.useCallback)((e=>{s(e.detail.metaTabId)}),[s]);(0,u.useEffect)((()=>(setTimeout(e.onLoad),window.addEventListener("YoastSEO:metaTabChange",a),()=>{window.removeEventListener("YoastSEO:metaTabChange",a)})),[]);const n={...e,activeMetaTabId:t,imageWarnings:r};return e.isPremium?(0,c.createElement)(p.Slot,{name:`YoastFacebookPremium${e.location.charAt(0).toUpperCase()+e.location.slice(1)}`,fillProps:n}):(0,c.createElement)(gn,{...n})};yn.propTypes={isPremium:y().bool.isRequired,onLoad:y().func.isRequired,location:y().string.isRequired,imageFallbackUrl:y().string,imageUrl:y().string,imageWarnings:y().array},yn.defaultProps={imageFallbackUrl:"",imageUrl:"",imageWarnings:[]};const fn=yn;function wn(e){(function(e){const t=window.wp.media();return t.on("select",(()=>{const s=t.state().get("selection").first();var r;e({type:(r=s.attributes).subtype,width:r.width,height:r.height,url:r.url,id:r.id,sizes:r.sizes,alt:r.alt||r.title||r.name})})),t})(e).open()}const bn=()=>{wn((e=>(0,i.dispatch)("yoast-seo/editor").setFacebookPreviewImage((e=>{const{width:t,height:s}=e,r=(0,ba.determineFacebookImageMode)({width:t,height:s}),a=ba.FACEBOOK_IMAGE_SIZES[r+"Width"],n=ba.FACEBOOK_IMAGE_SIZES[r+"Height"],o=Object.values(e.sizes).find((e=>e.width>=a&&e.height>=n));return{url:o?o.url:e.url,id:e.id,warnings:(0,Vt.validateFacebookImage)(e),alt:e.alt||""}})(e))))},En=(0,qt.compose)([(0,i.withSelect)((e=>{const{getFacebookDescription:t,getDescription:s,getFacebookTitle:r,getSeoTitle:a,getFacebookImageUrl:n,getImageFallback:o,getFacebookWarnings:i,getRecommendedReplaceVars:l,getReplaceVars:c,getSiteUrl:d,getSeoTitleTemplate:p,getSeoTitleTemplateNoFallback:u,getSocialTitleTemplate:m,getSeoDescriptionTemplate:g,getSocialDescriptionTemplate:h,getReplacedExcerpt:y,getFacebookAltText:f}=e("yoast-seo/editor");return{imageUrl:n(),imageFallbackUrl:o(),recommendedReplacementVariables:l(),replacementVariables:c(),description:t(),descriptionPreviewFallback:h()||s()||g()||y()||"",title:r(),titlePreviewFallback:m()||a()||u()||p()||"",imageWarnings:i(),siteUrl:d(),isPremium:!!ke().isPremium,titleInputPlaceholder:"",descriptionInputPlaceholder:"",socialMediumName:"Social",alt:f()}})),(0,i.withDispatch)(((e,t,{select:s})=>{const{setFacebookPreviewTitle:r,setFacebookPreviewDescription:a,clearFacebookPreviewImage:n,loadFacebookPreviewData:o,findCustomFields:i}=e("yoast-seo/editor"),l=s("yoast-seo/editor").getPostId();return{onSelectImageClick:bn,onRemoveImageClick:n,onDescriptionChange:a,onTitleChange:r,onLoad:o,onReplacementVariableSearchChange:aa(l,i)}})),ms()])(fn),vn=e=>{const t=hn(e.imageFallbackUrl,e.imageUrl,e.imageWarnings);(0,u.useEffect)((()=>{setTimeout(e.onLoad)}),[]);const s={...e,imageWarnings:t};return e.isPremium?(0,c.createElement)(p.Slot,{name:`YoastTwitterPremium${e.location.charAt(0).toUpperCase()+e.location.slice(1)}`,fillProps:s}):(0,c.createElement)(gn,{...s})};vn.propTypes={isPremium:y().bool.isRequired,onLoad:y().func.isRequired,location:y().string.isRequired,imageFallbackUrl:y().string,imageUrl:y().string,imageWarnings:y().array},vn.defaultProps={imageFallbackUrl:"",imageUrl:"",imageWarnings:[]};const kn=vn,xn=()=>{wn((e=>(0,i.dispatch)("yoast-seo/editor").setTwitterPreviewImage((e=>{const t="summary"!==(0,f.get)(window,"wpseoScriptData.metabox.twitterCardType")?"landscape":"square",s=ba.TWITTER_IMAGE_SIZES[t+"Width"],r=ba.TWITTER_IMAGE_SIZES[t+"Height"],a=Object.values(e.sizes).find((e=>e.width>=s&&e.height>=r));return{url:a?a.url:e.url,id:e.id,warnings:(0,Vt.validateTwitterImage)(e),alt:e.alt||""}})(e))))},Sn=(0,qt.compose)([(0,i.withSelect)((e=>{const{getTwitterDescription:t,getTwitterTitle:s,getTwitterImageUrl:r,getFacebookImageUrl:a,getFacebookTitle:n,getFacebookDescription:o,getDescription:i,getSeoTitle:l,getTwitterWarnings:c,getTwitterImageType:d,getImageFallback:p,getRecommendedReplaceVars:u,getReplaceVars:m,getSiteUrl:g,getSeoTitleTemplate:h,getSeoTitleTemplateNoFallback:y,getSocialTitleTemplate:f,getSeoDescriptionTemplate:w,getSocialDescriptionTemplate:b,getReplacedExcerpt:E,getTwitterAltText:_}=e("yoast-seo/editor");return{imageUrl:r(),imageFallbackUrl:a()||p(),recommendedReplacementVariables:u(),replacementVariables:m(),description:t(),descriptionPreviewFallback:b()||o()||i()||w()||E()||"",title:s(),titlePreviewFallback:f()||n()||l()||y()||h()||"",imageWarnings:c(),siteUrl:g(),isPremium:!!ke().isPremium,isLarge:"summary"!==d(),titleInputPlaceholder:"",descriptionInputPlaceholder:"",socialMediumName:"X",alt:_()}})),(0,i.withDispatch)(((e,t,{select:s})=>{const{setTwitterPreviewTitle:r,setTwitterPreviewDescription:a,clearTwitterPreviewImage:n,loadTwitterPreviewData:o,findCustomFields:i}=e("yoast-seo/editor"),l=s("yoast-seo/editor").getPostId();return{onSelectImageClick:xn,onRemoveImageClick:n,onDescriptionChange:a,onTitleChange:r,onLoad:o,onReplacementVariableSearchChange:aa(l,i)}})),ms()])(kn),Tn=ht()(Kt.Collapsible)`
	h2 > button {
		padding-left: 0;
		padding-top: 16px;

		&:hover {
			background-color: #f0f0f0;
		}
	}

	div[class^="collapsible_content"] {
		padding: 24px 0;
		margin: 0 24px;
		border-top: 1px solid rgba(0,0,0,0.2);
	}

`,Rn=e=>(0,c.createElement)(Tn,{hasPadding:!1,hasSeparator:!0,...e}),Cn=ht().legend`
	margin: 16px 0;
	padding: 0;
	color: ${Ea.colors.$color_headings};
	font-size: 12px;
	font-weight: 300;
`,In=ht().legend`
	margin: 0 0 16px;
	padding: 0;
	color: ${Ea.colors.$color_headings};
	font-size: 12px;
	font-weight: 300;
`,Pn=ht()(ha)`
	width: 18px;
	height: 18px;
	margin: 3px;
`,Ln=e=>{const{useOpenGraphData:t,useTwitterData:s}=e;if(!t&&!s)return;const r=xr();return(0,c.createElement)(Rr
/* translators: Social media appearance refers to a preview of how a page will be represented on social media. */,{title:(0,m.__)("Social media appearance","wordpress-seo"),id:"yoast-social-appearance-modal",shouldCloseOnClickOutside:!1,SuffixHeroIcon:(0,c.createElement)(Pn,{className:"yst-text-slate-500",...r})},t&&(0,c.createElement)(u.Fragment,null,(0,c.createElement)(In,null,(0,m.__)("Determine how your post should look on social media like Facebook, X, Instagram, WhatsApp, Threads, LinkedIn, Slack, and more.","wordpress-seo")),(0,c.createElement)(En,null),s&&(0,c.createElement)(Cn,null,(0,m.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below. If you leave these settings untouched, the 'Social media appearance' settings mentioned above will also be applied for sharing on X.","wordpress-seo"))),t&&s&&(0,c.createElement)(Rn,{title:(0,m.__)("X appearance","wordpress-seo"),hasSeparator:!0,initialIsOpen:!1},(0,c.createElement)(Sn,null)),!t&&s&&(0,c.createElement)(u.Fragment,null,(0,c.createElement)(In,null,(0,m.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below.","wordpress-seo")),(0,c.createElement)(Sn,null)))};Ln.propTypes={useOpenGraphData:y().bool.isRequired,useTwitterData:y().bool.isRequired};const An=Ln,On=(0,m.__)("Check your text on even more SEO criteria and get an enhanced keyphrase analysis, making it easier to optimize your content.","wordpress-seo"),Fn=e=>{const{locationContext:t}=(0,yt.useRootContext)(),s=(0,$t.addQueryArgs)(wpseoAdminL10n[e.buyLink],{context:t});return(0,c.createElement)(ns,{title:(0,m.__)("Get more help with writing content that ranks","wordpress-seo"),description:e.description,benefitsTitle:(0,m.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,m.__)("%s also gives you:","wordpress-seo"),"Yoast SEO Premium"),benefits:oe(),upsellButtonText:(0,m.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,m.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:s,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,m.__)("1 year of premium support and updates included!","wordpress-seo")})};Fn.propTypes={buyLink:y().string.isRequired,description:y().string},Fn.defaultProps={description:On};const Dn=Fn,Mn=({location:e})=>{const[t,s]=(0,u.useState)(!1),r=(0,u.useCallback)((()=>s(!1)),[]),a=(0,u.useCallback)((()=>s(!0)),[]),n=(0,g.useSvgAria)();return(0,c.createElement)(u.Fragment,null,t&&(0,c.createElement)(ds,{title:(0,m.__)("Unlock Premium SEO analysis","wordpress-seo"),onRequestClose:r,additionalClassName:"",className:`${ls} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,id:"yoast-premium-seo-analysis-modal",shouldCloseOnClickOutside:!0},(0,c.createElement)(is,null,(0,c.createElement)(Dn,{buyLink:`shortlinks.upsell.${e}.premium_seo_analysis_button`}))),"sidebar"===e&&(0,c.createElement)(Ht,{id:"yoast-premium-seo-analysis-modal-open-button",title:(0,m.__)("Premium SEO analysis","wordpress-seo"),prefixIcon:{icon:"seo-score-none",color:Ea.colors.$color_grey},onClick:a},(0,c.createElement)("div",{className:"yst-root"},(0,c.createElement)(g.Badge,{size:"small",variant:"upsell"},(0,c.createElement)(Ut,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...n})))),"metabox"===e&&(0,c.createElement)("div",{className:"yst-root"},(0,c.createElement)(zt,{id:"yoast-premium-seo-analysis-metabox-modal-open-button",onClick:a},(0,c.createElement)(Kt.SvgIcon,{icon:"seo-score-none",color:Ea.colors.$color_grey}),(0,c.createElement)(zt.Text,null,(0,m.__)("Premium SEO analysis","wordpress-seo")),(0,c.createElement)(g.Badge,{size:"small",variant:"upsell"},(0,c.createElement)(Ut,{className:"yst-w-2.5 yst-h-2.5 yst-me-1 yst-shrink-0",...n}),(0,c.createElement)("span",null,"Premium")))))};Mn.propTypes={location:y().string},Mn.defaultProps={location:"sidebar"};const Nn=Mn,qn=e=>{const[t,s]=(0,u.useState)(!1),{prefixIcon:r}=e;return(0,c.createElement)("div",{className:"yoast components-panel__body "+(t?"is-opened":"")},(0,c.createElement)("h2",{className:"components-panel__body-title"},(0,c.createElement)("button",{onClick:function(){s(!t)},className:"components-button components-panel__body-toggle",type:"button",id:e.buttonId},(0,c.createElement)("span",{className:"yoast-icon-span",style:{fill:`${r&&r.color||""}`}},r&&(0,c.createElement)(Kt.SvgIcon,{icon:r.icon,color:r.color,size:r.size})),(0,c.createElement)("span",{className:"yoast-title-container"},(0,c.createElement)("div",{className:"yoast-title"},e.title),(0,c.createElement)("div",{className:"yoast-subtitle"},e.subTitle)),e.hasBetaBadgeLabel&&(0,c.createElement)(Kt.BetaBadge,null),(0,c.createElement)("span",{className:"yoast-chevron","aria-hidden":"true"}))),t&&e.children)},Un=qn;qn.propTypes={title:y().string.isRequired,children:y().oneOfType([y().node,y().arrayOf(y().node)]).isRequired,prefixIcon:y().object,subTitle:y().string,hasBetaBadgeLabel:y().bool,buttonId:y().string},qn.defaultProps={prefixIcon:null,subTitle:"",hasBetaBadgeLabel:!1,buttonId:null};var $n=s(6746);const Bn=(0,Vt.makeOutboundLink)(),Wn=ht().div`
	padding: 16px;
`,Yn="yoast-seo/editor";function zn({location:e,show:t}){return t?(0,c.createElement)(Kt.Alert,{type:"info"},(0,m.sprintf)(/* translators: %s Expands to "Yoast News SEO" */
(0,m.__)("Are you working on a news article? %s helps you optimize your site for Google News.","wordpress-seo"),"Yoast News SEO")+" ",(0,c.createElement)(Bn,{href:window.wpseoAdminL10n[`shortlinks.upsell.${e}.news`]},(0,m.sprintf)(/* translators: %s: Expands to "Yoast News SEO". */
(0,m.__)("Buy %s now!","wordpress-seo"),"Yoast News SEO"))):null}zn.propTypes={show:y().bool.isRequired,location:y().string.isRequired};const Kn=(e,t,s)=>{const r=(0,i.useSelect)((e=>e(Yn).getIsProduct()),[]),a=(0,i.useSelect)((e=>e(Yn).getIsWooSeoActive()),[]),n=r&&a?{name:(0,m.__)("Item Page","wordpress-seo"),value:"ItemPage"}:e.find((e=>e.value===t));return[{name:(0,m.sprintf)(/* translators: %1$s expands to the plural name of the current post type, %2$s expands to the current site wide default. */
(0,m.__)("Default for %1$s (%2$s)","wordpress-seo"),s,n?n.name:""),value:""},...e]},jn=e=>(0,m.sprintf)(/* translators: %1$s expands to the plural name of the current post type, %2$s and %3$s expand to a link to the Settings page */
(0,m.__)("You can change the default type for %1$s under Content types in the %2$sSettings%3$s.","wordpress-seo"),e,"{{link}}","{{/link}}");y().string.isRequired,y().string.isRequired,y().string.isRequired;const Hn=e=>{const t=Kn(e.pageTypeOptions,e.defaultPageType,e.postTypeName),s=Kn(e.articleTypeOptions,e.defaultArticleType,e.postTypeName),r=(0,i.useSelect)((e=>e(Yn).selectLink("https://yoa.st/product-schema-metabox")),[]),a=(0,i.useSelect)((e=>e(Yn).getIsWooSeoUpsell()),[]),[n,o]=(0,u.useState)(e.schemaArticleTypeSelected),l=(0,m.__)("Want your products stand out in search results with rich results like price, reviews and more?","wordpress-seo"),d=(0,i.useSelect)((e=>e(Yn).getIsProduct()),[]),p=(0,i.useSelect)((e=>e(Yn).getIsWooSeoActive()),[]),g=(0,i.useSelect)((e=>e(Yn).selectAdminLink("?page=wpseo_page_settings")),[]),h=d&&p,y=(0,u.useCallback)(((e,t)=>{o(t)}),[n]);return(0,u.useEffect)((()=>{y(null,e.schemaArticleTypeSelected)}),[e.schemaArticleTypeSelected]),(0,c.createElement)(u.Fragment,null,(0,c.createElement)(Kt.FieldGroup,{label:(0,m.__)("What type of page or content is this?","wordpress-seo"),linkTo:e.additionalHelpTextLink
/* translators: Hidden accessibility text. */,linkText:(0,m.__)("Learn more about page or content types","wordpress-seo")}),a&&(0,c.createElement)(oa,{link:r,text:l}),(0,c.createElement)(Kt.Select,{id:(0,Vt.join)(["yoast-schema-page-type",e.location]),options:t,label:(0,m.__)("Page type","wordpress-seo"),onChange:e.schemaPageTypeChange,selected:h?"ItemPage":e.schemaPageTypeSelected,disabled:h}),e.showArticleTypeInput&&(0,c.createElement)(Kt.Select,{id:(0,Vt.join)(["yoast-schema-article-type",e.location]),options:s,label:(0,m.__)("Article type","wordpress-seo"),onChange:e.schemaArticleTypeChange,selected:e.schemaArticleTypeSelected,onOptionFocus:y}),(0,c.createElement)(zn,{location:e.location,show:!e.isNewsEnabled&&(b=n,E=e.defaultArticleType,"NewsArticle"===b||""===b&&"NewsArticle"===E)}),e.displayFooter&&!h&&(0,c.createElement)("p",null,(f=e.postTypeName,w=g,(0,$n.Z)({mixedString:jn(f),components:{link:(0,c.createElement)("a",{href:w,target:"_blank",rel:"noreferrer"})}}))),h&&(0,c.createElement)("p",null,(0,m.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,m.__)("You have %1$s activated on your site, automatically setting the Page type for your products to 'Item Page'. As a result, the Page type selection is disabled.","wordpress-seo"),"Yoast WooCommerce SEO")));var f,w,b,E},Vn=y().arrayOf(y().shape({name:y().string,value:y().string}));Hn.propTypes={schemaPageTypeChange:y().func,schemaPageTypeSelected:y().string,pageTypeOptions:Vn.isRequired,schemaArticleTypeChange:y().func,schemaArticleTypeSelected:y().string,articleTypeOptions:Vn.isRequired,showArticleTypeInput:y().bool.isRequired,additionalHelpTextLink:y().string.isRequired,helpTextLink:y().string.isRequired,helpTextTitle:y().string.isRequired,helpTextDescription:y().string.isRequired,postTypeName:y().string.isRequired,displayFooter:y().bool,defaultPageType:y().string.isRequired,defaultArticleType:y().string.isRequired,location:y().string.isRequired,isNewsEnabled:y().bool},Hn.defaultProps={schemaPageTypeChange:()=>{},schemaPageTypeSelected:null,schemaArticleTypeChange:()=>{},schemaArticleTypeSelected:null,displayFooter:!1,isNewsEnabled:!1};const Gn=e=>e.isMetabox?(0,u.createPortal)((0,c.createElement)(Wn,null,(0,c.createElement)(Hn,{...e})),document.getElementById("wpseo-meta-section-schema")):(0,c.createElement)(Hn,{...e});Gn.propTypes={showArticleTypeInput:y().bool,articleTypeLabel:y().string,additionalHelpTextLink:y().string,pageTypeLabel:y().string.isRequired,helpTextLink:y().string.isRequired,helpTextTitle:y().string.isRequired,helpTextDescription:y().string.isRequired,isMetabox:y().bool.isRequired,postTypeName:y().string.isRequired,displayFooter:y().bool,loadSchemaArticleData:y().func.isRequired,loadSchemaPageData:y().func.isRequired,location:y().string.isRequired},Gn.defaultProps={showArticleTypeInput:!1,articleTypeLabel:"",additionalHelpTextLink:"",displayFooter:!1};const Zn=Gn;class Qn{static get articleTypeInput(){return document.getElementById("yoast_wpseo_schema_article_type")}static get defaultArticleType(){return Qn.articleTypeInput.getAttribute("data-default")}static get articleType(){return Qn.articleTypeInput.value}static set articleType(e){Qn.articleTypeInput.value=e}static get pageTypeInput(){return document.getElementById("yoast_wpseo_schema_page_type")}static get defaultPageType(){return Qn.pageTypeInput.getAttribute("data-default")}static get pageType(){return Qn.pageTypeInput.value}static set pageType(e){Qn.pageTypeInput.value=e}}const Xn=e=>{const t=null!==Qn.articleTypeInput;(0,u.useEffect)((()=>{e.loadSchemaPageData(),t&&e.loadSchemaArticleData()}),[]);const{pageTypeOptions:s,articleTypeOptions:r}=window.wpseoScriptData.metabox.schema,a={articleTypeLabel:(0,m.__)("Article type","wordpress-seo"),pageTypeLabel:(0,m.__)("Page type","wordpress-seo"),postTypeName:window.wpseoAdminL10n.postTypeNamePlural,helpTextTitle:(0,m.__)("Yoast SEO automatically describes your pages using schema.org","wordpress-seo"),helpTextDescription:(0,m.__)("This helps search engines understand your website and your content. You can change some of your settings for this page below.","wordpress-seo"),showArticleTypeInput:t,pageTypeOptions:s,articleTypeOptions:r},n={...e,...a,...(o=e.location,"metabox"===o?{helpTextLink:wpseoAdminL10n["shortlinks.metabox.schema.explanation"],additionalHelpTextLink:wpseoAdminL10n["shortlinks.metabox.schema.page_type"],isMetabox:!0}:{helpTextLink:wpseoAdminL10n["shortlinks.sidebar.schema.explanation"],additionalHelpTextLink:wpseoAdminL10n["shortlinks.sidebar.schema.page_type"],isMetabox:!1})};var o;return(0,c.createElement)(Zn,{...n})};Xn.propTypes={displayFooter:y().bool.isRequired,schemaPageTypeSelected:y().string.isRequired,schemaArticleTypeSelected:y().string.isRequired,defaultArticleType:y().string.isRequired,defaultPageType:y().string.isRequired,loadSchemaPageData:y().func.isRequired,loadSchemaArticleData:y().func.isRequired,schemaPageTypeChange:y().func.isRequired,schemaArticleTypeChange:y().func.isRequired,location:y().string.isRequired};const Jn=(0,qt.compose)([(0,i.withSelect)((e=>{const{getPreferences:t,getPageType:s,getDefaultPageType:r,getArticleType:a,getDefaultArticleType:n}=e("yoast-seo/editor"),{displaySchemaSettingsFooter:o,isNewsEnabled:i}=t();return{displayFooter:o,isNewsEnabled:i,schemaPageTypeSelected:s(),schemaArticleTypeSelected:a(),defaultArticleType:n(),defaultPageType:r()}})),(0,i.withDispatch)((e=>{const{setPageType:t,setArticleType:s,getSchemaPageData:r,getSchemaArticleData:a}=e("yoast-seo/editor");return{loadSchemaPageData:r,loadSchemaArticleData:a,schemaPageTypeChange:t,schemaArticleTypeChange:s}})),ms()])(Xn),eo=({noIndex:e,onNoIndexChange:t,editorContext:s,isPrivateBlog:r})=>{const a=(e=>{const t=(0,m.__)("No","wordpress-seo"),s=(0,m.__)("Yes","wordpress-seo"),r=e.noIndex?t:s;return window.wpseoScriptData.isPost?[{name:(0,m.sprintf)(/* translators: %1$s translates to "yes" or "no", %2$s translates to the content type label in plural form */
(0,m.__)("%1$s (current default for %2$s)","wordpress-seo"),r,e.postTypeNamePlural),value:"0"},{name:t,value:"1"},{name:s,value:"2"}]:[{name:(0,m.sprintf)(/* translators: %1$s translates to "yes" or "no", %2$s translates to the content type label in plural form */
(0,m.__)("%1$s (current default for %2$s)","wordpress-seo"),r,e.postTypeNamePlural),value:"default"},{name:s,value:"index"},{name:t,value:"noindex"}]})(s);return(0,c.createElement)(yt.LocationConsumer,null,(s=>(0,c.createElement)(u.Fragment,null,r&&(0,c.createElement)(Kt.Alert,{type:"warning"},(0,m.__)("Even though you can set the meta robots setting here, the entire site is set to noindex in the sitewide privacy settings, so these settings won't have an effect.","wordpress-seo")),(0,c.createElement)(Kt.Select,{label:(0,m.__)("Allow search engines to show this content in search results?","wordpress-seo"),onChange:t,id:(0,Vt.join)(["yoast-meta-robots-noindex",s]),options:a,selected:e,linkTo:wpseoAdminL10n["shortlinks.advanced.allow_search_engines"]
/* translators: Hidden accessibility text. */,linkText:(0,m.__)("Learn more about the no-index setting on our help page.","wordpress-seo")}))))};eo.propTypes={noIndex:y().string.isRequired,onNoIndexChange:y().func.isRequired,editorContext:y().object.isRequired,isPrivateBlog:y().bool},eo.defaultProps={isPrivateBlog:!1};const to=({noFollow:e,onNoFollowChange:t})=>(0,c.createElement)(yt.LocationConsumer,null,(s=>{const r=(0,Vt.join)(["yoast-meta-robots-nofollow",s]);return(0,c.createElement)(Kt.RadioButtonGroup,{id:r,options:[{value:"0",label:"Yes"},{value:"1",label:"No"}],label:(0,m.__)("Should search engines follow links on this content?","wordpress-seo"),groupName:r,onChange:t,selected:e,linkTo:wpseoAdminL10n["shortlinks.advanced.follow_links"]
/* translators: Hidden accessibility text. */,linkText:(0,m.__)("Learn more about the no-follow setting on our help page.","wordpress-seo")})}));to.propTypes={noFollow:y().string.isRequired,onNoFollowChange:y().func.isRequired};const so=({advanced:e,onAdvancedChange:t})=>(0,c.createElement)(yt.LocationConsumer,null,(s=>{const r=(0,Vt.join)(["yoast-meta-robots-advanced",s]),a=`${r}-input`;return(0,c.createElement)(Kt.MultiSelect,{label:(0,m.__)("Meta robots advanced","wordpress-seo"),onChange:t,id:r,inputId:a,options:[{name:(0,m.__)("No Image Index","wordpress-seo"),value:"noimageindex"},{name:(0,m.__)("No Archive","wordpress-seo"),value:"noarchive"},{name:(0,m.__)("No Snippet","wordpress-seo"),value:"nosnippet"}],selected:e,linkTo:wpseoAdminL10n["shortlinks.advanced.meta_robots"]
/* translators: Hidden accessibility text. */,linkText:(0,m.__)("Learn more about advanced meta robots settings on our help page.","wordpress-seo")})}));so.propTypes={advanced:y().array.isRequired,onAdvancedChange:y().func.isRequired};const ro=({breadcrumbsTitle:e,onBreadcrumbsTitleChange:t})=>(0,c.createElement)(yt.LocationConsumer,null,(s=>(0,c.createElement)(Kt.TextInput,{label:(0,m.__)("Breadcrumbs Title","wordpress-seo"),id:(0,Vt.join)(["yoast-breadcrumbs-title",s]),onChange:t,value:e,linkTo:wpseoAdminL10n["shortlinks.advanced.breadcrumbs_title"]
/* translators: Hidden accessibility text. */,linkText:(0,m.__)("Learn more about the breadcrumbs title setting on our help page.","wordpress-seo")})));ro.propTypes={breadcrumbsTitle:y().string.isRequired,onBreadcrumbsTitleChange:y().func.isRequired};const ao=({canonical:e,onCanonicalChange:t})=>(0,c.createElement)(yt.LocationConsumer,null,(s=>(0,c.createElement)(Kt.TextInput,{label:(0,m.__)("Canonical URL","wordpress-seo"),id:(0,Vt.join)(["yoast-canonical",s]),onChange:t,value:e,linkTo:"https://yoa.st/canonical-url"
/* translators: Hidden accessibility text. */,linkText:(0,m.__)("Learn more about canonical URLs on our help page.","wordpress-seo")})));ao.propTypes={canonical:y().string.isRequired,onCanonicalChange:y().func.isRequired};const no=e=>{const{noIndex:t,noFollow:s,advanced:r,breadcrumbsTitle:a,canonical:n,onNoIndexChange:o,onNoFollowChange:i,onAdvancedChange:l,onBreadcrumbsTitleChange:d,onCanonicalChange:p,onLoad:m,isLoading:g,editorContext:h,isBreadcrumbsDisabled:y,isPrivateBlog:f}=e;(0,u.useEffect)((()=>{setTimeout((()=>{g&&m()}))}));const w={noIndex:t,onNoIndexChange:o,editorContext:h,isPrivateBlog:f},b={noFollow:s,onNoFollowChange:i},E={advanced:r,onAdvancedChange:l},_={breadcrumbsTitle:a,onBreadcrumbsTitleChange:d},v={canonical:n,onCanonicalChange:p};return g?null:(0,c.createElement)(u.Fragment,null,(0,c.createElement)(eo,{...w}),h.isPost&&(0,c.createElement)(to,{...b}),h.isPost&&(0,c.createElement)(so,{...E}),!y&&(0,c.createElement)(ro,{..._}),(0,c.createElement)(ao,{...v}))};no.propTypes={noIndex:y().string.isRequired,canonical:y().string.isRequired,onNoIndexChange:y().func.isRequired,onCanonicalChange:y().func.isRequired,onLoad:y().func.isRequired,isLoading:y().bool.isRequired,editorContext:y().object.isRequired,isBreadcrumbsDisabled:y().bool.isRequired,isPrivateBlog:y().bool,advanced:y().array,onAdvancedChange:y().func,noFollow:y().string,onNoFollowChange:y().func,breadcrumbsTitle:y().string,onBreadcrumbsTitleChange:y().func},no.defaultProps={advanced:[],onAdvancedChange:()=>{},noFollow:"",onNoFollowChange:()=>{},breadcrumbsTitle:"",onBreadcrumbsTitleChange:()=>{},isPrivateBlog:!1};const oo=no,io=(0,qt.compose)([(0,i.withSelect)((e=>{const{getNoIndex:t,getNoFollow:s,getAdvanced:r,getBreadcrumbsTitle:a,getCanonical:n,getIsLoading:o,getEditorContext:i,getPreferences:l}=e("yoast-seo/editor"),{isBreadcrumbsDisabled:c,isPrivateBlog:d}=l();return{noIndex:t(),noFollow:s(),advanced:r(),breadcrumbsTitle:a(),canonical:n(),isLoading:o(),editorContext:i(),isBreadcrumbsDisabled:c,isPrivateBlog:d}})),(0,i.withDispatch)((e=>{const{setNoIndex:t,setNoFollow:s,setAdvanced:r,setBreadcrumbsTitle:a,setCanonical:n,loadAdvancedSettingsData:o}=e("yoast-seo/editor");return{onNoIndexChange:t,onNoFollowChange:s,onAdvancedChange:r,onBreadcrumbsTitleChange:a,onCanonicalChange:n,onLoad:o}}))])(oo),lo=window.yoast.relatedKeyphraseSuggestions;function co(e){const{requestLimitReached:t,isSuccess:s,response:r,requestHasData:a,relatedKeyphrases:n}=e;return t?"requestLimitReached":!s&&function(e){return"invalid_json"===(null==e?void 0:e.code)||"fetch_error"===(null==e?void 0:e.code)||!(0,f.isEmpty)(e)&&"error"in e}(r)?"requestFailed":a?function(e){return e&&e.length>=4}(n)?"maxRelatedKeyphrases":void 0:"requestEmpty"}function po(e){var t,s;const{keyphrase:r="",relatedKeyphrases:a=[],renderAction:n=null,requestLimitReached:o=!1,countryCode:i="us",setCountry:l,newRequest:d,response:p={},isRtl:m=!1,userLocale:h="en_US",isPending:y=!1,isPremium:f=!1,semrushUpsellLink:w="",premiumUpsellLink:b=""}=e,[E,_]=(0,u.useState)(i),v=(0,u.useCallback)((async()=>{d(i,r),_(i)}),[i,r,d]);return(0,c.createElement)(g.Root,{context:{isRtl:m}},!o&&!f&&(0,c.createElement)(lo.PremiumUpsell,{url:b,className:"yst-mb-4"}),!o&&(0,c.createElement)(lo.CountrySelector,{countryCode:i,activeCountryCode:E,onChange:l,onClick:v,className:"yst-mb-4",userLocale:h.split("_")[0]}),!y&&(0,c.createElement)(lo.UserMessage,{variant:co(e),upsellLink:w}),(0,c.createElement)(lo.KeyphrasesTable,{relatedKeyphrases:a,columnNames:null==p||null===(t=p.results)||void 0===t?void 0:t.columnNames,data:null==p||null===(s=p.results)||void 0===s?void 0:s.rows,isPending:y,renderButton:n,className:"yst-mt-4"}))}po.propTypes={keyphrase:y().string,relatedKeyphrases:y().array,renderAction:y().func,requestLimitReached:y().bool,countryCode:y().string.isRequired,setCountry:y().func.isRequired,newRequest:y().func.isRequired,response:y().object,isRtl:y().bool,userLocale:y().string,isPending:y().bool,isPremium:y().bool,semrushUpsellLink:y().string,premiumUpsellLink:y().string};const uo=(0,qt.compose)([(0,i.withSelect)((e=>{const{getFocusKeyphrase:t,getSEMrushSelectedCountry:s,getSEMrushRequestLimitReached:r,getSEMrushRequestResponse:a,getSEMrushRequestIsSuccess:n,getSEMrushIsRequestPending:o,getSEMrushRequestHasData:i,getSEMrushRequestKeyphrase:l,getPreference:c,getIsPremium:d,selectLinkParams:p}=e("yoast-seo/editor");return{keyphrase:t(),countryCode:s(),requestLimitReached:r(),response:a(),isSuccess:n(),isPending:o(),requestHasData:i(),lastRequestKeyphrase:l(),isRtl:c("isRtl",!1),userLocale:c("userLocale","en_US"),isPremium:d(),semrushUpsellLink:(0,$t.addQueryArgs)("https://yoa.st/semrush-prices",p()),premiumUpsellLink:(0,$t.addQueryArgs)("https://yoa.st/413",p())}})),(0,i.withDispatch)((e=>{const{setSEMrushChangeCountry:t,setSEMrushNewRequest:s}=e("yoast-seo/editor");return{setCountry:e=>{t(e)},newRequest:(e,t)=>{s(e,t)}}}))])(po),mo=c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"}))}));var go,ho;function yo(){return yo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},yo.apply(this,arguments)}const fo=e=>c.createElement("svg",yo({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 425 456.27"},e),go||(go=c.createElement("path",{d:"M73 405.26a66.79 66.79 0 0 1-6.54-1.7 64.75 64.75 0 0 1-6.28-2.31c-1-.42-2-.89-3-1.37-1.49-.72-3-1.56-4.77-2.56-1.5-.88-2.71-1.64-3.83-2.39-.9-.61-1.8-1.26-2.68-1.92a70.154 70.154 0 0 1-5.08-4.19 69.21 69.21 0 0 1-8.4-9.17c-.92-1.2-1.68-2.25-2.35-3.24a70.747 70.747 0 0 1-3.44-5.64 68.29 68.29 0 0 1-8.29-32.55V142.13a68.26 68.26 0 0 1 8.29-32.55c1-1.92 2.21-3.82 3.44-5.64s2.55-3.58 4-5.27a69.26 69.26 0 0 1 14.49-13.25C50.37 84.19 52.27 83 54.2 82A67.59 67.59 0 0 1 73 75.09a68.75 68.75 0 0 1 13.75-1.39h169.66L263 55.39H86.75A86.84 86.84 0 0 0 0 142.13v196.09A86.84 86.84 0 0 0 86.75 425h11.32v-18.35H86.75A68.75 68.75 0 0 1 73 405.26zM368.55 60.85l-1.41-.53-6.41 17.18 1.41.53a68.06 68.06 0 0 1 8.66 4c1.93 1 3.82 2.2 5.65 3.43A69.19 69.19 0 0 1 391 98.67c1.4 1.68 2.72 3.46 3.95 5.27s2.39 3.72 3.44 5.64a68.29 68.29 0 0 1 8.29 32.55v264.52H233.55l-.44.76c-3.07 5.37-6.26 10.48-9.49 15.19L222 425h203V142.13a87.2 87.2 0 0 0-56.45-81.28z"})),ho||(ho=c.createElement("path",{stroke:"#000",strokeMiterlimit:10,strokeWidth:3.81,d:"M119.8 408.28v46c28.49-1.12 50.73-10.6 69.61-29.58 19.45-19.55 36.17-50 52.61-96L363.94 1.9H305l-98.25 272.89-48.86-153h-54l71.7 184.18a75.67 75.67 0 0 1 0 55.12c-7.3 18.68-20.25 40.66-55.79 47.19z"}))),wo=window.moment;var bo=s.n(wo);const Eo=(0,Vt.makeOutboundLink)(),_o=e=>{const t=(0,m.sprintf)(/* translators: %1$d expands to the amount of allowed keyphrases on a free account, %2$s expands to a link to Wincher plans. */
(0,m.__)("You've reached the maximum amount of %1$d keyphrases you can add to your Wincher account. If you wish to add more keyphrases, please %2$s.","wordpress-seo"),e.limit,"{{updateWincherPlanLink/}}");return(0,c.createElement)(Kt.Alert,{type:"error"},(0,$n.Z)({mixedString:t,components:{updateWincherPlanLink:(0,c.createElement)(Eo,{href:wpseoAdminGlobalL10n["links.wincher.pricing"]},(0,m.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,m.__)("upgrade your %s plan","wordpress-seo"),"Wincher"))}}))};_o.propTypes={limit:y().number},_o.defaultProps={limit:10};const vo=_o,ko=()=>(0,c.createElement)(Kt.Alert,{type:"error"},(0,m.__)("Something went wrong while tracking the ranking position(s) of your page. Please try again later.","wordpress-seo")),xo=window.wp.apiFetch;var So=s.n(xo);async function To(e,t,s,r=200){try{const a=await e();return!!a&&(a.status===r?t(a):s(a))}catch(e){console.error(e.message)}}async function Ro(e){try{return await So()(e)}catch(e){return e.error&&e.status?e:e instanceof Response&&await e.json()}}async function Co(e){return(0,f.isArray)(e)||(e=[e]),await Ro({path:"yoast/v1/wincher/keyphrases/track",method:"POST",data:{keyphrases:e}})}const Io=ht().p`
	color: ${Ea.colors.$color_pink_dark};
	font-size: 14px;
	font-weight: 700;
	margin: 13px 0 10px;
`,Po=ht()(Kt.SvgIcon)`
	margin-right: 5px;
	vertical-align: middle;
`,Lo=ht().button`
	position: absolute;
	top: 9px;
	right: 9px;
	border: none;
    background: none;
    cursor: pointer;
`,Ao=ht().p`
	font-size: 13px;
	font-weight: 500;
	margin: 10px 0 13px;
`,Oo=ht().div`
	position: relative;
	background: ${e=>e.isTitleShortened?"#F5F7F7":"transparent"};
	border: 1px solid #C7C7C7;
	border-left: 4px solid${Ea.colors.$color_pink_dark};
	padding: 0 16px;
	margin-bottom: 1.5em;
`,Fo=e=>{const[t,s]=(0,u.useState)(null);return(0,u.useEffect)((()=>{e&&!t&&async function(){return await Ro({path:"yoast/v1/wincher/account/limit",method:"GET"})}().then((e=>s(e)))}),[t]),t};Fo.propTypes={limit:y().bool.isRequired};const Do=({limit:e,usage:t,isTitleShortened:s,isFreeAccount:r})=>{const a=(0,m.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,m.__)("Your are tracking %1$s out of %2$s keyphrases included in your free account.","wordpress-seo"),t,e),n=(0,m.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,m.__)("Your are tracking %1$s out of %2$s keyphrases included in your account.","wordpress-seo"),t,e),o=r?a:n,i=(0,m.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,m.__)("Keyphrases tracked: %1$s/%2$s","wordpress-seo"),t,e),l=s?i:o;return(0,c.createElement)(Io,null,s&&(0,c.createElement)(Po,{icon:"exclamation-triangle",color:Ea.colors.$color_pink_dark,size:"14px"}),l)};Do.propTypes={limit:y().number.isRequired,usage:y().number.isRequired,isTitleShortened:y().bool,isFreeAccount:y().bool};const Mo=(0,Vt.makeOutboundLink)(),No=({discount:e,months:t})=>{const s=(0,c.createElement)(Mo,{href:wpseoAdminGlobalL10n["links.wincher.upgrade"],style:{fontWeight:600}},(0,m.sprintf)(/* Translators: %s : Expands to "Wincher". */
(0,m.__)("Click here to upgrade your %s plan","wordpress-seo"),"Wincher"));if(!e||!t)return(0,c.createElement)(Ao,null,s);const r=100*e,a=(0,m.sprintf)(
/* Translators: %1$s expands to upgrade account link.
   * %2$s expands to the upgrade discount value.
   * %3$s expands to the upgrade discount duration e.g. 2 months.
   */
(0,m.__)("%1$s and get an exclusive %2$s discount for %3$s month(s).","wordpress-seo"),"{{wincherAccountUpgradeLink/}}",r+"%",t);return(0,c.createElement)(Ao,null,(0,$n.Z)({mixedString:a,components:{wincherAccountUpgradeLink:s}}))};No.propTypes={discount:y().number,months:y().number};const qo=({onClose:e,isTitleShortened:t,trackingInfo:s})=>{const r=(()=>{const[e,t]=(0,u.useState)(null);return(0,u.useEffect)((()=>{e||async function(){return await Ro({path:"yoast/v1/wincher/account/upgrade-campaign",method:"GET"})}().then((e=>t(e)))}),[e]),e})();if(null===s)return null;const{limit:a,usage:n}=s;if(!(a&&n/a>=.8))return null;const o=Boolean(null==r?void 0:r.discount);return(0,c.createElement)(Oo,{isTitleShortened:t},e&&(0,c.createElement)(Lo,{type:"button","aria-label":(0,m.__)("Close the upgrade callout","wordpress-seo"),onClick:e},(0,c.createElement)(Kt.SvgIcon,{icon:"times-circle",color:Ea.colors.$color_pink_dark,size:"14px"})),(0,c.createElement)(Do,{...s,isTitleShortened:t,isFreeAccount:o}),(0,c.createElement)(No,{discount:null==r?void 0:r.discount,months:null==r?void 0:r.months}))};qo.propTypes={onClose:y().func,isTitleShortened:y().bool,trackingInfo:y().object};const Uo=qo,$o=()=>(0,c.createElement)(Kt.Alert,{type:"success"},(0,m.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,m.__)("You have successfully connected to %s! You can now track the SEO performance for the keyphrase(s) of this page.","wordpress-seo"),"Wincher")),Bo=()=>(0,c.createElement)(Kt.Alert,{type:"info"},(0,m.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,m.__)("%s is currently tracking the ranking position(s) of your page. This may take a few minutes. Please wait or check back later.","wordpress-seo"),"Wincher")),Wo=({data:e,mapChartDataToTableData:t,dataTableCaption:s,dataTableHeaderLabels:r,isDataTableVisuallyHidden:a})=>e.length!==r.length?(0,c.createElement)("p",null,(0,m.__)("The number of headers and header labels don't match.","wordpress-seo")):(0,c.createElement)("div",{className:a?"screen-reader-text":null},(0,c.createElement)("table",null,(0,c.createElement)("caption",null,s),(0,c.createElement)("thead",null,(0,c.createElement)("tr",null,r.map(((e,t)=>(0,c.createElement)("th",{key:t},e))))),(0,c.createElement)("tbody",null,(0,c.createElement)("tr",null,e.map(((e,s)=>(0,c.createElement)("td",{key:s},t(e.y))))))));Wo.propTypes={data:y().arrayOf(y().shape({x:y().number,y:y().number})).isRequired,mapChartDataToTableData:y().func,dataTableCaption:y().string.isRequired,dataTableHeaderLabels:y().array.isRequired,isDataTableVisuallyHidden:y().bool},Wo.defaultProps={mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const Yo=Wo,zo=({data:e,width:t,height:s,fillColor:r,strokeColor:a,strokeWidth:n,className:o,mapChartDataToTableData:i,dataTableCaption:l,dataTableHeaderLabels:d,isDataTableVisuallyHidden:p})=>{const m=Math.max(1,Math.max(...e.map((e=>e.x)))),g=Math.max(1,Math.max(...e.map((e=>e.y)))),h=s-n,y=e.map((e=>`${e.x/m*t},${h-e.y/g*h+n}`)).join(" "),f=`0,${h+n} `+y+` ${t},${h+n}`;return(0,c.createElement)(u.Fragment,null,(0,c.createElement)("svg",{width:t,height:s,viewBox:`0 0 ${t} ${s}`,className:o,role:"img","aria-hidden":"true",focusable:"false"},(0,c.createElement)("polygon",{fill:r,points:f}),(0,c.createElement)("polyline",{fill:"none",stroke:a,strokeWidth:n,strokeLinejoin:"round",strokeLinecap:"round",points:y})),i&&(0,c.createElement)(Yo,{data:e,mapChartDataToTableData:i,dataTableCaption:l,dataTableHeaderLabels:d,isDataTableVisuallyHidden:p}))};zo.propTypes={data:y().arrayOf(y().shape({x:y().number,y:y().number})).isRequired,width:y().number.isRequired,height:y().number.isRequired,fillColor:y().string,strokeColor:y().string,strokeWidth:y().number,className:y().string,mapChartDataToTableData:y().func,dataTableCaption:y().string.isRequired,dataTableHeaderLabels:y().array.isRequired,isDataTableVisuallyHidden:y().bool},zo.defaultProps={fillColor:null,strokeColor:"#000000",strokeWidth:1,className:"",mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const Ko=zo,jo=()=>(0,c.createElement)("p",{className:"yoast-wincher-seo-performance-modal__loading-message"},(0,m.__)("Tracking the ranking position…","wordpress-seo")," ",(0,c.createElement)(Kt.SvgIcon,{icon:"loading-spinner"})),Ho=ht()(Kt.SvgIcon)`
	margin-left: 2px;
	flex-shrink: 0;
	rotate: ${e=>e.isImproving?"-90deg":"90deg"};
`,Vo=ht().span`
	color: ${e=>e.isImproving?"#69AB56":"#DC3332"};
	font-size: 13px;
	font-weight: 600;
	line-height: 20px;
	margin-right: 2px;
	margin-left: 12px;
`,Go=ht().td`
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,Zo=ht().td`
	padding-left: 2px !important;
`,Qo=ht().td.attrs({className:"yoast-table--nopadding"})`
	& > div {
		justify-content: center;
	}
`,Xo=ht().div`
	display: flex;
	align-items: center;
	& > a {
		box-sizing: border-box;
	}
`,Jo=ht().button`
	background: none;
	color: inherit;
	border: none;
	padding: 0;
	font: inherit;
	cursor: pointer;
	outline: inherit;
    display: flex;
    align-items: center;
`,ei=ht().tr`
	background-color: ${e=>e.isEnabled?"#FFFFFF":"#F9F9F9"} !important;
`;function ti(e){return Math.round(100*e)}function si({chartData:e}){if((0,f.isEmpty)(e)||(0,f.isEmpty)(e.position))return"?";const t=function(e){return Array.from({length:e.position.history.length},((e,t)=>t+1)).map((e=>(0,m.sprintf)((0,m._n)("%d day","%d days",e,"wordpress-seo"),e)))}(e),s=e.position.history.map(((e,t)=>({x:t,y:101-e.value})));return(0,c.createElement)(Ko,{width:66,height:24,data:s,strokeWidth:1.8,strokeColor:"#498afc",fillColor:"#ade3fc",mapChartDataToTableData:ti,dataTableCaption:(0,m.__)("Keyphrase position in the last 90 days on a scale from 0 to 100.","wordpress-seo"),dataTableHeaderLabels:t})}si.propTypes={chartData:y().object},si.defaultProps={chartData:{}};const ri=({rowData:e})=>{var t;if(null==e||null===(t=e.position)||void 0===t||!t.change)return(0,c.createElement)(si,{chartData:e});const s=e.position.change<0;return(0,c.createElement)(u.Fragment,null,(0,c.createElement)(si,{chartData:e}),(0,c.createElement)(Vo,{isImproving:s},Math.abs(e.position.change)),(0,c.createElement)(Ho,{icon:"caret-right",color:s?"#69AB56":"#DC3332",size:"14px",isImproving:s}))};function ai(e){var t;const{keyphrase:s,rowData:r,onTrackKeyphrase:a,onUntrackKeyphrase:n,isFocusKeyphrase:o,isDisabled:i,isLoading:l,isSelected:d,onSelectKeyphrases:p}=e,g=!(0,f.isEmpty)(r),h=!(0,f.isEmpty)(null==r||null===(t=r.position)||void 0===t?void 0:t.history),y=(0,u.useCallback)((()=>{i||(g?n(s,r.id):a(s))}),[s,a,n,g,r,i]),w=(0,u.useCallback)((()=>{p((e=>d?e.filter((e=>e!==s)):e.concat(s)))}),[p,d,s]);return(0,c.createElement)(ei,{isEnabled:g},(0,c.createElement)(Go,null,h&&(0,c.createElement)(Kt.Checkbox,{id:"select-"+s,onChange:w,checked:d,label:""})),(0,c.createElement)(Zo,null,s,o&&(0,c.createElement)("span",null,"*")),function(e){const{rowData:t,websiteId:s,keyphrase:r,onSelectKeyphrases:a}=e,n=(0,u.useCallback)((()=>{a([r])}),[a,r]),o=!(0,f.isEmpty)(t),i=t&&t.updated_at&&bo()(t.updated_at)>=bo()().subtract(7,"days"),l=t?`https://app.wincher.com/websites/${s}/keywords?serp=${t.id}&utm_medium=plugin&utm_source=yoast&referer=yoast&partner=yoast`:null;return o?i?(0,c.createElement)(u.Fragment,null,(0,c.createElement)("td",null,(0,c.createElement)(Xo,null,function(e){return!e||!e.position||e.position.value>100?"> 100":e.position.value}(t),(0,c.createElement)(Kt.ButtonStyledLink,{variant:"secondary",href:l,style:{height:28,marginLeft:12},rel:"noopener",target:"_blank"},(0,m.__)("View","wordpress-seo")))),(0,c.createElement)("td",{className:"yoast-table--nopadding"},(0,c.createElement)(Jo,{type:"button",onClick:n},(0,c.createElement)(ri,{rowData:t}))),(0,c.createElement)("td",null,(d=t.updated_at,bo()(d).fromNow()))):(0,c.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,c.createElement)(jo,null)):(0,c.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,c.createElement)("i",null,(0,m.__)("Activate tracking to show the ranking position","wordpress-seo")));var d}(e),(0,c.createElement)(Qo,null,function({keyphrase:e,isEnabled:t,toggleAction:s,isLoading:r}){return r?(0,c.createElement)(Kt.SvgIcon,{icon:"loading-spinner"}):(0,c.createElement)(Kt.Toggle,{id:`toggle-keyphrase-tracking-${e}`,className:"wincher-toggle",isEnabled:t,onSetToggleState:s,showToggleStateLabel:!1})}({keyphrase:s,isEnabled:g,toggleAction:y,isLoading:l})))}ri.propTypes={rowData:y().object},ai.propTypes={rowData:y().object,keyphrase:y().string.isRequired,onTrackKeyphrase:y().func,onUntrackKeyphrase:y().func,isFocusKeyphrase:y().bool,isDisabled:y().bool,isLoading:y().bool,websiteId:y().string,isSelected:y().bool.isRequired,onSelectKeyphrases:y().func.isRequired},ai.defaultProps={rowData:{},onTrackKeyphrase:()=>{},onUntrackKeyphrase:()=>{},isFocusKeyphrase:!1,isDisabled:!1,isLoading:!1,websiteId:""};const ni=(0,Vt.makeOutboundLink)(),oi=ht().span`
	display: block;
	font-style: italic;

	@media (min-width: 782px) {
		display: inline;
		position: absolute;
		${(0,Vt.getDirectionalStyle)("right","left")}: 8px;
	}
`,ii=ht().div`
	width: 100%;
	overflow-y: auto;
`,li=ht().th`
	pointer-events: ${e=>e.isDisabled?"none":"initial"};
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,ci=ht().th`
	padding-left: 2px !important;
`,di=e=>{const t=(0,u.useRef)();return(0,u.useEffect)((()=>{t.current=e})),t.current},pi=(0,f.debounce)((async function(e=null,t=null,s=null,r){return await Ro({path:"yoast/v1/wincher/keyphrases",method:"POST",data:{keyphrases:e,permalink:s,startAt:t},signal:r})}),500,{leading:!0}),ui=e=>{const{addTrackedKeyphrase:t,isLoggedIn:s,keyphrases:r,permalink:a,removeTrackedKeyphrase:n,setKeyphraseLimitReached:o,setRequestFailed:i,setRequestSucceeded:l,setTrackedKeyphrases:d,setHasTrackedAll:p,trackAll:g,trackedKeyphrases:h,isNewlyAuthenticated:y,websiteId:w,focusKeyphrase:b,newRequest:E,startAt:_,selectedKeyphrases:v,onSelectKeyphrases:k}=e,x=(0,u.useRef)(),S=(0,u.useRef)(),T=(0,u.useRef)(!1),[R,C]=(0,u.useState)([]),I=(0,u.useCallback)((e=>{const t=e.toLowerCase();return h&&!(0,f.isEmpty)(h)&&h.hasOwnProperty(t)?h[t]:null}),[h]),P=(0,u.useMemo)((()=>async()=>{await To((()=>(S.current&&S.current.abort(),S.current="undefined"==typeof AbortController?null:new AbortController,pi(r,_,a,S.current.signal))),(e=>{l(e),d(e.results)}),(e=>{i(e)}))}),[l,i,d,r,a,_]),L=(0,u.useCallback)((async e=>{const s=(Array.isArray(e)?e:[e]).map((e=>e.toLowerCase()));C((e=>[...e,...s])),await To((()=>Co(s)),(e=>{l(e),t(e.results),P()}),(e=>{400===e.status&&e.limit&&o(e.limit),i(e)}),201),C((e=>(0,f.without)(e,...s)))}),[l,i,o,t,P]),A=(0,u.useCallback)((async(e,t)=>{e=e.toLowerCase(),C((t=>[...t,e])),await To((()=>async function(e){return await Ro({path:"yoast/v1/wincher/keyphrases/untrack",method:"DELETE",data:{keyphraseID:e}})}(t)),(t=>{l(t),n(e)}),(e=>{i(e)})),C((t=>(0,f.without)(t,e)))}),[l,n,i]),O=(0,u.useCallback)((async e=>{E(),await L(e)}),[E,L]),F=di(a),D=di(r),M=di(_),N=a&&_;(0,u.useEffect)((()=>{s&&N&&(a!==F||(0,f.difference)(r,D).length||_!==M)&&P()}),[s,a,F,r,D,P,N,_,M]),(0,u.useEffect)((()=>{if(s&&g&&null!==h){const e=r.filter((e=>!I(e)));e.length&&L(e),p()}}),[s,g,h,L,p,I,r]),(0,u.useEffect)((()=>{y&&!T.current&&(P(),T.current=!0)}),[y,P]),(0,u.useEffect)((()=>{if(s&&!(0,f.isEmpty)(h))return(0,f.filter)(h,(e=>(0,f.isEmpty)(e.updated_at))).length>0&&(x.current=setInterval((()=>{P()}),1e4)),()=>{clearInterval(x.current)}}),[s,h,P]);const q=s&&null===h,U=(0,u.useMemo)((()=>(0,f.isEmpty)(h)?[]:Object.values(h).filter((e=>{var t;return!(0,f.isEmpty)(null==e||null===(t=e.position)||void 0===t?void 0:t.history)})).map((e=>e.keyword))),[h]),$=(0,u.useMemo)((()=>v.length>0&&U.length>0&&U.every((e=>v.includes(e)))),[v,U]),B=(0,u.useCallback)((()=>{k($?[]:U)}),[k,$,U]),W=(0,u.useMemo)((()=>(0,f.orderBy)(r,[e=>Object.values(h||{}).map((e=>e.keyword)).includes(e)],["desc"])),[r,h]);return r&&!(0,f.isEmpty)(r)&&(0,c.createElement)(u.Fragment,null,(0,c.createElement)(ii,null,(0,c.createElement)("table",{className:"yoast yoast-table"},(0,c.createElement)("thead",null,(0,c.createElement)("tr",null,(0,c.createElement)(li,{isDisabled:0===U.length},(0,c.createElement)(Kt.Checkbox,{id:"select-all",onChange:B,checked:$,label:""})),(0,c.createElement)(ci,{scope:"col",abbr:(0,m.__)("Keyphrase","wordpress-seo")},(0,m.__)("Keyphrase","wordpress-seo")),(0,c.createElement)("th",{scope:"col",abbr:(0,m.__)("Position","wordpress-seo")},(0,m.__)("Position","wordpress-seo")),(0,c.createElement)("th",{scope:"col",abbr:(0,m.__)("Position over time","wordpress-seo")},(0,m.__)("Position over time","wordpress-seo")),(0,c.createElement)("th",{scope:"col",abbr:(0,m.__)("Last updated","wordpress-seo")},(0,m.__)("Last updated","wordpress-seo")),(0,c.createElement)("th",{scope:"col",abbr:(0,m.__)("Tracking","wordpress-seo")},(0,m.__)("Tracking","wordpress-seo")))),(0,c.createElement)("tbody",null,W.map(((e,t)=>(0,c.createElement)(ai,{key:`trackable-keyphrase-${t}`,keyphrase:e,onTrackKeyphrase:O,onUntrackKeyphrase:A,rowData:I(e),isFocusKeyphrase:e===b.trim().toLowerCase(),websiteId:w,isDisabled:!s,isLoading:q||R.indexOf(e.toLowerCase())>=0,isSelected:v.includes(e),onSelectKeyphrases:k})))))),(0,c.createElement)("p",{style:{marginBottom:0,position:"relative"}},(0,c.createElement)(ni,{href:wpseoAdminGlobalL10n["links.wincher.login"]},(0,m.sprintf)(/* translators: %s expands to Wincher */
(0,m.__)("Get more insights over at %s","wordpress-seo"),"Wincher")),(0,c.createElement)(oi,null,(0,m.__)("* focus keyphrase","wordpress-seo"))))};ui.propTypes={addTrackedKeyphrase:y().func.isRequired,isLoggedIn:y().bool,isNewlyAuthenticated:y().bool,keyphrases:y().array,newRequest:y().func.isRequired,removeTrackedKeyphrase:y().func.isRequired,setRequestFailed:y().func.isRequired,setKeyphraseLimitReached:y().func.isRequired,setRequestSucceeded:y().func.isRequired,setTrackedKeyphrases:y().func.isRequired,setHasTrackedAll:y().func.isRequired,trackAll:y().bool,trackedKeyphrases:y().object,websiteId:y().string,permalink:y().string.isRequired,focusKeyphrase:y().string,startAt:y().string,selectedKeyphrases:y().arrayOf(y().string).isRequired,onSelectKeyphrases:y().func.isRequired},ui.defaultProps={isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],trackAll:!1,websiteId:"",focusKeyphrase:""};const mi=ui,gi=(0,qt.compose)([(0,i.withSelect)((e=>{const{getWincherWebsiteId:t,getWincherTrackableKeyphrases:s,getWincherLoginStatus:r,getWincherPermalink:a,getFocusKeyphrase:n,isWincherNewlyAuthenticated:o,shouldWincherTrackAll:i}=e("yoast-seo/editor");return{focusKeyphrase:n(),keyphrases:s(),isLoggedIn:r(),trackAll:i(),websiteId:t(),isNewlyAuthenticated:o(),permalink:a()}})),(0,i.withDispatch)((e=>{const{setWincherNewRequest:t,setWincherRequestSucceeded:s,setWincherRequestFailed:r,setWincherSetKeyphraseLimitReached:a,setWincherTrackedKeyphrases:n,setWincherTrackingForKeyphrase:o,setWincherTrackAllKeyphrases:i,unsetWincherTrackingForKeyphrase:l}=e("yoast-seo/editor");return{newRequest:()=>{t()},setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{r(e)},setKeyphraseLimitReached:e=>{a(e)},addTrackedKeyphrase:e=>{o(e)},removeTrackedKeyphrase:e=>{l(e)},setTrackedKeyphrases:e=>{n(e)},setHasTrackedAll:()=>{i(!1)}}}))])(mi),hi=(0,Vt.makeOutboundLink)(),yi=(0,Vt.makeOutboundLink)(),fi=()=>{const e=(0,m.sprintf)(/* translators: %1$s expands to a link to Wincher, %2$s expands to a link to the keyphrase tracking article on Yoast.com */
(0,m.__)("With %1$s you can track the ranking position of your page in the search results based on your keyphrase(s). %2$s","wordpress-seo"),"{{wincherLink/}}","{{wincherReadMoreLink/}}");return(0,c.createElement)("p",null,(0,$n.Z)({mixedString:e,components:{wincherLink:(0,c.createElement)(hi,{href:wpseoAdminGlobalL10n["links.wincher.website"]},"Wincher"),wincherReadMoreLink:(0,c.createElement)(yi,{href:wpseoAdminL10n["shortlinks.wincher.seo_performance"]},(0,m.__)("Read more about keyphrase tracking with Wincher","wordpress-seo"))}}))},wi=()=>(0,c.createElement)(Kt.Alert,{type:"error"},(0,m.__)("No keyphrase has been set. Please set a keyphrase first.","wordpress-seo")),bi=()=>(0,c.createElement)(Kt.Alert,{type:"info"},(0,m.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,m.__)("Automatic tracking of keyphrases is enabled. Your keyphrase(s) will automatically be tracked by %s when you publish your post.","wordpress-seo"),"Wincher"));class Ei{constructor(e,t={},s={}){this.url=e,this.origin=new URL(e).origin,this.eventHandlers=Object.assign({success:{type:"",callback:()=>{}},error:{type:"",callback:()=>{}}},t),this.options=Object.assign({height:570,width:340,title:""},s),this.popup=null,this.createPopup=this.createPopup.bind(this),this.messageHandler=this.messageHandler.bind(this),this.getPopup=this.getPopup.bind(this)}createPopup(){const{height:e,width:t,title:s}=this.options,r=["top="+(window.top.outerHeight/2+window.top.screenY-e/2),"left="+(window.top.outerWidth/2+window.top.screenX-t/2),"width="+t,"height="+e,"resizable=1","scrollbars=1","status=0"];this.popup&&!this.popup.closed||(this.popup=window.open(this.url,s,r.join(","))),this.popup&&this.popup.focus(),window.addEventListener("message",this.messageHandler,!1)}async messageHandler(e){const{data:t,source:s,origin:r}=e;r===this.origin&&this.popup===s&&(t.type===this.eventHandlers.success.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.success.callback(t)),t.type===this.eventHandlers.error.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.error.callback(t)))}getPopup(){return this.popup}isClosed(){return!this.popup||this.popup.closed}focus(){this.isClosed()||this.popup.focus()}}const _i=e=>{const t=(0,m.sprintf)(/* translators: %s expands to a link to open the Wincher login popup. */
(0,m.__)("It seems like something went wrong when retrieving your website's data. Please %s and try again.","wordpress-seo"),"{{reconnectToWincher/}}","Wincher");return(0,c.createElement)(Kt.Alert,{type:"error",className:e.className},(0,$n.Z)({mixedString:t,components:{reconnectToWincher:(0,c.createElement)("a",{href:"#",onClick:t=>{t.preventDefault(),e.onReconnect()}},(0,m.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,m.__)("reconnect to %s","wordpress-seo"),"Wincher"))}}))};_i.propTypes={onReconnect:y().func.isRequired,className:y().string},_i.defaultProps={className:""};const vi=_i,ki=()=>(0,c.createElement)(Kt.Alert,{type:"error"},(0,m.__)("Before you can track your SEO performance make sure to set either the post’s title and save it as a draft or manually set the post’s slug.","wordpress-seo")),xi=window.yoast["chart.js"],Si="label";function Ti(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function Ri(e,t){e.labels=t}function Ci(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Si;const r=[];e.datasets=t.map((t=>{const a=e.datasets.find((e=>e[s]===t[s]));return a&&t.data&&!r.includes(a)?(r.push(a),Object.assign(a,t),a):{...t}}))}function Ii(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Si;const s={labels:[],datasets:[]};return Ri(s,e.labels),Ci(s,e.datasets,t),s}function Pi(e,t){const{height:s=150,width:r=300,redraw:a=!1,datasetIdKey:n,type:o,data:i,options:l,plugins:d=[],fallbackContent:p,updateMode:u,...m}=e,g=(0,c.useRef)(null),h=(0,c.useRef)(),y=()=>{g.current&&(h.current=new xi.Chart(g.current,{type:o,data:Ii(i,n),options:l&&{...l},plugins:d}),Ti(t,h.current))},f=()=>{Ti(t,null),h.current&&(h.current.destroy(),h.current=null)};return(0,c.useEffect)((()=>{!a&&h.current&&l&&function(e,t){const s=e.options;s&&t&&Object.assign(s,t)}(h.current,l)}),[a,l]),(0,c.useEffect)((()=>{!a&&h.current&&Ri(h.current.config.data,i.labels)}),[a,i.labels]),(0,c.useEffect)((()=>{!a&&h.current&&i.datasets&&Ci(h.current.config.data,i.datasets,n)}),[a,i.datasets]),(0,c.useEffect)((()=>{h.current&&(a?(f(),setTimeout(y)):h.current.update(u))}),[a,l,i.labels,i.datasets,u]),(0,c.useEffect)((()=>{h.current&&(f(),setTimeout(y))}),[o]),(0,c.useEffect)((()=>(y(),()=>f())),[]),c.createElement("canvas",Object.assign({ref:g,role:"img",height:s,width:r},m),p)}const Li=(0,c.forwardRef)(Pi);function Ai(e,t){return xi.Chart.register(t),(0,c.forwardRef)(((t,s)=>c.createElement(Li,Object.assign({},t,{ref:s,type:e}))))}const Oi=Ai("line",xi.LineController),Fi={datetime:"MMM D, YYYY, h:mm:ss a",millisecond:"h:mm:ss.SSS a",second:"h:mm:ss a",minute:"h:mm a",hour:"hA",day:"MMM D",week:"ll",month:"MMM YYYY",quarter:"[Q]Q - YYYY",year:"YYYY"};xi._adapters._date.override("function"==typeof bo()?{_id:"moment",formats:function(){return Fi},parse:function(e,t){return"string"==typeof e&&"string"==typeof t?e=bo()(e,t):e instanceof bo()||(e=bo()(e)),e.isValid()?e.valueOf():null},format:function(e,t){return bo()(e).format(t)},add:function(e,t,s){return bo()(e).add(t,s).valueOf()},diff:function(e,t,s){return bo()(e).diff(bo()(t),s)},startOf:function(e,t,s){return e=bo()(e),"isoWeek"===t?(s=Math.trunc(Math.min(Math.max(0,s),6)),e.isoWeekday(s).startOf("day").valueOf()):e.startOf(t).valueOf()},endOf:function(e,t){return bo()(e).endOf(t).valueOf()}}:{}),Math.PI,Number.POSITIVE_INFINITY,Math.log10,Math.sign,"undefined"==typeof window||window.requestAnimationFrame,new Map,Object.create(null),Object.create(null),Number.EPSILON;const Di=["top","right","bottom","left"];function Mi(e,t,s){const r={};s=s?"-"+s:"";for(let a=0;a<4;a++){const n=Di[a];r[n]=parseFloat(e[t+"-"+n+s])||0}return r.width=r.left+r.right,r.height=r.top+r.bottom,r}!function(){let e=!1;try{const t={get passive(){return e=!0,!1}};window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch(e){}}(),xi.Chart.register(xi.CategoryScale,xi.LineController,xi.LineElement,xi.PointElement,xi.LinearScale,xi.TimeScale,xi.Legend,xi.Tooltip);const Ni=["#ff983b","#ffa3f7","#3798ff","#ff3b3b","#acce81","#b51751","#3949ab","#26c6da","#ccb800","#de66ff","#4db6ac","#ffab91","#45f5f1","#77f210","#90a4ae","#ffd54f","#006b5e","#8ec7d2","#b1887c","#cc9300"];function qi({datasets:e,isChartShown:t,keyphrases:s}){if(!t)return null;const r=(0,u.useMemo)((()=>Object.fromEntries([...s].sort().map(((e,t)=>[e,Ni[t%Ni.length]])))),[s]),a=e.map((e=>{const t=r[e.label];return{...e,data:e.data.map((({datetime:e,value:t})=>({x:e,y:t}))),lineTension:0,pointRadius:1,pointHoverRadius:4,borderWidth:2,pointHitRadius:6,backgroundColor:t,borderColor:t}})).filter((e=>!1!==e.selected));return(0,c.createElement)(Oi,{height:100,data:{datasets:a},options:{plugins:{legend:{display:!0,position:"bottom",labels:{color:"black",usePointStyle:!0,boxHeight:7,boxWidth:7},onClick:f.noop},tooltip:{enabled:!0,callbacks:{title:e=>bo()(e[0].raw.x).utc().format("YYYY-MM-DD")},titleAlign:"center",mode:"xPoint",position:"nearest",usePointStyle:!0,boxHeight:7,boxWidth:7,boxPadding:2}},scales:{x:{bounds:"ticks",type:"time",time:{unit:"day",minUnit:"day"},grid:{display:!1},ticks:{autoSkipPadding:50,maxRotation:0,color:"black"}},y:{bounds:"ticks",offset:!0,reverse:!0,ticks:{precision:0,color:"black"},max:101}}}})}xi.Interaction.modes.xPoint=(e,t,s,r)=>{const a=function(e,t){if("native"in e)return e;const{canvas:s,currentDevicePixelRatio:r}=t,a=(m=s).ownerDocument.defaultView.getComputedStyle(m,null),n="border-box"===a.boxSizing,o=Mi(a,"padding"),i=Mi(a,"border","width"),{x:l,y:c,box:d}=function(e,t){const s=e.touches,r=s&&s.length?s[0]:e,{offsetX:a,offsetY:n}=r;let o,i,l=!1;if(((e,t,s)=>(e>0||t>0)&&(!s||!s.shadowRoot))(a,n,e.target))o=a,i=n;else{const e=t.getBoundingClientRect();o=r.clientX-e.left,i=r.clientY-e.top,l=!0}return{x:o,y:i,box:l}}(e,s),p=o.left+(d&&i.left),u=o.top+(d&&i.top);var m;let{width:g,height:h}=t;return n&&(g-=o.width+i.width,h-=o.height+i.height),{x:Math.round((l-p)/g*s.width/r),y:Math.round((c-u)/h*s.height/r)}}(t,e);let n=[];if(xi.Interaction.evaluateInteractionItems(e,"x",a,((e,t,s)=>{e.inXRange(a.x,r)&&n.push({element:e,datasetIndex:t,index:s})})),0===n.length)return n;const o=n.reduce(((e,t)=>Math.abs(a.x-e.element.x)<Math.abs(a.x-t.element.x)?e:t)).element.x;return n=n.filter((e=>e.element.x===o)),n.some((e=>Math.abs(e.element.y-a.y)<10))?n:[]},qi.propTypes={datasets:y().arrayOf(y().shape({label:y().string.isRequired,data:y().arrayOf(y().shape({datetime:y().string.isRequired,value:y().number.isRequired})).isRequired,selected:y().bool})).isRequired,isChartShown:y().bool.isRequired,keyphrases:y().array.isRequired};const Ui=({response:e,onLogin:t})=>[401,403,404].includes(e.status)?(0,c.createElement)(vi,{onReconnect:t}):(0,c.createElement)(ko,null);Ui.propTypes={response:y().object.isRequired,onLogin:y().func.isRequired};const $i=({isSuccess:e,response:t,allKeyphrasesMissRanking:s,onLogin:r,keyphraseLimitReached:a,limit:n})=>a?(0,c.createElement)(vo,{limit:n}):(0,f.isEmpty)(t)||e?s?(0,c.createElement)(Bo,null):null:(0,c.createElement)(Ui,{response:t,onLogin:r});$i.propTypes={isSuccess:y().bool.isRequired,allKeyphrasesMissRanking:y().bool.isRequired,response:y().object,onLogin:y().func.isRequired,keyphraseLimitReached:y().bool.isRequired,limit:y().number.isRequired},$i.defaultProps={response:{}};let Bi=null;const Wi=async e=>{if(Bi&&!Bi.isClosed())return void Bi.focus();const{url:t}=await async function(){return await Ro({path:"yoast/v1/wincher/authorization-url",method:"GET"})}();Bi=new Ei(t,{success:{type:"wincher:oauth:success",callback:t=>(async(e,t)=>{const{onAuthentication:s,setRequestSucceeded:r,setRequestFailed:a,keyphrases:n,addTrackedKeyphrase:o,setKeyphraseLimitReached:i}=e;await To((()=>async function(e){const{code:t,websiteId:s}=e;return await Ro({path:"yoast/v1/wincher/authenticate",method:"POST",data:{code:t,websiteId:s}})}(t)),(async e=>{s(!0,!0,t.websiteId.toString()),r(e);const l=(Array.isArray(n)?n:[n]).map((e=>e.toLowerCase()));await To((()=>Co(l)),(e=>{r(e),o(e.results)}),(e=>{400===e.status&&e.limit&&i(e.limit),a(e)}),201);const c=Bi.getPopup();c&&c.close()}),(async e=>a(e)))})(e,t)},error:{type:"wincher:oauth:error",callback:()=>e.onAuthentication(!1,!1)}},{title:"Wincher_login",width:500,height:700}),Bi.createPopup()},Yi=e=>e.isLoggedIn?null:(0,c.createElement)("p",null,(0,c.createElement)(Kt.NewButton,{onClick:e.onLogin,variant:"primary"},(0,m.sprintf)(/* translators: %s expands to Wincher */
(0,m.__)("Connect with %s","wordpress-seo"),"Wincher")));Yi.propTypes={isLoggedIn:y().bool.isRequired,onLogin:y().func.isRequired};const zi=ht().div`
	p {
		margin: 1em 0;
	}
`,Ki=ht().div`
	${e=>e.isDisabled&&"\n\t\topacity: .5;\n\t\tpointer-events: none;\n\t"};
`,ji=ht().div`
	font-weight: var(--yoast-font-weight-bold);
	color: var(--yoast-color-label);
	font-size: var(--yoast-font-size-default);
`,Hi=ht().div.attrs({className:"yoast-field-group"})`
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 14px;
`,Vi=ht().div`
	margin: 8px 0;
`,Gi=bo().utc().startOf("day"),Zi=[{name:(0,m.__)("Last day","wordpress-seo"),value:bo()(Gi).subtract(1,"days").format(),defaultIndex:1},{name:(0,m.__)("Last week","wordpress-seo"),value:bo()(Gi).subtract(1,"week").format(),defaultIndex:2},{name:(0,m.__)("Last month","wordpress-seo"),value:bo()(Gi).subtract(1,"month").format(),defaultIndex:3},{name:(0,m.__)("Last year","wordpress-seo"),value:bo()(Gi).subtract(1,"year").format(),defaultIndex:0}],Qi=e=>{const{onSelect:t,selected:s,options:r,isLoggedIn:a}=e;return a?r.length<1?null:(0,c.createElement)("select",{className:"components-select-control__input",id:"wincher-period-picker",value:(null==s?void 0:s.value)||r[0].value,onChange:t},r.map((e=>(0,c.createElement)("option",{key:e.name,value:e.value},e.name)))):null};Qi.propTypes={onSelect:y().func.isRequired,selected:y().object,options:y().array.isRequired,isLoggedIn:y().bool.isRequired};const Xi=e=>{const{trackedKeyphrases:t,isLoggedIn:s,keyphrases:r,shouldTrackAll:a,permalink:n,historyDaysLimit:o}=e;if(!n&&s)return(0,c.createElement)(ki,null);if(0===r.length)return(0,c.createElement)(wi,null);const i=bo()(Gi).subtract(o,"days"),l=Zi.filter((e=>bo()(e.value).isSameOrAfter(i))),d=(0,f.orderBy)(l,(e=>e.defaultIndex),"desc")[0],[p,g]=(0,u.useState)(d),[h,y]=(0,u.useState)([]),w=h.length>0,b=(0,qt.usePrevious)(t);(0,u.useEffect)((()=>{if(!(0,f.isEmpty)(t)&&(0,f.difference)(Object.keys(t),Object.keys(b||[])).length){const e=Object.values(t).map((e=>e.keyword));y(e)}}),[t,b]),(0,u.useEffect)((()=>{g(d)}),[null==d?void 0:d.name]);const E=(0,u.useCallback)((e=>{const t=Zi.find((t=>t.value===e.target.value));t&&g(t)}),[g]),_=(0,u.useMemo)((()=>(0,f.isEmpty)(h)||(0,f.isEmpty)(t)?[]:Object.values(t).filter((e=>{var t;return!(null==e||null===(t=e.position)||void 0===t||!t.history)})).map((e=>{var t;return{label:e.keyword,data:e.position.history,selected:h.includes(e.keyword)&&!(0,f.isEmpty)(null===(t=e.position)||void 0===t?void 0:t.history)}}))),[h,t]);return(0,c.createElement)(Ki,{isDisabled:!s},(0,c.createElement)("p",null,(0,m.__)("You can enable / disable tracking the SEO performance for each keyphrase below.","wordpress-seo")),s&&a&&(0,c.createElement)(bi,null),(0,c.createElement)(Hi,null,(0,c.createElement)(Qi,{selected:p,onSelect:E,options:l,isLoggedIn:s})),(0,c.createElement)(Vi,null,(0,c.createElement)(qi,{isChartShown:w,datasets:_,keyphrases:r})),(0,c.createElement)(gi,{startAt:null==p?void 0:p.value,selectedKeyphrases:h,onSelectKeyphrases:y,trackedKeyphrases:t}))};function Ji(e){const{isNewlyAuthenticated:t,isLoggedIn:s}=e,r=(0,u.useCallback)((()=>{Wi(e)}),[Wi,e]),a=Fo(s);return(0,c.createElement)(zi,null,t&&(0,c.createElement)($o,null),s&&(0,c.createElement)(Uo,{trackingInfo:a}),(0,c.createElement)(ji,null,(0,m.__)("SEO performance","wordpress-seo"),(0,c.createElement)(Kt.HelpIcon,{linkTo:wpseoAdminL10n["shortlinks.wincher.seo_performance"]
/* translators: Hidden accessibility text. */,linkText:(0,m.__)("Learn more about the SEO performance feature.","wordpress-seo")})),(0,c.createElement)(fi,null),(0,c.createElement)(Yi,{isLoggedIn:s,onLogin:r}),(0,c.createElement)($i,{...e,onLogin:r}),(0,c.createElement)(Xi,{...e,historyDaysLimit:(null==a?void 0:a.historyDays)||31}))}Xi.propTypes={trackedKeyphrases:y().object,keyphrases:y().array.isRequired,isLoggedIn:y().bool.isRequired,shouldTrackAll:y().bool.isRequired,permalink:y().string.isRequired,historyDaysLimit:y().number},Ji.propTypes={trackedKeyphrases:y().object,addTrackedKeyphrase:y().func.isRequired,isLoggedIn:y().bool,isNewlyAuthenticated:y().bool,keyphrases:y().array,response:y().object,shouldTrackAll:y().bool,permalink:y().string,historyDaysLimit:y().number},Ji.defaultProps={trackedKeyphrases:null,isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],response:{},shouldTrackAll:!1,permalink:"",historyDaysLimit:0};const el=(0,qt.compose)([(0,i.withSelect)((e=>{const{isWincherNewlyAuthenticated:t,getWincherKeyphraseLimitReached:s,getWincherLimit:r,getWincherHistoryDaysLimit:a,getWincherLoginStatus:n,getWincherRequestIsSuccess:o,getWincherRequestResponse:i,getWincherTrackableKeyphrases:l,getWincherTrackedKeyphrases:c,getWincherAllKeyphrasesMissRanking:d,getWincherPermalink:p,shouldWincherAutomaticallyTrackAll:u}=e("yoast-seo/editor");return{keyphrases:l(),trackedKeyphrases:c(),allKeyphrasesMissRanking:d(),isLoggedIn:n(),isNewlyAuthenticated:t(),isSuccess:o(),keyphraseLimitReached:s(),limit:r(),response:i(),shouldTrackAll:u(),permalink:p(),historyDaysLimit:a()}})),(0,i.withDispatch)((e=>{const{setWincherWebsiteId:t,setWincherRequestSucceeded:s,setWincherRequestFailed:r,setWincherTrackingForKeyphrase:a,setWincherSetKeyphraseLimitReached:n,setWincherLoginStatus:o}=e("yoast-seo/editor");return{setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{r(e)},addTrackedKeyphrase:e=>{a(e)},setKeyphraseLimitReached:e=>{n(e)},onAuthentication:(e,s,r)=>{t(r),o(e,s)}}}))])(Ji),tl=ht()(mo)`
	width: 18px;
	height: 18px;
	margin: 3px;
`;function sl(e){const{keyphrases:t,onNoKeyphraseSet:s,onOpen:r,location:a}=e;if(!t.length){let e=document.querySelector("#focus-keyword-input-metabox");return e||(e=document.querySelector("#focus-keyword-input-sidebar")),e.focus(),void s()}r(a)}function rl(e){const{location:t,whichModalOpen:s,shouldCloseOnClickOutside:r}=e,a=(0,u.useCallback)((()=>{sl(e)}),[sl,e]),n=(0,m.__)("Track SEO performance","wordpress-seo"),o=xr();return(0,c.createElement)(u.Fragment,null,s===t&&(0,c.createElement)(ds,{title:n,onRequestClose:e.onClose,icon:(0,c.createElement)(fo,null),additionalClassName:"yoast-wincher-seo-performance-modal yoast-gutenberg-modal__no-padding",shouldCloseOnClickOutside:r},(0,c.createElement)(os,{className:"yoast-gutenberg-modal__content yoast-wincher-seo-performance-modal__content"},(0,c.createElement)(el,null))),"sidebar"===t&&(0,c.createElement)(Ht,{id:`wincher-open-button-${t}`,title:n,SuffixHeroIcon:(0,c.createElement)(tl,{className:"yst-text-slate-500",...o}),onClick:a}),"metabox"===t&&(0,c.createElement)("div",{className:"yst-root"},(0,c.createElement)(zt,{id:`wincher-open-button-${t}`,onClick:a},(0,c.createElement)(zt.Text,null,n),(0,c.createElement)(mo,{className:"yst-h-5 yst-w-5 yst-text-slate-500",...o}))))}rl.propTypes={location:y().string,whichModalOpen:y().oneOf(["none","metabox","sidebar","postpublish"]),shouldCloseOnClickOutside:y().bool,keyphrases:y().array.isRequired,onNoKeyphraseSet:y().func.isRequired,onOpen:y().func.isRequired,onClose:y().func.isRequired},rl.defaultProps={location:"",whichModalOpen:"none",shouldCloseOnClickOutside:!0};const al=(0,qt.compose)([(0,i.withSelect)((e=>{const{getWincherModalOpen:t,getWincherTrackableKeyphrases:s}=e("yoast-seo/editor");return{keyphrases:s(),whichModalOpen:t()}})),(0,i.withDispatch)((e=>{const{setWincherOpenModal:t,setWincherDismissModal:s,setWincherNoKeyphrase:r}=e("yoast-seo/editor");return{onOpen:e=>{t(e)},onClose:()=>{s()},onNoKeyphraseSet:()=>{r()}}}))])(rl),nl=e=>(0,c.createElement)(ns,{title:(0,m.__)("Reach a wider audience","wordpress-seo"),description:(0,m.__)("Get help optimizing for up to 5 related keyphrases. This helps you reach a wider audience and get more traffic.","wordpress-seo"),benefitsTitle:(0,m.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,m.__)("%s also gives you:","wordpress-seo"),"Yoast SEO Premium"),benefits:oe(),upsellButtonText:(0,m.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,m.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:e.buyLink,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,m.__)("1 year free support and updates included!","wordpress-seo")});nl.propTypes={buyLink:y().string.isRequired};const ol=nl,il=()=>{const[e,,,t,s]=(0,g.useToggleState)(!1),r=(0,u.useContext)(yt.LocationContext),{locationContext:a}=(0,yt.useRootContext)(),n=(0,g.useSvgAria)(),o=wpseoAdminL10n["sidebar"===r.toLowerCase()?"shortlinks.upsell.sidebar.additional_button":"shortlinks.upsell.metabox.additional_button"];return(0,c.createElement)(c.Fragment,null,e&&(0,c.createElement)(ds,{title:(0,m.__)("Add related keyphrases","wordpress-seo"),onRequestClose:s,additionalClassName:"",id:"yoast-additional-keyphrases-modal",className:`${ls} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,shouldCloseOnClickOutside:!0},(0,c.createElement)(is,null,(0,c.createElement)(ol,{buyLink:(0,$t.addQueryArgs)(o,{context:a})}))),"sidebar"===r&&(0,c.createElement)(Ht,{id:"yoast-additional-keyphrase-modal-open-button",title:(0,m.__)("Add related keyphrase","wordpress-seo"),prefixIcon:{icon:"plus",color:Ea.colors.$color_grey_medium_dark},onClick:t},(0,c.createElement)("div",{className:"yst-root"},(0,c.createElement)(g.Badge,{size:"small",variant:"upsell"},(0,c.createElement)(Ut,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...n})))),"metabox"===r&&(0,c.createElement)("div",{className:"yst-root"},(0,c.createElement)(zt,{id:"yoast-additional-keyphrase-metabox-modal-open-button",onClick:t},(0,c.createElement)(Kt.SvgIcon,{icon:"plus",color:Ea.colors.$color_grey_medium_dark}),(0,c.createElement)(zt.Text,null,(0,m.__)("Add related keyphrase","wordpress-seo")),(0,c.createElement)(g.Badge,{size:"small",variant:"upsell"},(0,c.createElement)(Ut,{className:"yst-w-2.5 yst-h-2.5 yst-me-1 yst-shrink-0",...n}),(0,c.createElement)("span",null,"Premium")))))};function ll({isLoading:e,onLoad:t,settings:s}){const r=(({webinarIntroUrl:e})=>{const{shouldShow:t}=vs(),s=(e=>{for(const t of e)if(null!=t&&t.getIsEligible())return t;return null})([{getIsEligible:()=>t,component:xs},{getIsEligible:vr,component:()=>(0,c.createElement)(_r,{hasIcon:!1,image:null,url:e})},{getIsEligible:()=>!0,component:()=>(0,c.createElement)(bs,{hasIcon:!1})}]);return(null==s?void 0:s.component)||null})({webinarIntroUrl:(0,i.useSelect)((e=>e("yoast-seo/editor").selectLink("https://yoa.st/webinar-intro-elementor")),[])});return(0,u.useEffect)((()=>{setTimeout((()=>{e&&t()}))})),e?null:(0,c.createElement)(u.Fragment,null,(0,c.createElement)(p.Fill,{name:"YoastElementor"},(0,c.createElement)(Gr,{renderPriority:1},(0,c.createElement)(Hr,null),r&&(0,c.createElement)("div",{className:"yst-inline-block yst-px-1.5"},(0,c.createElement)(r,null))),s.isKeywordAnalysisActive&&(0,c.createElement)(Gr,{renderPriority:8},(0,c.createElement)(us.KeywordInput,{isSEMrushIntegrationActive:s.isSEMrushIntegrationActive}),!window.wpseoScriptData.metabox.isPremium&&(0,c.createElement)(p.Fill,{name:"YoastRelatedKeyphrases"},(0,c.createElement)(uo,null))),s.isKeywordAnalysisActive&&(0,c.createElement)(Gr,{renderPriority:10},(0,c.createElement)(u.Fragment,null,(0,c.createElement)(us.SeoAnalysis,{shouldUpsell:s.shouldUpsell,shouldUpsellWordFormRecognition:s.isWordFormRecognitionActive,shouldUpsellHighlighting:s.shouldUpsell}),s.shouldUpsell&&(0,c.createElement)(Nn,null))),s.isContentAnalysisActive&&(0,c.createElement)(Gr,{renderPriority:15},(0,c.createElement)(us.ReadabilityAnalysis,{shouldUpsell:s.shouldUpsell,shouldUpsellHighlighting:s.shouldUpsell})),s.isInclusiveLanguageAnalysisActive&&(0,c.createElement)(Gr,{renderPriority:19},(0,c.createElement)(us.InclusiveLanguageAnalysis,{shouldUpsellHighlighting:s.shouldUpsell})),s.isKeywordAnalysisActive&&(0,c.createElement)(Gr,{key:"additional-keywords-upsell",renderPriority:22},s.shouldUpsell&&(0,c.createElement)(il,null)),s.isKeywordAnalysisActive&&s.isWincherIntegrationActive&&(0,c.createElement)(Gr,{key:"wincher-seo-performance",renderPriority:23},(0,c.createElement)(al,{location:"sidebar",shouldCloseOnClickOutside:!1})),s.shouldUpsell&&(0,c.createElement)(Gr,{key:"internal-linking-suggestions-upsell",renderPriority:24},(0,c.createElement)(ps,null)),(0,c.createElement)(Gr,{renderPriority:25},(0,c.createElement)(ga,null)),(s.useOpenGraphData||s.useTwitterData)&&(0,c.createElement)(Gr,{key:"social-appearance",renderPriority:26},(0,c.createElement)(An,{useOpenGraphData:s.useOpenGraphData,useTwitterData:s.useTwitterData})),s.displaySchemaSettings&&(0,c.createElement)(Gr,{renderPriority:28},(0,c.createElement)(Un,{title:(0,m.__)("Schema","wordpress-seo")},(0,c.createElement)(Jn,null))),s.displayAdvancedTab&&(0,c.createElement)(Gr,{renderPriority:29},(0,c.createElement)(Un,{title:(0,m.__)("Advanced","wordpress-seo"),buttonId:"yoast-seo-elementor-advanced-button"},(0,c.createElement)(io,{location:"sidebar"}))),s.isCornerstoneActive&&(0,c.createElement)(Gr,{renderPriority:30},(0,c.createElement)(gs,null)),s.isInsightsEnabled&&(0,c.createElement)(Gr,{renderPriority:32},(0,c.createElement)(Kr,{location:"elementor"}))))}ll.propTypes={isLoading:y().bool.isRequired,onLoad:y().func.isRequired,settings:y().object.isRequired};const cl=(0,qt.compose)([(0,i.withSelect)((e=>{const{getPreferences:t,getSnippetEditorIsLoading:s}=e("yoast-seo/editor");return{settings:t(),isLoading:s()}})),(0,i.withDispatch)((e=>{const{loadSnippetEditorData:t}=e("yoast-seo/editor");return{onLoad:t}}))])(ll),dl="yoast-elementor-react-tab",pl="yoast-seo-tab",ul="Yoast SEO",ml="panel/page-settings",gl=()=>{const{settings:e}=elementor.documents.getCurrent().config;e.tabs[pl]||(e.tabs=(0,f.reduce)(e.tabs,((e,t,s)=>(e[s]=t,"settings"===s&&(e[pl]=ul),e)),{})),$e.components.get(ml).hasTab(pl)||$e.components.get(ml).addTab(pl,{title:ul})};let hl=!1,yl=!1;const fl=(0,f.debounce)(Ot,500,{trailing:!0}),wl=()=>{const e=document.getElementById("yoast-form");if(!e)return void console.error("Yoast form not found!");window.YoastSEO=window.YoastSEO||{},window.YoastSEO._registerReactComponent=vt,(()=>{const e=document.createElement("div");e.id="yoast-elementor-react-root",document.body.appendChild(e),function(e,t){const s=ke();Et=(0,u.createRef)();const r={isRtl:s.isRtl};(0,u.render)((0,c.createElement)(wt,{theme:r,location:"sidebar"},(0,c.createElement)(p.SlotFillProvider,null,(0,c.createElement)(u.Fragment,null,t,(0,c.createElement)(_t,{ref:Et})))),document.getElementById(e)),bt.forEach((e=>{Et.current.registerComponent(e.key,e.Component)}))}(e.id,(0,c.createElement)(yt.Root,{context:{locationContext:"elementor-sidebar"}},(0,c.createElement)(Dt,{id:dl},(0,c.createElement)(Nt,null),(0,c.createElement)(cl,null))))})(),Ct("editor/documents/load","yoast-seo/register-tab",gl,(({config:e})=>It(e.id))),$e.routes.on("run:after",((e,t)=>{t===`${ml}/${pl}`&&(()=>{if(document.getElementById(dl))return;const e=document.getElementById("elementor-panel-page-settings-controls");if(!e)return;const t=e.querySelector(".elementor-control-yoast-seo-section");t&&(t.style.display="none");const s=document.createElement("div");s.id=dl,s.className="yoast yoast-elementor-panel__fills",e.appendChild(s)})()})),gl(),elementor.getPanelView().getPages("menu").view.addItem({name:"yoast",icon:"yoast yoast-element-menu-icon",title:ul,type:"page",callback:()=>{try{$e.route(`${ml}/${pl}`)}catch(e){$e.route(`${ml}/settings`),$e.route(`${ml}/${pl}`)}}},"more"),((e,t=500)=>{const s=(0,f.debounce)(e,t,{trailing:!0});Tt("document/elements/settings","yoast-seo/document/post-status",(({settings:e})=>s(e.post_status)),(({container:e,settings:t})=>{var s;return!!It((null==e||null===(s=e.document)||void 0===s?void 0:s.id)||elementor.documents.getCurrent().id)&&Boolean(null==t?void 0:t.post_status)}))})((()=>fl(hl)));const t=((e,t=500)=>{const s={},r=Array.from(e.querySelectorAll("input[name^='yoast']")),a=r.reduce(((e,{name:t,value:s})=>(e[t]=s,e)),{}),n={...a},o=new MutationObserver((0,f.debounce)((e=>{const t=[];e.forEach((e=>{"value"===e.attributeName&&e.target.name.startsWith("yoast")&&e.target.value!==a[e.target.name]&&(t.push({input:e.target,name:e.target.name,value:e.target.value,previousValue:a[e.target.name],snapshotValue:n[e.target.name]}),a[e.target.name]=e.target.value)})),t.length>0&&(0,f.forEach)(s,(e=>e(t)))}),t));return{start:()=>o.observe(e,{attributes:!0,subtree:!0}),stop:()=>o.disconnect(),subscribe:e=>{const t=(0,f.uniqueId)("yoast-form-listener");return s[t]=e,()=>delete s[t]},takeSnapshot:()=>{r.forEach((({name:e,value:t})=>{n[e]=t}))},restoreSnapshot:()=>{r.forEach((e=>{e.value=n[e.name],a[e.name]=n[e.name]}))}}})(e);t.subscribe((e=>{e.some((e=>{return t=e.name,s=e.value,r=e.previousValue,!(Lt.includes(t)||At.includes(t)&&((e,t)=>{if(t===e)return!0;if(""===t||""===e)return!1;let s,r;try{s=JSON.parse(t),r=JSON.parse(e)}catch(e){return!0}return s.length===r.length&&s.every(((e,t)=>e.keyword===r[t].keyword))})(r,s)||s===r);var t,s,r}))&&(hl=!0,fl(hl),$e.internal("document/save/set-is-modified",{status:!0}))})),t.start(),Rt("editor/documents/open","yoast-seo/document/open",(()=>{YoastSEO.store._freeze(!1),t.start(),(0,l.doAction)("yoast.elementor.toggleFreeze",{isFreeze:!1,isDiscard:!1})}),(({id:e})=>It(e))),Tt("editor/documents/close","yoast-seo/document/close",(0,f.throttle)((({mode:e})=>{t.stop(),"discard"===e&&(YoastSEO.store._restoreSnapshot(),t.restoreSnapshot(),hl=!1,Ot(hl));const s=()=>{YoastSEO.store._freeze(!0),(0,l.doAction)("yoast.elementor.toggleFreeze",{isFreeze:!0,isDiscard:"discard"===e}),(0,l.removeAction)("yoast.elementor.save.success","yoast/yoast-seo/finishClosingDocument"),(0,l.removeAction)("yoast.elementor.save.failure","yoast/yoast-seo/finishClosingDocument")};if(yl)return(0,l.addAction)("yoast.elementor.save.success","yoast/yoast-seo/finishClosingDocument",s),void(0,l.addAction)("yoast.elementor.save.failure","yoast/yoast-seo/finishClosingDocument",s);s()}),500,{leading:!0,trailing:!1}),(({id:e})=>It(e))),Ct("document/save/save","yoast-seo/document/save",(async({document:s})=>{if(yl=!0,!It(s.id))return;if(s.id!==elementor.config.document.revisions.current_id)return;hl=!1;const{success:r,formData:a,data:n,xhr:o}=await(e=>new Promise((t=>{const s=jQuery(e).serializeArray().reduce(((e,{name:t,value:s})=>(e[t]=s,e)),{});jQuery.post(e.getAttribute("action"),s).done((({success:e,data:r},a,n)=>t({success:e,formData:s,data:r,xhr:n}))).fail((e=>t({success:!1,formData:s,xhr:e})))})))(e);if(!r)return hl=!0,yl=!1,void(0,l.doAction)("yoast.elementor.save.failure");n.slug&&n.slug!==a.slug&&(0,i.dispatch)("yoast-seo/editor").updateData({slug:n.slug}),(0,i.dispatch)("yoast-seo/editor").setEditorDataSlug(n.slug),Ot(hl),(0,l.doAction)("yoast.elementor.save.success",o),YoastSEO.store._takeSnapshot(),t.takeSnapshot(),yl=!1}),(({document:e})=>It((null==e?void 0:e.id)||elementor.documents.getCurrent().id))),setTimeout((()=>{YoastSEO.store._takeSnapshot(),t.takeSnapshot()}),2e3)},bl=window.yoast.reduxJsToolkit,El="adminUrl",_l=(0,bl.createSlice)({name:El,initialState:"",reducers:{setAdminUrl:(e,{payload:t})=>t}}),vl=(_l.getInitialState,{selectAdminUrl:e=>(0,f.get)(e,El,"")});vl.selectAdminLink=(0,bl.createSelector)([vl.selectAdminUrl,(e,t)=>t],((e,t="")=>{try{return new URL(t,e).href}catch(t){return e}})),_l.actions,_l.reducer;const kl="linkParams",xl=(0,bl.createSlice)({name:kl,initialState:{},reducers:{setLinkParams:(e,{payload:t})=>t}}),Sl=(xl.getInitialState,{selectLinkParam:(e,t,s={})=>(0,f.get)(e,`${kl}.${t}`,s),selectLinkParams:e=>(0,f.get)(e,kl,{})});Sl.selectLink=(0,bl.createSelector)([Sl.selectLinkParams,(e,t)=>t,(e,t,s={})=>s],((e,t,s)=>(0,$t.addQueryArgs)(t,{...e,...s}))),xl.actions,xl.reducer;const Tl=(0,bl.createSlice)({name:"notifications",initialState:{},reducers:{addNotification:{reducer:(e,{payload:t})=>{e[t.id]={id:t.id,variant:t.variant,size:t.size,title:t.title,description:t.description}},prepare:({id:e,variant:t="info",size:s="default",title:r,description:a})=>({payload:{id:e||(0,bl.nanoid)(),variant:t,size:s,title:r||"",description:a}})},removeNotification:(e,{payload:t})=>(0,f.omit)(e,t)}}),Rl=(Tl.getInitialState,Tl.actions,Tl.reducer,"pluginUrl"),Cl=(0,bl.createSlice)({name:Rl,initialState:"",reducers:{setPluginUrl:(e,{payload:t})=>t}}),Il=(Cl.getInitialState,{selectPluginUrl:e=>(0,f.get)(e,Rl,"")});Il.selectImageLink=(0,bl.createSelector)([Il.selectPluginUrl,(e,t,s="images")=>s,(e,t)=>t],((e,t,s)=>[(0,f.trimEnd)(e,"/"),(0,f.trim)(t,"/"),(0,f.trimStart)(s,"/")].join("/"))),Cl.actions,Cl.reducer;const Pl="wistiaEmbedPermission",Ll=(0,bl.createSlice)({name:Pl,initialState:{value:!1,status:ce,error:{}},reducers:{setWistiaEmbedPermissionValue:(e,{payload:t})=>{e.value=Boolean(t)}},extraReducers:e=>{e.addCase(`${Pl}/request`,(e=>{e.status=de})),e.addCase(`${Pl}/success`,((e,{payload:t})=>{e.status="success",e.value=Boolean(t&&t.value)})),e.addCase(`${Pl}/error`,((e,{payload:t})=>{e.status="error",e.value=Boolean(t&&t.value),e.error={code:(0,f.get)(t,"error.code",500),message:(0,f.get)(t,"error.message","Unknown")}}))}}),Al=(Ll.getInitialState,{selectWistiaEmbedPermission:e=>(0,f.get)(e,Pl,{value:!1,status:ce}),selectWistiaEmbedPermissionValue:e=>(0,f.get)(e,[Pl,"value"],!1),selectWistiaEmbedPermissionStatus:e=>(0,f.get)(e,[Pl,"status"],ce),selectWistiaEmbedPermissionError:e=>(0,f.get)(e,[Pl,"error"],{})}),Ol=(Ll.actions,{[Pl]:async({payload:e})=>So()({path:"/yoast/v1/wistia_embed_permission",method:"POST",data:{value:Boolean(e)}})});function Fl({alertKey:e}){return new Promise((t=>wpseoApi.post("alerts/dismiss",{key:e},(()=>t()))))}function Dl({query:e,postId:t}){return new Promise((s=>{wpseoApi.get("meta/search",{query:e,post_id:t},(e=>{s(e.meta)}))}))}Ll.reducer;const Ml=async({countryCode:e,keyphrase:t})=>(So()({path:"yoast/v1/semrush/country_code",method:"POST",data:{country_code:e}}),So()({path:(0,$t.addQueryArgs)("/yoast/v1/semrush/related_keyphrases",{keyphrase:t,country_code:e})})),Nl=Ol[Pl];class ql{static get titleElement(){return document.getElementById(window.wpseoScriptData.isPost?"yoast_wpseo_title":"hidden_wpseo_title")}static get descriptionElement(){return document.getElementById(window.wpseoScriptData.isPost?"yoast_wpseo_metadesc":"hidden_wpseo_desc")}static get slugElement(){return document.getElementById("yoast_wpseo_slug")}static get title(){return ql.titleElement.value}static set title(e){ql.titleElement.value=e}static get description(){return ql.descriptionElement.value}static set description(e){ql.descriptionElement.value=e}static get slug(){return ql.slugElement.value}static set slug(e){ql.slugElement.value=e}}const{UPDATE_DATA:Ul,LOAD_SNIPPET_EDITOR_DATA:$l}=_e.actions;function Bl(e){if(e.hasOwnProperty("title")){let t=e.title;e.title===(0,f.get)(window,"wpseoScriptData.metabox.title_template","")&&(t=""),ql.title=t}if(e.hasOwnProperty("description")){let t=e.description;e.description===(0,f.get)(window,"wpseoScriptData.metabox.metadesc_template","")&&(t=""),ql.description=t}return e.hasOwnProperty("slug")&&(ql.slug=e.slug),{type:Ul,data:e}}const Wl=()=>{const e=(0,f.get)(window,"wpseoScriptData.metabox.title_template",""),t=(0,f.get)(window,"wpseoScriptData.metabox.metadesc_template","");return{type:$l,data:{title:ql.title||e,description:ql.description||t,slug:ql.slug},templates:{title:e,description:t}}},Yl="yoast-measurement-element";function zl(e){let t=document.getElementById(Yl);return t||(t=function(){const e=document.createElement("div");return e.id=Yl,e.style.position="absolute",e.style.left="-9999em",e.style.top=0,e.style.height=0,e.style.overflow="hidden",e.style.fontFamily="arial, sans-serif",e.style.fontSize="20px",e.style.fontWeight="400",document.body.appendChild(e),e}()),t.innerText=e,t.offsetWidth}const{getEditorDataSlug:Kl,getEditorDataTitle:jl,getSnippetEditorDescription:Hl,getSnippetEditorSlug:Vl,getSnippetEditorTitle:Gl}=_e.selectors,Zl=(0,bl.createSelector)([Kl,Vl,jl,()=>(0,f.get)(window,"elementor.documents.currentDocument.id",0)],((e,t,s,r)=>t||e||(0,$t.cleanForSlug)(s)||String(r))),Ql=(0,bl.createSelector)([Gl,Hl,Zl],((e,t,s)=>({title:e,description:t,slug:s}))),{getBaseUrlFromSettings:Xl,getContentLocale:Jl,getEditorDataContent:ec,getFocusKeyphrase:tc,getSnippetEditorDescriptionWithTemplate:sc,getSnippetEditorTitleWithTemplate:rc,getDateFromSettings:ac}=_e.selectors,nc=e=>{let t=rc(e),s=sc(e),r=Zl(e);const a=Xl(e);return t=Vt.strings.stripHTMLTags(Me("data_page_title",t)),s=Vt.strings.stripHTMLTags(Me("data_meta_desc",s)),r=r.trim().replace(/\s+/g,"-"),{text:ec(e),title:t,keyword:tc(e),description:s,locale:Jl(e),titleWidth:zl(t),slug:r,permalink:a+r,date:ac(e)}},oc=e=>{let t=(0,f.get)(e,"editorData.excerpt","");if(""===t){const s="ja"===xe()?80:156;t=ra((0,f.get)(e,"editorData.content",""),s)}return t},ic=e=>(0,f.get)(e,"analysisData.snippet.title",""),lc=e=>(0,f.get)(e,"analysisData.snippet.description",""),cc=()=>(0,f.get)(window,"wpseoScriptData.metabox.title_template",""),dc=()=>(0,f.get)(window,"wpseoScriptData.metabox.title_template_no_fallback",""),pc=()=>(0,f.get)(window,"wpseoScriptData.metabox.social_title_template",""),uc=()=>(0,f.get)(window,"wpseoScriptData.metabox.metadesc_template",""),mc=()=>(0,f.get)(window,"wpseoScriptData.metabox.social_description_template",""),gc=e=>{let t="";return(0,f.get)(e,"snippetEditor.replacementVariables",[]).forEach((e=>{"excerpt"===e.name&&(t=e.value)})),t},hc=e=>(0,f.get)(e,"facebookEditor.title",""),yc=e=>(0,f.get)(e,"facebookEditor.description",""),fc=(0,bl.createSelector)([pc,ic,dc,cc],((...e)=>e.find(Boolean)||"")),wc=((0,bl.createSelector)([hc,fc],((e,t)=>e||t)),(0,bl.createSelector)([mc,lc,uc,gc,oc],((...e)=>{var t;return null!==(t=e.find(Boolean))&&void 0!==t?t:""})));(0,bl.createSelector)([yc,wc],((e,t)=>e||t));const bc=(0,bl.createSelector)([pc,hc,ic,dc,cc],((...e)=>e.find(Boolean)||"")),Ec=((0,bl.createSelector)([e=>(0,f.get)(e,"twitterEditor.title",""),bc],((e,t)=>e||t)),(0,bl.createSelector)([mc,yc,lc,uc,gc,oc],((...e)=>{var t;return null!==(t=e.find(Boolean))&&void 0!==t?t:""})));(0,bl.createSelector)([e=>(0,f.get)(e,"twitterEditor.description",""),Ec],((e,t)=>e||t));const{selectAdminUrl:_c,selectAdminLink:vc}=vl,{selectLinkParams:kc,selectLinkParam:xc,selectLink:Sc}=Sl,{selectPluginUrl:Tc,selectImageLink:Rc}=Il,{selectWistiaEmbedPermission:Cc,selectWistiaEmbedPermissionValue:Ic,selectWistiaEmbedPermissionStatus:Pc,selectWistiaEmbedPermissionError:Lc}=Al,Ac=(0,bl.createSelector)([e=>(0,f.get)(e,"settings.snippetEditor.baseUrl",""),Zl],((e,t)=>e+t)),Oc={name:"author_first_name",label:"Author first name",placeholder:"%%author_first_name%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.author_first_name","")},regexp:new RegExp("%%author_first_name%%","g")},Fc={name:"author_last_name",label:"Author last name",placeholder:"%%author_last_name%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.author_last_name","")},regexp:new RegExp("%%author_last_name%%","g")},Dc={name:"currentdate",label:"Current date",placeholder:"%%currentdate%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.currentdate","")},regexp:new RegExp("%%currentdate%%","g")},Mc={name:"currentday",label:"Current day",placeholder:"%%currentday%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.currentday","")},regexp:new RegExp("%%currentday%%","g")},Nc={name:"currentmonth",label:"Current month",placeholder:"%%currentmonth%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.currentmonth","")},regexp:new RegExp("%%currentmonth%%","g")},qc={name:"category",label:"Category",placeholder:"%%category%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.category","")},regexp:new RegExp("%%category%%","g")},Uc={name:"category_title",label:"Category Title",placeholder:"%%category_title%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.category_title","")},regexp:new RegExp("%%category_title%%","g")},$c={name:"currentyear",label:"Current year",placeholder:"%%currentyear%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.currentyear","")},regexp:new RegExp("%%currentyear%%","g")},Bc={name:"date",label:"Date",placeholder:"%%date%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.date","")},regexp:new RegExp("%%date%%","g")},Wc={name:"excerpt",label:"Excerpt",placeholder:"%%excerpt%%",aliases:[{name:"excerpt_only",label:"Excerpt only",placeholder:"%%excerpt_only%%"}],getReplacement:function(){return(0,i.select)("yoast-seo/editor").getEditorDataExcerptWithFallback()},regexp:new RegExp("%%excerpt%%|%%excerpt_only%%","g")},Yc={name:"focuskw",label:"Focus keyphrase",placeholder:"%%focuskw%%",aliases:[],getReplacement:function(){return(0,i.select)("yoast-seo/editor").getFocusKeyphrase()},regexp:new RegExp("%%focuskw%%|%%keyword%%","g")},zc={name:"id",label:"ID",placeholder:"%%id%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.id","")},regexp:new RegExp("%%id%%","g")},Kc={name:"modified",label:"Modified",placeholder:"%%modified%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.modified","")},regexp:new RegExp("%%modified%%","g")},jc={name:"name",label:"Name",placeholder:"%%name%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.name","")},regexp:new RegExp("%%name%%","g")},Hc={name:"page",label:"Page",placeholder:"%%page%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.page","")},regexp:new RegExp("%%page%%","g")},Vc={name:"pagenumber",label:"Pagenumber",placeholder:"%%pagenumber%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.pagenumber","")},regexp:new RegExp("%%pagenumber%%","g")},Gc={name:"pagetotal",label:"Pagetotal",placeholder:"%%pagetotal%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.pagetotal","")},regexp:new RegExp("%%pagetotal%%","g")},Zc={name:"permalink",label:"Permalink",placeholder:"%%permalink%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.permalink","")},regexp:new RegExp("%%permalink%%","g")},Qc={name:"post_content",label:"Post Content",placeholder:"%%post_content%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.post_content","")},regexp:new RegExp("%%post_content%%","g")},Xc={name:"post_day",label:"Post Day",placeholder:"%%post_day%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.post_day","")},regexp:new RegExp("%%post_day%%","g")},Jc={name:"post_month",label:"Post Month",placeholder:"%%post_month%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.post_month","")},regexp:new RegExp("%%post_month%%","g")},ed={name:"post_year",label:"Post Year",placeholder:"%%post_year%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.post_year","")},regexp:new RegExp("%%post_year%%","g")},td={name:"pt_plural",label:"Post type (plural)",placeholder:"%%pt_plural%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.pt_plural","")},regexp:new RegExp("%%pt_plural%%","g")},sd={name:"pt_single",label:"Post type (singular)",placeholder:"%%pt_single%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.pt_single","")},regexp:new RegExp("%%pt_single%%","g")},rd={name:"primary_category",label:"Primary category",placeholder:"%%primary_category%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.primary_category","")},regexp:new RegExp("%%primary_category%%","g")},ad={name:"searchphrase",label:"Search phrase",placeholder:"%%searchphrase%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.searchphrase","")},regexp:new RegExp("%%searchphrase%%","g")},nd={name:"sep",label:"Separator",placeholder:"%%sep%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.sep","")},regexp:new RegExp("%%sep%%(\\s*%%sep%%)*","g")},od={name:"sitedesc",label:"Tagline",placeholder:"%%sitedesc%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.sitedesc","")},regexp:new RegExp("%%sitedesc%%","g")},id={name:"sitename",label:"Site title",placeholder:"%%sitename%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.sitename","")},regexp:new RegExp("%%sitename%%","g")},ld={name:"tag",label:"Tag",placeholder:"%%tag%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.tag","")},regexp:new RegExp("%%tag%%","g")},cd={name:"term404",label:"Term404",placeholder:"%%term404%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.term404","")},regexp:new RegExp("%%term404%%","g")},dd={name:"term_description",label:"Term description",placeholder:"%%term_description%%",aliases:[{name:"tag_description",label:"Tag description",placeholder:"%%tag_description%%"},{name:"category_description",label:"Category description",placeholder:"%%category_description%%"}],getReplacement:function(){return(0,f.get)(window,"YoastSEO.app.rawData.text","")},regexp:new RegExp("%%term_description%%|%%tag_description%%|%%category_description%%","g")},pd={name:"term_hierarchy",label:"Term hierarchy",placeholder:"%%term_hierarchy%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.term_hierarchy","")},regexp:new RegExp("%%term_hierarchy%%","g")},ud={name:"term_title",label:"Term title",placeholder:"%%term_title%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.term_title","")},regexp:new RegExp("%%term_title%%","g")},md={name:"title",label:"Title",placeholder:"%%title%%",aliases:[],getReplacement:function(){return(0,i.select)("yoast-seo/editor").getEditorDataTitle()},regexp:new RegExp("%%title%%","g")},gd={name:"user_description",label:"User description",placeholder:"%%user_description%%",aliases:[],getReplacement:function(){return(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.replace_vars.user_description","")},regexp:new RegExp("%%user_description%%","g")};var hd={source:"wpseoScriptData.analysis.plugins.replaceVars",scope:[],aliases:[]},yd=function(e,t,s){this.placeholder=e,this.replacement=t,this.options=(0,f.defaults)(s,hd)};yd.prototype.getPlaceholder=function(e){return(e=e||!1)&&this.hasAlias()?this.placeholder+"|"+this.getAliases().join("|"):this.placeholder},yd.prototype.setSource=function(e){this.options.source=e},yd.prototype.hasScope=function(){return!(0,f.isEmpty)(this.options.scope)},yd.prototype.addScope=function(e){this.hasScope()||(this.options.scope=[]),this.options.scope.push(e)},yd.prototype.inScope=function(e){return!this.hasScope()||(0,f.indexOf)(this.options.scope,e)>-1},yd.prototype.hasAlias=function(){return!(0,f.isEmpty)(this.options.aliases)},yd.prototype.addAlias=function(e){this.hasAlias()||(this.options.aliases=[]),this.options.aliases.push(e)},yd.prototype.getAliases=function(){return this.options.aliases};const fd=yd,wd="replaceVariablePlugin";let bd=null,Ed=null;const _d=e=>{["content","title","snippet_title","snippet_meta","primary_category","data_page_title","data_meta_desc","excerpt"].forEach((t=>{Fe(t,e,wd,10)}))},vd=(e="")=>{switch(""===e&&(e=(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.scope","")),e){case"post":case"page":return["authorFirstName","authorLastName","category","categoryTitle","currentDate","currentDay","currentMonth","currentYear","date","excerpt","id","focusKeyphrase","modified","name","page","primaryCategory","pageNumber","pageTotal","permalink","postContent","postDay","postMonth","postYear","postTypeNamePlural","postTypeNameSingular","searchPhrase","separator","siteDescription","siteName","tag","title","userDescription"]}return[]},kd=e=>_d((t=>t.replace(new RegExp(e.placeholder,"g"),e.replacement))),xd=()=>{if(null===Ed){Ed=[];const e=(0,f.get)(window,"wpseoScriptData.analysis.plugins.replaceVars.hidden_replace_vars",[]);(null===bd&&(bd=vd().map((e=>null==o?void 0:o[e])).filter(Boolean)),bd).forEach((t=>{const s=e.includes(t.name);Ed.push({name:t.name,label:t.label,value:t.placeholder,hidden:s}),t.aliases.forEach((e=>{Ed.push({name:e.name,label:e.label,value:e.placeholder,hidden:s})}))}))}return Ed};const Sd={content:"",title:"",excerpt:"",slug:"",imageUrl:"",featuredImage:"",contentImage:"",excerptOnly:""},Td="yoastmark";function Rd(e=elementor.documents.getCurrent()){var t,s;let r=null===(t=e.$element)||void 0===t?void 0:t.find(".elementor-widget-container");var a;return null!==(s=r)&&void 0!==s&&s.length||(r=null===(a=e.$element)||void 0===a?void 0:a.find(".elementor-widget").children().not(".elementor-background-overlay, .elementor-element-overlay, .ui-resizable-handle")),r}function Cd(e,t=!1){let s=elementor.settings.page.model.get("post_excerpt");return t?s||"":(s||(s=ra(e,"ja"===xe()?80:156)),s)}function Id(){const e=elementor.documents.getCurrent();if(!Pt())return;if(!["wp-post","wp-page"].includes(e.config.type))return;if((0,i.select)("yoast-seo/editor").getActiveMarker())return;const t=function(e){const t=function(e){var t;const s=[];return null===(t=Rd(e))||void 0===t||t.each(((e,t)=>{const r=t.innerHTML.replace(/[\n\t]/g,"").trim();s.push(r)})),s.join("")}(e),s=(0,f.get)(elementor.settings.page.model.get("post_featured_image"),"url",""),r=function(e){const t=be.languageProcessing.imageInText(e);if(0===t.length)return"";const s=jQuery.parseHTML(t.join(""));for(const e of s)if(e.src)return e.src;return""}(t);return{content:t,title:elementor.settings.page.model.get("post_title"),excerpt:Cd(t),excerptOnly:Cd(t,!0),imageUrl:s||r,featuredImage:s,contentImage:r,status:elementor.settings.page.model.get("post_status")}}(e);t.content!==Sd.content&&(Sd.content=t.content,(0,i.dispatch)("yoast-seo/editor").setEditorDataContent(Sd.content)),t.title!==Sd.title&&(Sd.title=t.title,(0,i.dispatch)("yoast-seo/editor").setEditorDataTitle(Sd.title)),t.excerpt!==Sd.excerpt&&(Sd.excerpt=t.excerpt,Sd.excerptOnly=t.excerptOnly,(0,i.dispatch)("yoast-seo/editor").setEditorDataExcerpt(Sd.excerpt),(0,i.dispatch)("yoast-seo/editor").updateReplacementVariable("excerpt",Sd.excerpt),(0,i.dispatch)("yoast-seo/editor").updateReplacementVariable("excerpt_only",Sd.excerptOnly)),t.imageUrl!==Sd.imageUrl&&(Sd.imageUrl=t.imageUrl,(0,i.dispatch)("yoast-seo/editor").setEditorDataImageUrl(Sd.imageUrl)),t.contentImage!==Sd.contentImage&&(Sd.contentImage=t.contentImage,(0,i.dispatch)("yoast-seo/editor").setContentImage(Sd.contentImage)),t.featuredImage!==Sd.featuredImage&&(Sd.featuredImage=t.featuredImage,(0,i.dispatch)("yoast-seo/editor").updateData({snippetPreviewImageURL:Sd.featuredImage}))}function Pd(){Rd().each(((e,t)=>{-1!==t.innerHTML.indexOf("<"+Td)&&(t.innerHTML=be.markers.removeMarks(t.innerHTML))})),(0,i.dispatch)("yoast-seo/editor").setActiveMarker(null),(0,i.dispatch)("yoast-seo/editor").setMarkerPauseStatus(!1),YoastSEO.analysis.applyMarks(new be.Paper("",{}),[])}const Ld=(0,f.debounce)(Id,500);function Ad(e,t){const{updateWordsToHighlight:s}=(0,i.dispatch)("yoast-seo/editor");e("morphology",new be.Paper("",{keyword:t})).then((({result:{keyphraseForms:e}})=>{s((0,f.uniq)((0,f.flatten)(e)))})).catch((()=>{s([])}))}const Od=(0,f.debounce)(Ad,500);var Fd=jQuery;function Dd(e,t,s,r,a){this._scriptUrl=r,this._options={usedKeywords:t.keyword_usage,usedKeywordsPostTypes:t.keyword_usage_post_types,searchUrl:t.search_url,postUrl:t.post_edit_url},this._keywordUsage=t.keyword_usage,this._usedKeywordsPostTypes=t.keyword_usage_post_types,this._postID=Fd("#post_ID, [name=tag_ID]").val(),this._taxonomy=Fd("[name=taxonomy]").val()||"",this._nonce=a,this._ajaxAction=e,this._refreshAnalysis=s,this._initialized=!1}function Md(){window.YoastSEO=window.YoastSEO||{},window.YoastSEO.store=function(){const{snapshotReducer:s,takeSnapshot:o,restoreSnapshot:l}=((e,t)=>{let s,r=!1,a=!1;return{snapshotReducer:(a=t,n)=>r?(r=!1,s):e(a,n),takeSnapshot:(e,t)=>{t({type:"CREATE_SNAPSHOT"}),s=(0,f.cloneDeep)(e()),a=!0},restoreSnapshot:e=>{a&&(r=!0,e({type:"RESTORE_SNAPSHOT"}))}}})((0,i.combineReducers)(_e.reducers)),{freezeReducer:c,toggleFreeze:d}=((e,t)=>{let s=!1,r=null;return{freezeReducer:(a=t,n)=>s?r:e(a,n),toggleFreeze:(e,t=!s)=>{r=t?(0,f.cloneDeep)(e()):null,s=Boolean(t)}}})(s),p=(0,i.registerStore)("yoast-seo/editor",{reducer:c,selectors:{..._e.selectors,...a,...r,...n},actions:(0,f.pickBy)({..._e.actions,...t},(e=>"function"==typeof e)),controls:e,initialState:{snippetEditor:{mode:"mobile",data:{title:"",description:"",slug:""},wordsToHighlight:[],replacementVariables:[{name:"date",label:(0,m.__)("Date","wordpress-seo"),value:""},{name:"id",label:(0,m.__)("ID","wordpress-seo"),value:""},{name:"page",label:(0,m.__)("Page","wordpress-seo"),value:""},{name:"searchphrase",label:(0,m.__)("Search phrase","wordpress-seo"),value:""},{name:"sitedesc",label:(0,m.__)("Tagline","wordpress-seo"),value:""},{name:"sitename",label:(0,m.__)("Site title","wordpress-seo"),value:""},{name:"category",label:(0,m.__)("Category","wordpress-seo"),value:""},{name:"focuskw",label:(0,m.__)("Focus keyphrase","wordpress-seo"),value:""},{name:"title",label:(0,m.__)("Title","wordpress-seo"),value:""},{name:"parent_title",label:(0,m.__)("Parent title","wordpress-seo"),value:""},{name:"excerpt",label:(0,m.__)("Excerpt","wordpress-seo"),value:""},{name:"primary_category",label:(0,m.__)("Primary category","wordpress-seo"),value:""},{name:"sep",label:(0,m.__)("Separator","wordpress-seo"),value:""},{name:"excerpt_only",label:(0,m.__)("Excerpt only","wordpress-seo"),value:""},{name:"category_description",label:(0,m.__)("Category description","wordpress-seo"),value:""},{name:"tag_description",label:(0,m.__)("Tag description","wordpress-seo"),value:""},{name:"term_description",label:(0,m.__)("Term description","wordpress-seo"),value:""},{name:"currentyear",label:(0,m.__)("Current year","wordpress-seo"),value:""}],uniqueRefreshValue:"",templates:{title:"",description:""},isLoading:!0,replacementVariables:xd()}}});return(e=>{e.dispatch(_e.actions.loadCornerstoneContent()),e.dispatch(_e.actions.loadFocusKeyword()),e.dispatch(_e.actions.setMarkerStatus(window.wpseoScriptData.metabox.elementorMarkerStatus)),e.dispatch(_e.actions.setSettings({socialPreviews:{sitewideImage:window.wpseoScriptData.sitewideSocialImage,siteName:window.wpseoScriptData.metabox.site_name,contentImage:window.wpseoScriptData.metabox.first_content_image,twitterCardType:window.wpseoScriptData.metabox.twitterCardType},snippetEditor:{baseUrl:window.wpseoScriptData.metabox.base_url,date:window.wpseoScriptData.metabox.metaDescriptionDate,recommendedReplacementVariables:window.wpseoScriptData.analysis.plugins.replaceVars.recommended_replace_vars,siteIconUrl:window.wpseoScriptData.metabox.siteIconUrl}}));const{facebook:t,twitter:s}=window.wpseoScriptData.metabox.showSocial;t&&e.dispatch(_e.actions.loadFacebookPreviewData()),s&&e.dispatch(_e.actions.loadTwitterPreviewData()),e.dispatch(_e.actions.setSEMrushChangeCountry(window.wpseoScriptData.metabox.countryCode)),e.dispatch(_e.actions.setSEMrushLoginStatus(window.wpseoScriptData.metabox.SEMrushLoginStatus)),e.dispatch(_e.actions.setWincherLoginStatus(window.wpseoScriptData.metabox.wincherLoginStatus,!1)),e.dispatch(_e.actions.setWincherWebsiteId(window.wpseoScriptData.metabox.wincherWebsiteId)),e.dispatch(_e.actions.setWincherAutomaticKeyphaseTracking(window.wpseoScriptData.metabox.wincherAutoAddKeyphrases)),e.dispatch(_e.actions.setDismissedAlerts((0,f.get)(window,"wpseoScriptData.dismissedAlerts",{}))),e.dispatch(_e.actions.setCurrentPromotions((0,f.get)(window,"wpseoScriptData.currentPromotions",{}))),e.dispatch(_e.actions.setIsPremium(Boolean((0,f.get)(window,"wpseoScriptData.metabox.isPremium",!1)))),e.dispatch(_e.actions.setAdminUrl((0,f.get)(window,"wpseoScriptData.adminUrl",""))),e.dispatch(_e.actions.setLinkParams((0,f.get)(window,"wpseoScriptData.linkParams",{}))),e.dispatch(_e.actions.setPluginUrl((0,f.get)(window,"wpseoScriptData.pluginUrl",""))),e.dispatch(_e.actions.setWistiaEmbedPermissionValue("1"===(0,f.get)(window,"wpseoScriptData.wistiaEmbedPermission",!1)));const r=document.getElementById("yoast_wpseo_slug");r&&e.dispatch(_e.actions.setEditorDataSlug(r.value))})(p),p._freeze=d.bind(null,p.getState),p._takeSnapshot=o.bind(null,p.getState,p.dispatch),p._restoreSnapshot=l.bind(null,p.dispatch),p}(),function(){Rt("panel/editor/open","yoast-seo/marks/reset-on-edit",(0,f.debounce)(Pd,500),Pt),Rt("document/save/save","yoast-seo/marks/reset-on-save",Pd,(({document:e})=>It((null==e?void 0:e.id)||elementor.documents.getCurrent().id)));const e=(e=>{const t=new MutationObserver(e);return(e=document)=>(t.observe(e,{attributes:!0,childList:!0,subtree:!0,characterData:!0}),()=>t.disconnect())})(Ld);let t=f.noop;Tt("editor/documents/close","yoast-seo/content-scraper/stop",(()=>{t(),t=f.noop,Ld.cancel()}),(({id:e})=>It(e))),Tt("editor/documents/attach-preview","yoast-seo/content-scraper/start",(()=>{t=e()}),Pt),Tt("document/save/set-is-modified","yoast-seo/content-scraper/on-modified",Ld,(({document:e})=>It((null==e?void 0:e.id)||elementor.documents.getCurrent().id))),Id()}(),window.YoastSEO.pluginReady=Ae,window.YoastSEO.pluginReloaded=Oe,window.YoastSEO.registerModification=Fe,window.YoastSEO.registerPlugin=De,window.YoastSEO.applyModifications=Me,window.YoastSEO.analysis=window.YoastSEO.analysis||{},window.YoastSEO.analysis.run=(0,i.dispatch)("yoast-seo/editor").runAnalysis,window.YoastSEO.analysis.worker=function(){const{getAnalysisTimestamp:e,isCornerstoneContent:t}=(0,i.select)("yoast-seo/editor"),s=function(){const e=(0,f.get)(window,["wpseoScriptData","analysis","worker","url"],"analysis-worker.js"),t=(0,be.createWorker)(e),s=(0,f.get)(window,["wpseoScriptData","analysis","worker","dependencies"],[]),r=[];for(const e in s){if(!Object.prototype.hasOwnProperty.call(s,e))continue;const t=window.document.getElementById(`${e}-js-translations`);if(!t)continue;const a=t.innerHTML.slice(214),n=a.indexOf(","),o=a.slice(0,n-1);try{const e=JSON.parse(a.slice(n+1,-4));r.push([o,e])}catch(t){console.warn(`Failed to parse translation data for ${e} to send to the Yoast SEO worker`);continue}}return t.postMessage({dependencies:s,translations:r}),new be.AnalysisWorkerWrapper(t)}();s.initialize(function(e={}){const t={locale:xe(),contentAnalysisActive:Se(),keywordAnalysisActive:Te(),inclusiveLanguageAnalysisActive:Re(),defaultQueryParams:(0,f.get)(window,["wpseoAdminL10n","default_query_params"],{}),logLevel:(0,f.get)(window,["wpseoScriptData","analysis","worker","log_level"],"ERROR"),enabledFeatures:(0,Ce.enabledFeatures)()};return(0,f.merge)(t,e)}({useCornerstone:t(),marker:dt()})).catch(Ee),window.YoastSEO.analysis.applyMarks=(e,t)=>dt()(e,t);let r=ut(),a=t(),n=e();return(0,i.subscribe)((()=>{const o=t(),i=ut(),l=e();if(o!==a)return a=o,r=i,void s.initialize({useCornerstone:o}).then((()=>pt(s,i))).catch(Ee);l===n&&!1!==(0,f.isEqual)(i,r)||(r=i,n=l,pt(s,i))})),s}(),window.YoastSEO.analysis.collectData=ut,De(wd,{status:"ready"}),vd().forEach((e=>{const t=null==o?void 0:o[e];if(t){const e=(({getReplacement:e,regexp:t})=>s=>s.replace(t,e()))(t);_d(e)}})),window.YoastSEO.wp=window.YoastSEO.wp||{},window.YoastSEO.wp.replaceVarsPlugin={addReplacement:kd,ReplaceVar:fd},function(){const e=ke(),t=(0,f.get)(window,["wpseoScriptData","analysis","worker","keywords_assessment_url"],"used-keywords-assessment.js"),s=(0,f.get)(window,["wpseoScriptData","usedKeywordsNonce"],""),r=new Dd("get_focus_keyword_usage_and_post_types",e,(0,i.dispatch)("yoast-seo/editor").runAnalysis,t,s);r.init();let a="";(0,i.subscribe)((()=>{const e=(0,i.select)("yoast-seo/editor").getFocusKeyphrase();e!==a&&(a=e,r.setKeyword(e))}))}(),(()=>{if((0,i.select)("yoast-seo/editor").getPreference("isInsightsEnabled",!1))(0,i.dispatch)("yoast-seo/editor").loadEstimatedReadingTime(),(0,i.subscribe)((0,f.debounce)(mt(),1500,{maxWait:3e3}))})(),function(e){const{getFocusKeyphrase:t}=(0,i.select)("yoast-seo/editor");let s=t();Ad(e,s),(0,i.subscribe)((()=>{const r=t();s!==r&&(s=r,Od(e,r))}))}(window.YoastSEO.analysis.worker.runResearch),"1"===window.wpseoScriptData.isAlwaysIntroductionV2||window.elementorFrontend.config.experimentalFeatures.editor_v2?function(){if(!0===window.elementor.config.user.introduction["yoast-introduction-editor-v2"])return;const e=new window.elementorModules.editor.utils.Introduction({introductionKey:"yoast-introduction-editor-v2",dialogOptions:{id:"yoast-introduction-editor-v2",className:"elementor-right-click-introduction yoast-elementor-introduction",headerMessage:(0,m.__)("Yoast SEO for Elementor","wordpress-seo"),message:(0,m.__)("Get started with Yoast SEO's content analysis for Elementor!","wordpress-seo"),position:{my:"center top",at:"center bottom+20",of:document.querySelector("button[value='document-settings']"),using:function(e,t){this.style.setProperty("--yoast-elementor-introduction-arrow",t.target.left-t.element.left+8+"px");const s=t.target.element.closest("#elementor-editor-wrapper-v2 header");s&&s.offsetHeight>e.top-12?this.style.top=s.offsetHeight+20+"px":(0,f.isObject)(s)&&s[0].offsetHeight>e.top-12?this.style.top=s[0].offsetHeight+12+"px":this.style.top=e.top+"px",this.style.left=e.left+"px"},autoRefresh:!0},hide:{onOutsideClick:!1}},onDialogInitCallback:t=>{window.$e.routes.on("run:after",(function(e,s){"panel/page-settings/settings"===s&&t.getElements("ok").trigger("click")})),t.addButton({name:"ok",text:(0,m.__)("Got it","wordpress-seo"),callback:()=>e.setViewed()}),t.getElements("ok").addClass("elementor-button elementor-button-success")}});setTimeout((function t(){try{e.show()}catch(e){setTimeout(t,100)}}),100)}():function(){if(!0===window.elementor.config.user.introduction["yoast-introduction"])return;const e=new window.elementorModules.editor.utils.Introduction({introductionKey:"yoast-introduction",dialogOptions:{id:"yoast-introduction",className:"elementor-right-click-introduction yoast-elementor-introduction",headerMessage:(0,m.__)("New: Yoast SEO for Elementor","wordpress-seo"),message:(0,m.__)("Get started with Yoast SEO's content analysis for Elementor!","wordpress-seo"),position:{my:"left top",at:"right top",autoRefresh:!0},hide:{onOutsideClick:!1}},onDialogInitCallback:t=>{window.$e.routes.on("run:after",(function(e,s){"panel/menu"===s&&t.getElements("ok").trigger("click")})),t.addButton({name:"ok",text:(0,m.__)("Got it","wordpress-seo"),callback:()=>e.setViewed()}),t.getElements("ok").addClass("elementor-button elementor-button-success")}});setTimeout((function t(){try{e.show(window.elementor.getPanelView().header.currentView.ui.menuButton[0])}catch(e){setTimeout(t,100)}}),100)}(),wl(),window.wpseoScriptData.postType&&!["attachment","product"].includes(window.wpseoScriptData.postType)&&(()=>{const e=(0,i.select)(we).getIsPremium(),t=(0,i.select)(we).getIsWooSeoUpsell(),s=(0,i.select)(we).getIsWooSeoUpsellTerm(),r=!e||t||s;(0,l.addFilter)("yoast.replacementVariableEditor.additionalButtons","yoast/yoast-seo-premium/AiGenerator",((e,{fieldId:t})=>(r&&e.push((0,c.createElement)(p.Fill,{name:`yoast.replacementVariableEditor.additionalButtons.${t}`},(0,c.createElement)(fe,{fieldId:t}))),e)))})(),(0,l.doAction)("yoast.elementor.loaded")}Dd.prototype.init=function(){const{worker:e}=window.YoastSEO.analysis;this.requestKeywordUsage=(0,f.debounce)(this.requestKeywordUsage.bind(this),500),e.loadScript(this._scriptUrl).then((()=>{e.sendMessage("initialize",this._options,"used-keywords-assessment")})).then((()=>{this._initialized=!0,(0,f.isEqual)(this._options.usedKeywords,this._keywordUsage)?this._refreshAnalysis():e.sendMessage("updateKeywordUsage",this._keywordUsage,"used-keywords-assessment").then((()=>this._refreshAnalysis()))})).catch((e=>console.error(e)))},Dd.prototype.setKeyword=function(e){(0,f.has)(this._keywordUsage,e)||this.requestKeywordUsage(e)},Dd.prototype.requestKeywordUsage=function(e){Fd.post(ajaxurl,{action:this._ajaxAction,post_id:this._postID,keyword:e,taxonomy:this._taxonomy,nonce:this._nonce},this.updateKeywordUsage.bind(this,e),"json")},Dd.prototype.updateKeywordUsage=function(e,t){const{worker:s}=window.YoastSEO.analysis,r=t.keyword_usage,a=t.post_types;r&&(0,f.isArray)(r)&&(this._keywordUsage[e]=r,this._usedKeywordsPostTypes[e]=a,this._initialized&&s.sendMessage("updateKeywordUsage",{usedKeywords:this._keywordUsage,usedKeywordsPostTypes:this._usedKeywordsPostTypes},"used-keywords-assessment").then((()=>this._refreshAnalysis())))},jQuery(window).on("elementor:init",(()=>{window.elementor.on("panel:init",(()=>{setTimeout(Md)}))}))})()})();