(()=>{"use strict";var e={6746:(e,t,s)=>{var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=i(s(9196)),o=i(s(9156)),a=i(s(6743));function i(e){return e&&e.__esModule?e:{default:e}}var l=void 0;function c(e,t){var s,a,i,d,u,p,m,h,g=[],y={};for(p=0;p<e.length;p++)if("string"!==(u=e[p]).type){if(!t.hasOwnProperty(u.value)||void 0===t[u.value])throw new Error("Invalid interpolation, missing component node: `"+u.value+"`");if("object"!==r(t[u.value]))throw new Error("Invalid interpolation, component node must be a ReactElement or null: `"+u.value+"`","\n> "+l);if("componentClose"===u.type)throw new Error("Missing opening component token: `"+u.value+"`");if("componentOpen"===u.type){s=t[u.value],i=p;break}g.push(t[u.value])}else g.push(u.value);return s&&(d=function(e,t){var s,r,n=t[e],o=0;for(r=e+1;r<t.length;r++)if((s=t[r]).value===n.value){if("componentOpen"===s.type){o++;continue}if("componentClose"===s.type){if(0===o)return r;o--}}throw new Error("Missing closing component token `"+n.value+"`")}(i,e),m=c(e.slice(i+1,d),t),a=n.default.cloneElement(s,{},m),g.push(a),d<e.length-1&&(h=c(e.slice(d+1),t),g=g.concat(h))),1===g.length?g[0]:(g.forEach((function(e,t){e&&(y["interpolation-child-"+t]=e)})),(0,o.default)(y))}t.Z=function(e){var t=e.mixedString,s=e.components,n=e.throwErrors;if(l=t,!s)return t;if("object"!==(void 0===s?"undefined":r(s))){if(n)throw new Error("Interpolation Error: unable to process `"+t+"` because components is not an object");return t}var o=(0,a.default)(t);try{return c(o,s)}catch(e){if(n)throw new Error("Interpolation Error: unable to process `"+t+"` because of error `"+e.message+"`");return t}}},6743:e=>{function t(e){return e.match(/^\{\{\//)?{type:"componentClose",value:e.replace(/\W/g,"")}:e.match(/\/\}\}$/)?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.match(/^\{\{/)?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}e.exports=function(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(t)}},9156:(e,t,s)=>{var r=s(9196),n="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,o=s(7942),a=s(9179),i=s(397),l=".",c=":",d="function"==typeof Symbol&&Symbol.iterator,u="@@iterator";function p(e,t){return e&&"object"==typeof e&&null!=e.key?(s=e.key,r={"=":"=0",":":"=2"},"$"+(""+s).replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var s,r}function m(e,t,s,r){var o,i=typeof e;if("undefined"!==i&&"boolean"!==i||(e=null),null===e||"string"===i||"number"===i||"object"===i&&e.$$typeof===n)return s(r,e,""===t?l+p(e,0):t),1;var h=0,g=""===t?l:t+c;if(Array.isArray(e))for(var y=0;y<e.length;y++)h+=m(o=e[y],g+p(o,y),s,r);else{var f=function(e){var t=e&&(d&&e[d]||e[u]);if("function"==typeof t)return t}(e);if(f)for(var b,w=f.call(e),k=0;!(b=w.next()).done;)h+=m(o=b.value,g+p(o,k++),s,r);else if("object"===i){var v=""+e;a(!1,"Objects are not valid as a React child (found: %s).%s","[object Object]"===v?"object with keys {"+Object.keys(e).join(", ")+"}":v,"")}}return h}var h=/\/+/g;function g(e){return(""+e).replace(h,"$&/")}var y,f,b=w,w=function(e){var t=this;if(t.instancePool.length){var s=t.instancePool.pop();return t.call(s,e),s}return new t(e)};function k(e,t,s,r){this.result=e,this.keyPrefix=t,this.func=s,this.context=r,this.count=0}function v(e,t,s){var n,a,i=e.result,l=e.keyPrefix,c=e.func,d=e.context,u=c.call(d,t,e.count++);Array.isArray(u)?E(u,i,s,o.thatReturnsArgument):null!=u&&(r.isValidElement(u)&&(n=u,a=l+(!u.key||t&&t.key===u.key?"":g(u.key)+"/")+s,u=r.cloneElement(n,{key:a},void 0!==n.props?n.props.children:void 0)),i.push(u))}function E(e,t,s,r,n){var o="";null!=s&&(o=g(s)+"/");var a=k.getPooled(t,o,r,n);!function(e,t,s){null==e||m(e,"",t,s)}(e,v,a),k.release(a)}k.prototype.destructor=function(){this.result=null,this.keyPrefix=null,this.func=null,this.context=null,this.count=0},y=function(e,t,s,r){var n=this;if(n.instancePool.length){var o=n.instancePool.pop();return n.call(o,e,t,s,r),o}return new n(e,t,s,r)},(f=k).instancePool=[],f.getPooled=y||b,f.poolSize||(f.poolSize=10),f.release=function(e){var t=this;a(e instanceof t,"Trying to release an instance into a pool of a different type."),e.destructor(),t.instancePool.length<t.poolSize&&t.instancePool.push(e)},e.exports=function(e){if("object"!=typeof e||!e||Array.isArray(e))return i(!1,"React.addons.createFragment only accepts a single object. Got: %s",e),e;if(r.isValidElement(e))return i(!1,"React.addons.createFragment does not accept a ReactElement without a wrapper object."),e;a(1!==e.nodeType,"React.addons.createFragment(...): Encountered an invalid child; DOM elements are not valid children of React components.");var t=[];for(var s in e)E(e[s],t,s,o.thatReturnsArgument);return t}},7942:e=>{function t(e){return function(){return e}}var s=function(){};s.thatReturns=t,s.thatReturnsFalse=t(!1),s.thatReturnsTrue=t(!0),s.thatReturnsNull=t(null),s.thatReturnsThis=function(){return this},s.thatReturnsArgument=function(e){return e},e.exports=s},9179:e=>{e.exports=function(e,t,s,r,n,o,a,i){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[s,r,n,o,a,i],d=0;(l=new Error(t.replace(/%s/g,(function(){return c[d++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}},397:(e,t,s)=>{var r=s(7942);e.exports=r},9196:e=>{e.exports=window.React}},t={};function s(r){var n=t[r];if(void 0!==n)return n.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,s),o.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var r in t)s.o(t,r)&&!s.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e={};s.r(e),s.d(e,{refreshDelay:()=>i});var t={};s.r(t),s.d(t,{default:()=>f,initializationDone:()=>b,sortResultsByIdentifier:()=>y});var r={};s.r(r),s.d(r,{default:()=>Y,getIconForScore:()=>U});var n={};s.r(n),s.d(n,{doAjaxRequest:()=>Ps});var o={};s.r(o),s.d(o,{applyReplaceUsingPlugin:()=>Gs,createLabelFromName:()=>$s,excerptFromContent:()=>Vs,fillReplacementVariables:()=>Ks,handlePrefixes:()=>Ds,mapCustomFields:()=>zs,mapCustomTaxonomies:()=>Ys,nonReplaceVars:()=>Fs,prepareCustomFieldForDispatch:()=>Hs,prepareCustomTaxonomyForDispatch:()=>Us,pushNewReplaceVar:()=>Ws,replaceSpaces:()=>js});const a=window.yoast.externals.contexts,i=500,l=window.lodash;function c(){return(0,l.get)(window,"wpseoScriptData.metabox",{intl:{},isRtl:!1})}const d=window.wp.i18n,u=window.yoast.analysis,p=window.wp.hooks,m=window.yoast.externals.redux;function h(){}let g=!1;function y(e){return e.sort(((e,t)=>e._identifier.localeCompare(t._identifier)))}function f(e,t,s,r,n){if(!g)return;const o=u.Paper.parse(t());e.analyze(o).then((a=>{const{result:{seo:i,readability:l,inclusiveLanguage:c}}=a;if(i){const e=i[""];e.results.forEach((e=>{e.getMarker=()=>()=>s(o,e.marks)})),e.results=y(e.results),r.dispatch(m.actions.setSeoResultsForKeyword(o.getKeyword(),e.results)),r.dispatch(m.actions.setOverallSeoScore(e.score,o.getKeyword())),r.dispatch(m.actions.refreshSnippetEditor()),n.saveScores(e.score,o.getKeyword())}l&&(l.results.forEach((e=>{e.getMarker=()=>()=>s(o,e.marks)})),l.results=y(l.results),r.dispatch(m.actions.setReadabilityResults(l.results)),r.dispatch(m.actions.setOverallReadabilityScore(l.score)),r.dispatch(m.actions.refreshSnippetEditor()),n.saveContentScore(l.score)),c&&(c.results.forEach((e=>{e.getMarker=()=>()=>s(o,e.marks)})),c.results=y(c.results),r.dispatch(m.actions.setInclusiveLanguageResults(c.results)),r.dispatch(m.actions.setOverallInclusiveLanguageScore(c.score)),r.dispatch(m.actions.refreshSnippetEditor()),n.saveInclusiveLanguageScore(c.score)),(0,p.doAction)("yoast.analysis.refresh",a,{paper:o,worker:e,collectData:t,applyMarks:s,store:r,dataCollector:n})})).catch(h)}function b(){g=!0}var w=s(9196);const k=window.wp.element,v=window.yoast.styledComponents;var E=s.n(v);const x=window.yoast.propTypes;var _=s.n(x);const R=window.yoast.componentsNew,I=window.yoast.helpers,T=window.yoast.styleGuide,S=T.colors.$color_bad,C=T.colors.$palette_error_background,L=T.colors.$color_grey_text_light,P=T.colors.$palette_error_text,A=E().div`
	display: flex;
	flex-direction: column;
`,O=E().label`
	font-size: var(--yoast-font-size-default);
	font-weight: var(--yoast-font-weight-bold);
	${(0,I.getDirectionalStyle)("margin-right: 4px","margin-left: 4px")};
`,B=E().span`
	margin-bottom: 0.5em;
`,N=E()(R.InputField)`
	flex: 1 !important;
	box-sizing: border-box;
	max-width: 100%;
	margin: 0; // Reset margins inherited from WordPress.

	// Hide native X in Edge and IE11.
	&::-ms-clear {
		display: none;
	}

	&.has-error {
		border-color: ${S} !important;
		background-color: ${C} !important;

		&:focus {
			box-shadow: 0 0 2px ${S} !important;
		}
	}
`,q=E().ul`
	color: ${P};
	list-style-type: disc;
	list-style-position: outside;
	margin: 0;
	margin-left: 1.2em;
`,M=E().li`
	color: ${P};
	margin: 0 0 0.5em 0;
`,F=(0,R.addFocusStyle)(E().button`
		border: 1px solid transparent;
		box-shadow: none;
		background: none;
		flex: 0 0 32px;
		height: 32px;
		max-width: 32px;
		padding: 0;
		cursor: pointer;
	`);F.propTypes={type:_().string,focusColor:_().string,focusBackgroundColor:_().string,focusBorderColor:_().string},F.defaultProps={type:"button",focusColor:T.colors.$color_button_text_hover,focusBackgroundColor:"transparent",focusBorderColor:T.colors.$color_blue};const K=E()(R.SvgIcon)`
	margin-top: 4px;
`,D=E().div`
	display: flex;
	flex-direction: row;
	align-items: center;

	&.has-remove-keyword-button {
		${N} {
			${(0,I.getDirectionalStyle)("padding-right: 40px","padding-left: 40px")};
		}

		${F} {
			${(0,I.getDirectionalStyle)("margin-left: -32px","margin-right: -32px")};
		}
	}
`;class $ extends k.Component{constructor(e){super(e),this.handleChange=this.handleChange.bind(this)}handleChange(e){this.props.onChange(e.target.value)}renderLabel(){const{id:e,label:t,helpLink:s}=this.props;return(0,w.createElement)(B,null,(0,w.createElement)(O,{htmlFor:e},t),s)}renderErrorMessages(){const e=[...this.props.errorMessages];return!(0,l.isEmpty)(e)&&(0,w.createElement)(q,null,e.map(((e,t)=>(0,w.createElement)(M,{key:t},(0,w.createElement)("span",{role:"alert"},e)))))}render(){const{id:e,showLabel:t,keyword:s,onRemoveKeyword:r,onBlurKeyword:n,onFocusKeyword:o,hasError:a}=this.props,i=!t,c=r!==l.noop;return(0,w.createElement)(A,null,t&&this.renderLabel(),a&&this.renderErrorMessages(),(0,w.createElement)(D,{className:c?"has-remove-keyword-button":null},(0,w.createElement)(N,{"aria-label":i?this.props.label:null,type:"text",id:e,className:a?"has-error":null,onChange:this.handleChange,onFocus:o,onBlur:n,value:s,autoComplete:"off"}),c&&(0,w.createElement)(F,{onClick:r,focusBoxShadowColor:"#084A67"},(0,w.createElement)(K,{size:"18px",icon:"times-circle",color:L}))))}}$.propTypes={id:_().string.isRequired,showLabel:_().bool,keyword:_().string,onChange:_().func.isRequired,onRemoveKeyword:_().func,onBlurKeyword:_().func,onFocusKeyword:_().func,label:_().string.isRequired,helpLink:_().node,hasError:_().bool,errorMessages:_().arrayOf(_().string)},$.defaultProps={showLabel:!0,keyword:"",onRemoveKeyword:l.noop,onBlurKeyword:l.noop,onFocusKeyword:l.noop,helpLink:null,hasError:!1,errorMessages:[]};const W=$;function j(e,t=""){const s=e.getIdentifier(),r={score:e.score,rating:u.interpreters.scoreToRating(e.score),hasMarks:e.hasMarks(),marker:e.getMarker(),id:s,text:e.text,markerId:t.length>0?`${t}:${s}`:s,hasBetaBadge:e.hasBetaBadge(),hasJumps:e.hasJumps(),hasAIFixes:e.hasAIFixes(),editFieldName:e.editFieldName};return"ok"===r.rating&&(r.rating="OK"),r}function H(e,t){switch(e.rating){case"error":t.errorsResults.push(e);break;case"feedback":t.considerationsResults.push(e);break;case"bad":t.problemsResults.push(e);break;case"OK":t.improvementsResults.push(e);break;case"good":t.goodResults.push(e)}return t}function U(e){switch(e){case"loading":return{icon:"loading-spinner",color:T.colors.$color_green_medium_light};case"not-set":return{icon:"seo-score-none",color:T.colors.$color_score_icon};case"noindex":return{icon:"seo-score-none",color:T.colors.$color_noindex};case"good":return{icon:"seo-score-good",color:T.colors.$color_green_medium};case"ok":return{icon:"seo-score-ok",color:T.colors.$color_ok};default:return{icon:"seo-score-bad",color:T.colors.$color_red}}}function Y(e,t=""){let s={errorsResults:[],problemsResults:[],improvementsResults:[],goodResults:[],considerationsResults:[]};if(!e)return s;for(let r=0;r<e.length;r++){const n=e[r];n.text&&(s=H(j(n,t),s))}return s}const z=(0,I.makeOutboundLink)(E().a`
	display: inline-block;
	position: relative;
	outline: none;
	text-decoration: none;
	border-radius: 100%;
	width: 24px;
	height: 24px;
	margin: -4px 0;
	vertical-align: middle;

	color: ${T.colors.$color_help_text};
	
	&:hover,
	&:focus {
		color: ${T.colors.$color_snippet_focus};	
	}
	
	// Overwrite the default blue active color for links.
	&:active {
		color: ${T.colors.$color_help_text};	
	}

	&::before {
		position: absolute;
		top: 0;
		left: 0;
		padding: 2px;
		content: "\f223";
	}
`),V=E()(R.Collapsible)`
	h2 > button {
		padding-left: 24px;
		padding-top: 16px;

		&:hover {
			background-color: #f0f0f0;
		}
	}

	div[class^="collapsible_content"] {
		padding: 24px 0;
		margin: 0 24px;
		border-top: 1px solid rgba(0,0,0,0.2);
	}

`,G=window.wp.components,Q="yoast yoast-gutenberg-modal",X=e=>{const{title:t,className:s,showYoastIcon:r,additionalClassName:n,...o}=e,a=r?(0,w.createElement)("span",{className:"yoast-icon"}):null;return(0,w.createElement)(G.Modal,{title:t,className:`${s} ${n}`,icon:a,...o},e.children)};X.propTypes={title:_().string,className:_().string,showYoastIcon:_().bool,children:_().oneOfType([_().node,_().arrayOf(_().node)]),additionalClassName:_().string},X.defaultProps={title:"Yoast SEO",className:Q,showYoastIcon:!0,children:null,additionalClassName:""};const Z=X,J=(window.yoast.socialMetadataForms,e=>({type:e.subtype,width:e.width,height:e.height,url:e.url,id:e.id,sizes:e.sizes,alt:e.alt||e.title||e.name}));const ee=({hiddenField:e,hiddenFieldImageId:t,hiddenFieldFallbackImageId:s,hasImageValidation:r,...n})=>{const[o,a]=(0,k.useState)(null!==document.getElementById(s)),i=(0,k.useMemo)((()=>document.getElementById(e))),l=(0,k.useMemo)((()=>document.getElementById(t)));let c=null;c=s&&document.getElementById(s)?(0,k.useMemo)((()=>document.getElementById(s))):l;const[d,u]=(0,k.useState)({url:i?i.value:"",id:c?parseInt(c.value,10):"",alt:""}),[p,m]=(0,k.useState)([]),h=(0,k.useCallback)((e=>{i&&(i.value=e.url),c&&(c.value=e.id)})),g=(0,k.useCallback)((()=>{(function(e){const t=window.wp.media();return t.on("select",(()=>{const s=t.state().get("selection").first();e(J(s.attributes))})),t})((e=>{c=l,u(e),h(e),r&&m((0,I.validateFacebookImage)(e)),a(!1)})).open()}),[r,h]),y=(0,k.useCallback)((()=>{c=l;const e={url:"",id:"",alt:""};u(e),h(e),m([]),a(!0)}),[h]);return(0,k.useEffect)((()=>{var e;d.id&&!d.alt&&(e=d.id,new Promise(((t,s)=>{window.wp.media.attachment||s(),window.wp.media.attachment(e).fetch().then((e=>{t(J(e))})).catch((()=>s()))}))).then((e=>u(e)))}),[d]),(0,w.createElement)(R.ImageSelect,{...n,usingFallback:o,imageUrl:d.url,imageId:d.id,imageAltText:d.alt,onClick:g,onRemoveImageClick:y,warnings:p})};ee.propTypes={hiddenField:_().string.isRequired,hiddenFieldImageId:_().string,hiddenFieldFallbackImageId:_().string,hasImageValidation:_().bool},ee.defaultProps={hiddenFieldImageId:"",hiddenFieldFallbackImageId:"",hasImageValidation:!1};const te=ee;function se({target:e,children:t}){let s=e;return"string"==typeof e&&(s=document.getElementById(e)),s?(0,k.createPortal)(t,s):null}function re({target:e,label:t,hasPreview:s,hiddenField:r,hiddenFieldImageId:n,hiddenFieldFallbackImageId:o,selectImageButtonId:a,replaceImageButtonId:i,removeImageButtonId:l,hasNewBadge:c,isDisabled:d,hasPremiumBadge:u,hasImageValidation:p}){return(0,w.createElement)(se,{target:e},(0,w.createElement)(te,{label:t,hasPreview:s,hiddenField:r,hiddenFieldImageId:n,hiddenFieldFallbackImageId:o,selectImageButtonId:a,replaceImageButtonId:i,removeImageButtonId:l,hasNewBadge:c,isDisabled:d,hasPremiumBadge:u,hasImageValidation:p}))}se.propTypes={target:_().oneOfType([_().string,_().object]).isRequired,children:_().node.isRequired},re.propTypes={target:_().string.isRequired,label:_().string.isRequired,hasPreview:_().bool.isRequired,hiddenField:_().string.isRequired,hiddenFieldImageId:_().string,hiddenFieldFallbackImageId:_().string,selectImageButtonId:_().string,replaceImageButtonId:_().string,removeImageButtonId:_().string,hasNewBadge:_().bool,isDisabled:_().bool,hasPremiumBadge:_().bool,hasImageValidation:_().bool},re.defaultProps={hiddenFieldImageId:"",hiddenFieldFallbackImageId:"",selectImageButtonId:"",replaceImageButtonId:"",removeImageButtonId:"",hasNewBadge:!1,isDisabled:!1,hasPremiumBadge:!1,hasImageValidation:!1};const ne=({target:e,scoreIndicator:t})=>(0,w.createElement)(se,{target:e},(0,w.createElement)(R.SvgIcon,{...U(t)}));ne.propTypes={target:_().string.isRequired,scoreIndicator:_().string.isRequired};const oe=ne,ae=e=>{const[t,s]=(0,k.useState)(!1),{prefixIcon:r}=e;return(0,w.createElement)("div",{className:"yoast components-panel__body "+(t?"is-opened":"")},(0,w.createElement)("h2",{className:"components-panel__body-title"},(0,w.createElement)("button",{onClick:function(){s(!t)},className:"components-button components-panel__body-toggle",type:"button",id:e.buttonId},(0,w.createElement)("span",{className:"yoast-icon-span",style:{fill:`${r&&r.color||""}`}},r&&(0,w.createElement)(R.SvgIcon,{icon:r.icon,color:r.color,size:r.size})),(0,w.createElement)("span",{className:"yoast-title-container"},(0,w.createElement)("div",{className:"yoast-title"},e.title),(0,w.createElement)("div",{className:"yoast-subtitle"},e.subTitle)),e.hasBetaBadgeLabel&&(0,w.createElement)(R.BetaBadge,null),(0,w.createElement)("span",{className:"yoast-chevron","aria-hidden":"true"}))),t&&e.children)},ie=ae;ae.propTypes={title:_().string.isRequired,children:_().oneOfType([_().node,_().arrayOf(_().node)]).isRequired,prefixIcon:_().object,subTitle:_().string,hasBetaBadgeLabel:_().bool,buttonId:_().string},ae.defaultProps={prefixIcon:null,subTitle:"",hasBetaBadgeLabel:!1,buttonId:null};const le=({children:e})=>(0,w.createElement)("div",null,e);le.propTypes={renderPriority:_().number.isRequired,children:_().node.isRequired};const ce=le,de=({theme:e,location:t,children:s})=>(0,w.createElement)(a.LocationProvider,{value:t},(0,w.createElement)(v.ThemeProvider,{theme:e},s));de.propTypes={theme:_().object.isRequired,location:_().oneOf(["sidebar","metabox","modal"]).isRequired,children:_().element.isRequired};const ue=de,pe=window.wp.compose,me=window.wp.data,he=e=>(0,w.createElement)("div",{className:"yoast components-panel__body"},(0,w.createElement)("h2",{className:"components-panel__body-title"},(0,w.createElement)("button",{id:e.id,onClick:e.onClick,className:"components-button components-panel__body-toggle",type:"button"},e.prefixIcon&&(0,w.createElement)("span",{className:"yoast-icon-span",style:{fill:`${e.prefixIcon&&e.prefixIcon.color||""}`}},(0,w.createElement)(R.SvgIcon,{size:e.prefixIcon.size,icon:e.prefixIcon.icon})),(0,w.createElement)("span",{className:"yoast-title-container"},(0,w.createElement)("div",{className:"yoast-title"},e.title),(0,w.createElement)("div",{className:"yoast-subtitle"},e.subTitle)),e.children,e.suffixIcon&&(0,w.createElement)(R.SvgIcon,{size:e.suffixIcon.size,icon:e.suffixIcon.icon}),e.SuffixHeroIcon))),ge=he;he.propTypes={onClick:_().func.isRequired,title:_().string.isRequired,id:_().string,subTitle:_().string,suffixIcon:_().object,SuffixHeroIcon:_().object,prefixIcon:_().object,children:_().node},he.defaultProps={id:"",suffixIcon:null,SuffixHeroIcon:null,prefixIcon:null,subTitle:"",children:null};const ye=({id:e,postTypeName:t,children:s,title:r,isOpen:n,close:o,open:i,shouldCloseOnClickOutside:l,showChangesWarning:c,SuffixHeroIcon:u})=>(0,w.createElement)(k.Fragment,null,n&&(0,w.createElement)(a.LocationProvider,{value:"modal"},(0,w.createElement)(Z,{title:r,onRequestClose:o,additionalClassName:"yoast-collapsible-modal yoast-post-settings-modal",id:"id",shouldCloseOnClickOutside:l},(0,w.createElement)("div",{className:"yoast-content-container"},(0,w.createElement)("div",{className:"yoast-modal-content"},s)),(0,w.createElement)("div",{className:"yoast-notice-container"},(0,w.createElement)("hr",null),(0,w.createElement)("div",{className:"yoast-button-container"},c&&(0,w.createElement)("p",null,/* Translators: %s translates to the Post Label in singular form */
(0,d.sprintf)((0,d.__)("Make sure to save your %s for changes to take effect","wordpress-seo"),t)),(0,w.createElement)("button",{className:"yoast-button yoast-button--primary yoast-button--post-settings-modal",type:"button",onClick:o},/* Translators: %s translates to the Post Label in singular form */
(0,d.sprintf)((0,d.__)("Return to your %s","wordpress-seo"),t)))))),(0,w.createElement)(ge,{id:e+"-open-button",title:r,SuffixHeroIcon:u,suffixIcon:u?null:{size:"20px",icon:"pencil-square"},onClick:i}));ye.propTypes={id:_().string.isRequired,postTypeName:_().string.isRequired,children:_().oneOfType([_().node,_().arrayOf(_().node)]).isRequired,title:_().string.isRequired,isOpen:_().bool.isRequired,open:_().func.isRequired,close:_().func.isRequired,shouldCloseOnClickOutside:_().bool,showChangesWarning:_().bool,SuffixHeroIcon:_().object},ye.defaultProps={shouldCloseOnClickOutside:!0,showChangesWarning:!0};const fe=ye,be=(0,pe.compose)([(0,me.withSelect)(((e,t)=>{const{getPostOrPageString:s,getIsModalOpen:r}=e("yoast-seo/editor");return{postTypeName:s(),isOpen:r(t.id)}})),(0,me.withDispatch)(((e,t)=>{const{openEditorModal:s,closeEditorModal:r}=e("yoast-seo/editor");return{open:()=>s(t.id),close:r}}))])(fe),we=(0,pe.compose)([(0,me.withSelect)(((e,t)=>{const{isAlertDismissed:s}=e(t.store||"yoast-seo/editor");return{isAlertDismissed:s(t.alertKey)}})),(0,me.withDispatch)(((e,t)=>{const{dismissAlert:s}=e(t.store||"yoast-seo/editor");return{onDismissed:()=>s(t.alertKey)}}))])(R.Alert),ke=window.yoast.analysisReport,ve=window.yoast.uiLibrary,Ee=w.forwardRef((function(e,t){return w.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),w.createElement("path",{fillRule:"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",clipRule:"evenodd"}))})),xe=(E().div`
	min-width: 600px;

	@media screen and ( max-width: 680px ) {
		min-width: 0;
		width: 86vw;
	}
`,E().div`
	@media screen and ( min-width: 600px ) {
		max-width: 420px;
	}
`),_e=(E()(R.Icon)`
	float: ${(0,I.getDirectionalStyle)("right","left")};
	margin: ${(0,I.getDirectionalStyle)("0 0 16px 16px","0 16px 16px 0")};

	&& {
		width: 150px;
		height: 150px;

		@media screen and ( max-width: 680px ) {
			width: 80px;
			height: 80px;
		}
	}
`,window.wp.url),Re=(e,t)=>{try{return(0,k.createInterpolateElement)(e,t)}catch(t){return console.error("Error in translation for:",e,t),e}},Ie=E().div`
  padding: 25px 32px 32px;
  color: #303030;
`,Te=E().ul`
  margin: 0;
  padding: 0;

  li {
    list-style-image: var(--yoast-svg-icon-check);
    margin: 0.5rem 0 0 1.5rem;
    line-height: 1.4em;

    &::marker {
      font-size: 1.5rem;
    }
  }
`,Se=E().span`
  display: block;
  margin-top: 4px;
`,Ce=E().h2`
  margin-top: 0;
  margin-bottom: 0.25rem;
  color: #303030;
  font-size: 0.8125rem;
  font-weight: 600;
`,Le=E().p`
  display: block;
  margin: 0.25rem 0 1rem 0 !important;
  max-width: 420px;
`,Pe=E().hr`
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  border-top: 0;
  border-bottom: 1px solid #E2E8F0;
`,Ae=E().div`
  text-align: center;
`,Oe=E().a`
  width: 100%;
`,Be=(0,I.makeOutboundLink)(Oe);class Ne extends k.Component{constructor(e){super(e),this.state={defaultPrice:"99"}}createBenefitsList(e){return e.length>0&&(0,w.createElement)(Te,{role:"list"},e.map(((e,t)=>(0,w.createElement)("li",{key:`upsell-benefit-${t}`},Re(e,{strong:(0,w.createElement)("strong",null)})))))}render(){const e=(0,me.select)("yoast-seo/editor").isPromotionActive("black-friday-2024-promotion"),{defaultPrice:t}=this.state,s=e?"69.30":null,r=s||t;return(0,w.createElement)(k.Fragment,null,e&&(0,w.createElement)("div",{className:"yst-flex  yst-items-center yst-text-lg yst-content-between yst-bg-black yst-text-amber-300 yst-h-9 yst-border-amber-300 yst-border-y yst-border-x-0 yst-border-solid yst-px-6"},(0,w.createElement)("div",{className:"yst-mx-auto"},(0,d.__)("30% OFF - BLACK FRIDAY","wordpress-seo"))),(0,w.createElement)(Ie,null,(0,w.createElement)(Ce,null,this.props.title),(0,w.createElement)(Le,null,this.props.description),(0,w.createElement)(Ae,null,(0,w.createElement)(Be,{...this.props.upsellButton},this.props.upsellButtonText,this.props.upsellButtonHasCaret&&(0,w.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"})),(0,w.createElement)("div",{className:"yst-text-slate-600 yst-my-4"},s&&(0,w.createElement)(k.Fragment,null,(0,w.createElement)("span",{className:"yst-text-slate-500 yst-line-through"},t)," "),(0,w.createElement)("span",{className:"yst-text-slate-900 yst-text-2xl yst-font-bold"},r)," ",(0,d.__)("$ USD / € EUR / £ GBP per year (ex. VAT)","wordpress-seo")),(0,w.createElement)(Se,{id:this.props.upsellButton["aria-describedby"]},this.props.upsellButtonLabel)),(0,w.createElement)(Pe,null),(0,w.createElement)(Ce,null,this.props.benefitsTitle),this.createBenefitsList(this.props.benefits)))}}Ne.propTypes={title:_().node,benefits:_().array,benefitsTitle:_().node,description:_().node,upsellButton:_().object,upsellButtonText:_().string.isRequired,upsellButtonLabel:_().string,upsellButtonHasCaret:_().bool},Ne.defaultProps={title:null,description:null,benefits:[],benefitsTitle:null,upsellButton:{href:"",className:"button button-primary"},upsellButtonLabel:"",upsellButtonHasCaret:!0};const qe=Ne,Me=(0,d.__)("Check your text on even more SEO criteria and get an enhanced keyphrase analysis, making it easier to optimize your content.","wordpress-seo"),Fe=e=>{const{locationContext:t}=(0,a.useRootContext)(),s=(0,_e.addQueryArgs)(wpseoAdminL10n[e.buyLink],{context:t});return(0,w.createElement)(qe,{title:(0,d.__)("Get more help with writing content that ranks","wordpress-seo"),description:e.description,benefitsTitle:(0,d.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,d.__)("%s also gives you:","wordpress-seo"),"Yoast SEO Premium"),benefits:[(0,d.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,d.__)("%1$sAI%2$s: Better SEO titles and meta descriptions, faster.","wordpress-seo"),"<strong>","</strong>"),(0,d.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,d.__)("%1$sMultiple keywords%2$s: Rank higher for more searches.","wordpress-seo"),"<strong>","</strong>"),(0,d.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,d.__)("%1$sSuper fast%2$s internal linking suggestions.","wordpress-seo"),"<strong>","</strong>"),(0,d.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,d.__)("%1$sNo more broken links%2$s: Automatic redirect manager.","wordpress-seo"),"<strong>","</strong>"),(0,d.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,d.__)("%1$sAppealing social previews%2$s people actually want to click on.","wordpress-seo"),"<strong>","</strong>"),(0,d.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,d.__)("%1$s24/7 support%2$s: Also on evenings and weekends.","wordpress-seo"),"<strong>","</strong>")],upsellButtonText:(0,d.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,d.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:s,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,d.__)("1 year of premium support and updates included!","wordpress-seo")})};Fe.propTypes={buyLink:_().string.isRequired,description:_().string},Fe.defaultProps={description:Me};const Ke=Fe;class De extends k.Component{constructor(e){super(e);const t=this.props.results;this.state={mappedResults:{}},null!==t&&(this.state={mappedResults:Y(t,this.props.keywordKey)}),this.handleMarkButtonClick=this.handleMarkButtonClick.bind(this),this.handleEditButtonClick=this.handleEditButtonClick.bind(this),this.handleResultsChange=this.handleResultsChange.bind(this),this.renderHighlightingUpsell=this.renderHighlightingUpsell.bind(this),this.createMarkButton=this.createMarkButton.bind(this)}componentDidUpdate(e){null!==this.props.results&&this.props.results!==e.results&&this.setState({mappedResults:Y(this.props.results,this.props.keywordKey)})}createMarkButton({ariaLabel:e,id:t,className:s,status:r,onClick:n,isPressed:o}){return(0,w.createElement)(k.Fragment,null,(0,w.createElement)(R.IconButtonToggle,{marksButtonStatus:r,className:s,onClick:n,id:t,icon:"eye",pressed:o,ariaLabel:e}),this.props.shouldUpsellHighlighting&&(0,w.createElement)("div",{className:"yst-root"},(0,w.createElement)(ve.Badge,{className:"yst-absolute yst-px-[3px] yst-py-[3px] yst--end-[6.5px] yst--top-[6.5px]",size:"small",variant:"upsell"},(0,w.createElement)(Ee,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",role:"img","aria-hidden":!0,focusable:!1}))))}deactivateMarker(){this.props.setActiveMarker(null),this.props.setMarkerPauseStatus(!1),this.removeMarkers()}activateMarker(e,t){this.props.setActiveMarker(e),t()}handleMarkButtonClick(e,t){const s=this.props.keywordKey.length>0?`${this.props.keywordKey}:${e}`:e;this.props.activeAIFixesButton&&this.props.setActiveAIFixesButton(null),s===this.props.activeMarker?this.deactivateMarker():this.activateMarker(s,t)}handleResultsChange(e,t,s){const r=this.props.keywordKey.length>0?`${this.props.keywordKey}:${e}`:e;r===this.props.activeMarker&&(s?(0,l.isUndefined)(t)||this.activateMarker(r,t):this.deactivateMarker())}focusOnKeyphraseField(e){const t=this.props.keywordKey,s=""===t?"focus-keyword-input-"+e:"yoast-keyword-input-"+t+"-"+e,r=document.getElementById(s);r.focus(),r.scrollIntoView({behavior:"auto",block:"center",inline:"center"})}focusOnGooglePreviewField(e,t){let s;s="metaDescriptionKeyword"===e||"metaDescriptionLength"===e?"description":"titleWidth"===e||"keyphraseInSEOTitle"===e?"title":"slug";const r=document.getElementById("yoast-google-preview-"+s+"-"+t);r.focus(),r.scrollIntoView({behavior:"auto",block:"center",inline:"center"})}handleEditButtonClick(e){const t=this.props.location;"functionWordsInKeyphrase"!==e&&"keyphraseLength"!==e?(["metaDescriptionKeyword","metaDescriptionLength","titleWidth","keyphraseInSEOTitle","slugKeyword"].includes(e)&&this.handleGooglePreviewFocus(t,e),(0,p.doAction)("yoast.focus.input",e)):this.focusOnKeyphraseField(t)}handleGooglePreviewFocus(e,t){if("sidebar"===e)document.getElementById("yoast-search-appearance-modal-open-button").click(),setTimeout((()=>this.focusOnGooglePreviewField(t,"modal")),500);else{const s=document.getElementById("yoast-snippet-editor-metabox");s&&"false"===s.getAttribute("aria-expanded")?(s.click(),setTimeout((()=>this.focusOnGooglePreviewField(t,e)),100)):this.focusOnGooglePreviewField(t,e)}}removeMarkers(){window.YoastSEO.analysis.applyMarks(new u.Paper("",{}),[])}renderHighlightingUpsell(e,t){const s=(0,d.__)("Highlight areas of improvement in your text, no more searching for a needle in a haystack, straight to optimizing! Now also in Elementor!","wordpress-seo");return e&&(0,w.createElement)(Z,{title:(0,d.__)("Unlock Premium SEO analysis","wordpress-seo"),onRequestClose:t,additionalClassName:"",className:`${Q} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,id:"yoast-premium-seo-analysis-highlighting-modal",shouldCloseOnClickOutside:!0},(0,w.createElement)(xe,null,(0,w.createElement)(Ke,{buyLink:this.props.highlightingUpsellLink,description:s})))}render(){const{mappedResults:e}=this.state,{errorsResults:t,improvementsResults:s,goodResults:r,considerationsResults:n,problemsResults:o}=e,{upsellResults:a,resultCategoryLabels:i}=this.props,l={errors:(0,d.__)("Errors","wordpress-seo"),problems:(0,d.__)("Problems","wordpress-seo"),improvements:(0,d.__)("Improvements","wordpress-seo"),considerations:(0,d.__)("Considerations","wordpress-seo"),goodResults:(0,d.__)("Good results","wordpress-seo")},c=Object.assign(l,i);let u=this.props.marksButtonStatus;return"enabled"===u&&this.props.shortcodesForParsing.length>0&&(u="disabled"),(0,w.createElement)(k.Fragment,null,(0,w.createElement)(ke.ContentAnalysis,{errorsResults:t,problemsResults:o,upsellResults:a,improvementsResults:s,considerationsResults:n,goodResults:r,activeMarker:this.props.activeMarker,onMarkButtonClick:this.handleMarkButtonClick,onEditButtonClick:this.handleEditButtonClick,marksButtonClassName:this.props.marksButtonClassName,editButtonClassName:this.props.editButtonClassName,marksButtonStatus:u,headingLevel:3,keywordKey:this.props.keywordKey,isPremium:this.props.isPremium,resultCategoryLabels:c,onResultChange:this.handleResultsChange,shouldUpsellHighlighting:this.props.shouldUpsellHighlighting,renderAIOptimizeButton:this.props.renderAIOptimizeButton,renderHighlightingUpsell:this.renderHighlightingUpsell,markButtonFactory:this.createMarkButton}))}}De.propTypes={results:_().array,upsellResults:_().array,marksButtonClassName:_().string,editButtonClassName:_().string,marksButtonStatus:_().oneOf(["enabled","disabled","hidden"]),setActiveMarker:_().func.isRequired,setMarkerPauseStatus:_().func.isRequired,setActiveAIFixesButton:_().func.isRequired,activeMarker:_().string,activeAIFixesButton:_().string,keywordKey:_().string,location:_().string,isPremium:_().bool,resultCategoryLabels:_().shape({errors:_().string,problems:_().string,improvements:_().string,considerations:_().string,goodResults:_().string}),shortcodesForParsing:_().array,shouldUpsellHighlighting:_().bool,highlightingUpsellLink:_().string,renderAIOptimizeButton:_().func},De.defaultProps={results:null,upsellResults:[],marksButtonStatus:"enabled",marksButtonClassName:"",editButtonClassName:"",activeMarker:null,activeAIFixesButton:null,keywordKey:"",location:"",isPremium:!1,resultCategoryLabels:{},shortcodesForParsing:[],shouldUpsellHighlighting:!1,highlightingUpsellLink:"",renderAIOptimizeButton:()=>{}};const $e=De,We=(0,pe.compose)([(0,me.withSelect)((e=>{const{getActiveMarker:t,getIsPremium:s,getShortcodesForParsing:r,getActiveAIFixesButton:n}=e("yoast-seo/editor");return{activeMarker:t(),isPremium:s(),shortcodesForParsing:r(),activeAIFixesButton:n()}})),(0,me.withDispatch)((e=>{const{setActiveMarker:t,setMarkerPauseStatus:s,setActiveAIFixesButton:r}=e("yoast-seo/editor");return{setActiveMarker:t,setMarkerPauseStatus:s,setActiveAIFixesButton:r}}))])($e),je=window.yoast.relatedKeyphraseSuggestions;function He(e){const{requestLimitReached:t,isSuccess:s,response:r,requestHasData:n,relatedKeyphrases:o}=e;return t?"requestLimitReached":!s&&function(e){return"invalid_json"===(null==e?void 0:e.code)||"fetch_error"===(null==e?void 0:e.code)||!(0,l.isEmpty)(e)&&"error"in e}(r)?"requestFailed":n?function(e){return e&&e.length>=4}(o)?"maxRelatedKeyphrases":void 0:"requestEmpty"}function Ue(e){var t,s;const{keyphrase:r="",relatedKeyphrases:n=[],renderAction:o=null,requestLimitReached:a=!1,countryCode:i="us",setCountry:l,newRequest:c,response:d={},isRtl:u=!1,userLocale:p="en_US",isPending:m=!1,isPremium:h=!1,semrushUpsellLink:g="",premiumUpsellLink:y=""}=e,[f,b]=(0,k.useState)(i),v=(0,k.useCallback)((async()=>{c(i,r),b(i)}),[i,r,c]);return(0,w.createElement)(ve.Root,{context:{isRtl:u}},!a&&!h&&(0,w.createElement)(je.PremiumUpsell,{url:y,className:"yst-mb-4"}),!a&&(0,w.createElement)(je.CountrySelector,{countryCode:i,activeCountryCode:f,onChange:l,onClick:v,className:"yst-mb-4",userLocale:p.split("_")[0]}),!m&&(0,w.createElement)(je.UserMessage,{variant:He(e),upsellLink:g}),(0,w.createElement)(je.KeyphrasesTable,{relatedKeyphrases:n,columnNames:null==d||null===(t=d.results)||void 0===t?void 0:t.columnNames,data:null==d||null===(s=d.results)||void 0===s?void 0:s.rows,isPending:m,renderButton:o,className:"yst-mt-4"}))}Ue.propTypes={keyphrase:_().string,relatedKeyphrases:_().array,renderAction:_().func,requestLimitReached:_().bool,countryCode:_().string.isRequired,setCountry:_().func.isRequired,newRequest:_().func.isRequired,response:_().object,isRtl:_().bool,userLocale:_().string,isPending:_().bool,isPremium:_().bool,semrushUpsellLink:_().string,premiumUpsellLink:_().string};const Ye=(0,pe.compose)([(0,me.withSelect)((e=>{const{getFocusKeyphrase:t,getSEMrushSelectedCountry:s,getSEMrushRequestLimitReached:r,getSEMrushRequestResponse:n,getSEMrushRequestIsSuccess:o,getSEMrushIsRequestPending:a,getSEMrushRequestHasData:i,getSEMrushRequestKeyphrase:l,getPreference:c,getIsPremium:d,selectLinkParams:u}=e("yoast-seo/editor");return{keyphrase:t(),countryCode:s(),requestLimitReached:r(),response:n(),isSuccess:o(),isPending:a(),requestHasData:i(),lastRequestKeyphrase:l(),isRtl:c("isRtl",!1),userLocale:c("userLocale","en_US"),isPremium:d(),semrushUpsellLink:(0,_e.addQueryArgs)("https://yoa.st/semrush-prices",u()),premiumUpsellLink:(0,_e.addQueryArgs)("https://yoa.st/413",u())}})),(0,me.withDispatch)((e=>{const{setSEMrushChangeCountry:t,setSEMrushNewRequest:s}=e("yoast-seo/editor");return{setCountry:e=>{t(e)},newRequest:(e,t)=>{s(e,t)}}}))])(Ue),ze=window.moment;var Ve=s.n(ze),Ge=s(6746);const Qe=(0,I.makeOutboundLink)(),Xe=e=>{const t=(0,d.sprintf)(/* translators: %1$d expands to the amount of allowed keyphrases on a free account, %2$s expands to a link to Wincher plans. */
(0,d.__)("You've reached the maximum amount of %1$d keyphrases you can add to your Wincher account. If you wish to add more keyphrases, please %2$s.","wordpress-seo"),e.limit,"{{updateWincherPlanLink/}}");return(0,w.createElement)(R.Alert,{type:"error"},(0,Ge.Z)({mixedString:t,components:{updateWincherPlanLink:(0,w.createElement)(Qe,{href:wpseoAdminGlobalL10n["links.wincher.pricing"]},(0,d.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,d.__)("upgrade your %s plan","wordpress-seo"),"Wincher"))}}))};Xe.propTypes={limit:_().number},Xe.defaultProps={limit:10};const Ze=Xe,Je=()=>(0,w.createElement)(R.Alert,{type:"error"},(0,d.__)("Something went wrong while tracking the ranking position(s) of your page. Please try again later.","wordpress-seo")),et=window.wp.apiFetch;var tt=s.n(et);async function st(e,t,s,r=200){try{const n=await e();return!!n&&(n.status===r?t(n):s(n))}catch(e){console.error(e.message)}}async function rt(e){try{return await tt()(e)}catch(e){return e.error&&e.status?e:e instanceof Response&&await e.json()}}async function nt(e){return(0,l.isArray)(e)||(e=[e]),await rt({path:"yoast/v1/wincher/keyphrases/track",method:"POST",data:{keyphrases:e}})}const ot=E().p`
	color: ${T.colors.$color_pink_dark};
	font-size: 14px;
	font-weight: 700;
	margin: 13px 0 10px;
`,at=E()(R.SvgIcon)`
	margin-right: 5px;
	vertical-align: middle;
`,it=E().button`
	position: absolute;
	top: 9px;
	right: 9px;
	border: none;
    background: none;
    cursor: pointer;
`,lt=E().p`
	font-size: 13px;
	font-weight: 500;
	margin: 10px 0 13px;
`,ct=E().div`
	position: relative;
	background: ${e=>e.isTitleShortened?"#F5F7F7":"transparent"};
	border: 1px solid #C7C7C7;
	border-left: 4px solid${T.colors.$color_pink_dark};
	padding: 0 16px;
	margin-bottom: 1.5em;
`,dt=e=>{const[t,s]=(0,k.useState)(null);return(0,k.useEffect)((()=>{e&&!t&&async function(){return await rt({path:"yoast/v1/wincher/account/limit",method:"GET"})}().then((e=>s(e)))}),[t]),t};dt.propTypes={limit:_().bool.isRequired};const ut=({limit:e,usage:t,isTitleShortened:s,isFreeAccount:r})=>{const n=(0,d.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,d.__)("Your are tracking %1$s out of %2$s keyphrases included in your free account.","wordpress-seo"),t,e),o=(0,d.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,d.__)("Your are tracking %1$s out of %2$s keyphrases included in your account.","wordpress-seo"),t,e),a=r?n:o,i=(0,d.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,d.__)("Keyphrases tracked: %1$s/%2$s","wordpress-seo"),t,e),l=s?i:a;return(0,w.createElement)(ot,null,s&&(0,w.createElement)(at,{icon:"exclamation-triangle",color:T.colors.$color_pink_dark,size:"14px"}),l)};ut.propTypes={limit:_().number.isRequired,usage:_().number.isRequired,isTitleShortened:_().bool,isFreeAccount:_().bool};const pt=(0,I.makeOutboundLink)(),mt=({discount:e,months:t})=>{const s=(0,w.createElement)(pt,{href:wpseoAdminGlobalL10n["links.wincher.upgrade"],style:{fontWeight:600}},(0,d.sprintf)(/* Translators: %s : Expands to "Wincher". */
(0,d.__)("Click here to upgrade your %s plan","wordpress-seo"),"Wincher"));if(!e||!t)return(0,w.createElement)(lt,null,s);const r=100*e,n=(0,d.sprintf)(
/* Translators: %1$s expands to upgrade account link.
   * %2$s expands to the upgrade discount value.
   * %3$s expands to the upgrade discount duration e.g. 2 months.
   */
(0,d.__)("%1$s and get an exclusive %2$s discount for %3$s month(s).","wordpress-seo"),"{{wincherAccountUpgradeLink/}}",r+"%",t);return(0,w.createElement)(lt,null,(0,Ge.Z)({mixedString:n,components:{wincherAccountUpgradeLink:s}}))};mt.propTypes={discount:_().number,months:_().number};const ht=({onClose:e,isTitleShortened:t,trackingInfo:s})=>{const r=(()=>{const[e,t]=(0,k.useState)(null);return(0,k.useEffect)((()=>{e||async function(){return await rt({path:"yoast/v1/wincher/account/upgrade-campaign",method:"GET"})}().then((e=>t(e)))}),[e]),e})();if(null===s)return null;const{limit:n,usage:o}=s;if(!(n&&o/n>=.8))return null;const a=Boolean(null==r?void 0:r.discount);return(0,w.createElement)(ct,{isTitleShortened:t},e&&(0,w.createElement)(it,{type:"button","aria-label":(0,d.__)("Close the upgrade callout","wordpress-seo"),onClick:e},(0,w.createElement)(R.SvgIcon,{icon:"times-circle",color:T.colors.$color_pink_dark,size:"14px"})),(0,w.createElement)(ut,{...s,isTitleShortened:t,isFreeAccount:a}),(0,w.createElement)(mt,{discount:null==r?void 0:r.discount,months:null==r?void 0:r.months}))};ht.propTypes={onClose:_().func,isTitleShortened:_().bool,trackingInfo:_().object};const gt=ht,yt=()=>(0,w.createElement)(R.Alert,{type:"success"},(0,d.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,d.__)("You have successfully connected to %s! You can now track the SEO performance for the keyphrase(s) of this page.","wordpress-seo"),"Wincher")),ft=()=>(0,w.createElement)(R.Alert,{type:"info"},(0,d.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,d.__)("%s is currently tracking the ranking position(s) of your page. This may take a few minutes. Please wait or check back later.","wordpress-seo"),"Wincher")),bt=({data:e,mapChartDataToTableData:t,dataTableCaption:s,dataTableHeaderLabels:r,isDataTableVisuallyHidden:n})=>e.length!==r.length?(0,w.createElement)("p",null,(0,d.__)("The number of headers and header labels don't match.","wordpress-seo")):(0,w.createElement)("div",{className:n?"screen-reader-text":null},(0,w.createElement)("table",null,(0,w.createElement)("caption",null,s),(0,w.createElement)("thead",null,(0,w.createElement)("tr",null,r.map(((e,t)=>(0,w.createElement)("th",{key:t},e))))),(0,w.createElement)("tbody",null,(0,w.createElement)("tr",null,e.map(((e,s)=>(0,w.createElement)("td",{key:s},t(e.y))))))));bt.propTypes={data:_().arrayOf(_().shape({x:_().number,y:_().number})).isRequired,mapChartDataToTableData:_().func,dataTableCaption:_().string.isRequired,dataTableHeaderLabels:_().array.isRequired,isDataTableVisuallyHidden:_().bool},bt.defaultProps={mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const wt=bt,kt=({data:e,width:t,height:s,fillColor:r,strokeColor:n,strokeWidth:o,className:a,mapChartDataToTableData:i,dataTableCaption:l,dataTableHeaderLabels:c,isDataTableVisuallyHidden:d})=>{const u=Math.max(1,Math.max(...e.map((e=>e.x)))),p=Math.max(1,Math.max(...e.map((e=>e.y)))),m=s-o,h=e.map((e=>`${e.x/u*t},${m-e.y/p*m+o}`)).join(" "),g=`0,${m+o} `+h+` ${t},${m+o}`;return(0,w.createElement)(k.Fragment,null,(0,w.createElement)("svg",{width:t,height:s,viewBox:`0 0 ${t} ${s}`,className:a,role:"img","aria-hidden":"true",focusable:"false"},(0,w.createElement)("polygon",{fill:r,points:g}),(0,w.createElement)("polyline",{fill:"none",stroke:n,strokeWidth:o,strokeLinejoin:"round",strokeLinecap:"round",points:h})),i&&(0,w.createElement)(wt,{data:e,mapChartDataToTableData:i,dataTableCaption:l,dataTableHeaderLabels:c,isDataTableVisuallyHidden:d}))};kt.propTypes={data:_().arrayOf(_().shape({x:_().number,y:_().number})).isRequired,width:_().number.isRequired,height:_().number.isRequired,fillColor:_().string,strokeColor:_().string,strokeWidth:_().number,className:_().string,mapChartDataToTableData:_().func,dataTableCaption:_().string.isRequired,dataTableHeaderLabels:_().array.isRequired,isDataTableVisuallyHidden:_().bool},kt.defaultProps={fillColor:null,strokeColor:"#000000",strokeWidth:1,className:"",mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const vt=kt,Et=()=>(0,w.createElement)("p",{className:"yoast-wincher-seo-performance-modal__loading-message"},(0,d.__)("Tracking the ranking position…","wordpress-seo")," ",(0,w.createElement)(R.SvgIcon,{icon:"loading-spinner"})),xt=E()(R.SvgIcon)`
	margin-left: 2px;
	flex-shrink: 0;
	rotate: ${e=>e.isImproving?"-90deg":"90deg"};
`,_t=E().span`
	color: ${e=>e.isImproving?"#69AB56":"#DC3332"};
	font-size: 13px;
	font-weight: 600;
	line-height: 20px;
	margin-right: 2px;
	margin-left: 12px;
`,Rt=E().td`
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,It=E().td`
	padding-left: 2px !important;
`,Tt=E().td.attrs({className:"yoast-table--nopadding"})`
	& > div {
		justify-content: center;
	}
`,St=E().div`
	display: flex;
	align-items: center;
	& > a {
		box-sizing: border-box;
	}
`,Ct=E().button`
	background: none;
	color: inherit;
	border: none;
	padding: 0;
	font: inherit;
	cursor: pointer;
	outline: inherit;
    display: flex;
    align-items: center;
`,Lt=E().tr`
	background-color: ${e=>e.isEnabled?"#FFFFFF":"#F9F9F9"} !important;
`;function Pt(e){return Math.round(100*e)}function At({chartData:e}){if((0,l.isEmpty)(e)||(0,l.isEmpty)(e.position))return"?";const t=function(e){return Array.from({length:e.position.history.length},((e,t)=>t+1)).map((e=>(0,d.sprintf)((0,d._n)("%d day","%d days",e,"wordpress-seo"),e)))}(e),s=e.position.history.map(((e,t)=>({x:t,y:101-e.value})));return(0,w.createElement)(vt,{width:66,height:24,data:s,strokeWidth:1.8,strokeColor:"#498afc",fillColor:"#ade3fc",mapChartDataToTableData:Pt,dataTableCaption:(0,d.__)("Keyphrase position in the last 90 days on a scale from 0 to 100.","wordpress-seo"),dataTableHeaderLabels:t})}At.propTypes={chartData:_().object},At.defaultProps={chartData:{}};const Ot=({rowData:e})=>{var t;if(null==e||null===(t=e.position)||void 0===t||!t.change)return(0,w.createElement)(At,{chartData:e});const s=e.position.change<0;return(0,w.createElement)(k.Fragment,null,(0,w.createElement)(At,{chartData:e}),(0,w.createElement)(_t,{isImproving:s},Math.abs(e.position.change)),(0,w.createElement)(xt,{icon:"caret-right",color:s?"#69AB56":"#DC3332",size:"14px",isImproving:s}))};function Bt(e){var t;const{keyphrase:s,rowData:r,onTrackKeyphrase:n,onUntrackKeyphrase:o,isFocusKeyphrase:a,isDisabled:i,isLoading:c,isSelected:u,onSelectKeyphrases:p}=e,m=!(0,l.isEmpty)(r),h=!(0,l.isEmpty)(null==r||null===(t=r.position)||void 0===t?void 0:t.history),g=(0,k.useCallback)((()=>{i||(m?o(s,r.id):n(s))}),[s,n,o,m,r,i]),y=(0,k.useCallback)((()=>{p((e=>u?e.filter((e=>e!==s)):e.concat(s)))}),[p,u,s]);return(0,w.createElement)(Lt,{isEnabled:m},(0,w.createElement)(Rt,null,h&&(0,w.createElement)(R.Checkbox,{id:"select-"+s,onChange:y,checked:u,label:""})),(0,w.createElement)(It,null,s,a&&(0,w.createElement)("span",null,"*")),function(e){const{rowData:t,websiteId:s,keyphrase:r,onSelectKeyphrases:n}=e,o=(0,k.useCallback)((()=>{n([r])}),[n,r]),a=!(0,l.isEmpty)(t),i=t&&t.updated_at&&Ve()(t.updated_at)>=Ve()().subtract(7,"days"),c=t?`https://app.wincher.com/websites/${s}/keywords?serp=${t.id}&utm_medium=plugin&utm_source=yoast&referer=yoast&partner=yoast`:null;return a?i?(0,w.createElement)(k.Fragment,null,(0,w.createElement)("td",null,(0,w.createElement)(St,null,function(e){return!e||!e.position||e.position.value>100?"> 100":e.position.value}(t),(0,w.createElement)(R.ButtonStyledLink,{variant:"secondary",href:c,style:{height:28,marginLeft:12},rel:"noopener",target:"_blank"},(0,d.__)("View","wordpress-seo")))),(0,w.createElement)("td",{className:"yoast-table--nopadding"},(0,w.createElement)(Ct,{type:"button",onClick:o},(0,w.createElement)(Ot,{rowData:t}))),(0,w.createElement)("td",null,(u=t.updated_at,Ve()(u).fromNow()))):(0,w.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,w.createElement)(Et,null)):(0,w.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,w.createElement)("i",null,(0,d.__)("Activate tracking to show the ranking position","wordpress-seo")));var u}(e),(0,w.createElement)(Tt,null,function({keyphrase:e,isEnabled:t,toggleAction:s,isLoading:r}){return r?(0,w.createElement)(R.SvgIcon,{icon:"loading-spinner"}):(0,w.createElement)(R.Toggle,{id:`toggle-keyphrase-tracking-${e}`,className:"wincher-toggle",isEnabled:t,onSetToggleState:s,showToggleStateLabel:!1})}({keyphrase:s,isEnabled:m,toggleAction:g,isLoading:c})))}Ot.propTypes={rowData:_().object},Bt.propTypes={rowData:_().object,keyphrase:_().string.isRequired,onTrackKeyphrase:_().func,onUntrackKeyphrase:_().func,isFocusKeyphrase:_().bool,isDisabled:_().bool,isLoading:_().bool,websiteId:_().string,isSelected:_().bool.isRequired,onSelectKeyphrases:_().func.isRequired},Bt.defaultProps={rowData:{},onTrackKeyphrase:()=>{},onUntrackKeyphrase:()=>{},isFocusKeyphrase:!1,isDisabled:!1,isLoading:!1,websiteId:""};const Nt=(0,I.makeOutboundLink)(),qt=E().span`
	display: block;
	font-style: italic;

	@media (min-width: 782px) {
		display: inline;
		position: absolute;
		${(0,I.getDirectionalStyle)("right","left")}: 8px;
	}
`,Mt=E().div`
	width: 100%;
	overflow-y: auto;
`,Ft=E().th`
	pointer-events: ${e=>e.isDisabled?"none":"initial"};
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,Kt=E().th`
	padding-left: 2px !important;
`,Dt=e=>{const t=(0,k.useRef)();return(0,k.useEffect)((()=>{t.current=e})),t.current},$t=(0,l.debounce)((async function(e=null,t=null,s=null,r){return await rt({path:"yoast/v1/wincher/keyphrases",method:"POST",data:{keyphrases:e,permalink:s,startAt:t},signal:r})}),500,{leading:!0}),Wt=e=>{const{addTrackedKeyphrase:t,isLoggedIn:s,keyphrases:r,permalink:n,removeTrackedKeyphrase:o,setKeyphraseLimitReached:a,setRequestFailed:i,setRequestSucceeded:c,setTrackedKeyphrases:u,setHasTrackedAll:p,trackAll:m,trackedKeyphrases:h,isNewlyAuthenticated:g,websiteId:y,focusKeyphrase:f,newRequest:b,startAt:v,selectedKeyphrases:E,onSelectKeyphrases:x}=e,_=(0,k.useRef)(),I=(0,k.useRef)(),T=(0,k.useRef)(!1),[S,C]=(0,k.useState)([]),L=(0,k.useCallback)((e=>{const t=e.toLowerCase();return h&&!(0,l.isEmpty)(h)&&h.hasOwnProperty(t)?h[t]:null}),[h]),P=(0,k.useMemo)((()=>async()=>{await st((()=>(I.current&&I.current.abort(),I.current="undefined"==typeof AbortController?null:new AbortController,$t(r,v,n,I.current.signal))),(e=>{c(e),u(e.results)}),(e=>{i(e)}))}),[c,i,u,r,n,v]),A=(0,k.useCallback)((async e=>{const s=(Array.isArray(e)?e:[e]).map((e=>e.toLowerCase()));C((e=>[...e,...s])),await st((()=>nt(s)),(e=>{c(e),t(e.results),P()}),(e=>{400===e.status&&e.limit&&a(e.limit),i(e)}),201),C((e=>(0,l.without)(e,...s)))}),[c,i,a,t,P]),O=(0,k.useCallback)((async(e,t)=>{e=e.toLowerCase(),C((t=>[...t,e])),await st((()=>async function(e){return await rt({path:"yoast/v1/wincher/keyphrases/untrack",method:"DELETE",data:{keyphraseID:e}})}(t)),(t=>{c(t),o(e)}),(e=>{i(e)})),C((t=>(0,l.without)(t,e)))}),[c,o,i]),B=(0,k.useCallback)((async e=>{b(),await A(e)}),[b,A]),N=Dt(n),q=Dt(r),M=Dt(v),F=n&&v;(0,k.useEffect)((()=>{s&&F&&(n!==N||(0,l.difference)(r,q).length||v!==M)&&P()}),[s,n,N,r,q,P,F,v,M]),(0,k.useEffect)((()=>{if(s&&m&&null!==h){const e=r.filter((e=>!L(e)));e.length&&A(e),p()}}),[s,m,h,A,p,L,r]),(0,k.useEffect)((()=>{g&&!T.current&&(P(),T.current=!0)}),[g,P]),(0,k.useEffect)((()=>{if(s&&!(0,l.isEmpty)(h))return(0,l.filter)(h,(e=>(0,l.isEmpty)(e.updated_at))).length>0&&(_.current=setInterval((()=>{P()}),1e4)),()=>{clearInterval(_.current)}}),[s,h,P]);const K=s&&null===h,D=(0,k.useMemo)((()=>(0,l.isEmpty)(h)?[]:Object.values(h).filter((e=>{var t;return!(0,l.isEmpty)(null==e||null===(t=e.position)||void 0===t?void 0:t.history)})).map((e=>e.keyword))),[h]),$=(0,k.useMemo)((()=>E.length>0&&D.length>0&&D.every((e=>E.includes(e)))),[E,D]),W=(0,k.useCallback)((()=>{x($?[]:D)}),[x,$,D]),j=(0,k.useMemo)((()=>(0,l.orderBy)(r,[e=>Object.values(h||{}).map((e=>e.keyword)).includes(e)],["desc"])),[r,h]);return r&&!(0,l.isEmpty)(r)&&(0,w.createElement)(k.Fragment,null,(0,w.createElement)(Mt,null,(0,w.createElement)("table",{className:"yoast yoast-table"},(0,w.createElement)("thead",null,(0,w.createElement)("tr",null,(0,w.createElement)(Ft,{isDisabled:0===D.length},(0,w.createElement)(R.Checkbox,{id:"select-all",onChange:W,checked:$,label:""})),(0,w.createElement)(Kt,{scope:"col",abbr:(0,d.__)("Keyphrase","wordpress-seo")},(0,d.__)("Keyphrase","wordpress-seo")),(0,w.createElement)("th",{scope:"col",abbr:(0,d.__)("Position","wordpress-seo")},(0,d.__)("Position","wordpress-seo")),(0,w.createElement)("th",{scope:"col",abbr:(0,d.__)("Position over time","wordpress-seo")},(0,d.__)("Position over time","wordpress-seo")),(0,w.createElement)("th",{scope:"col",abbr:(0,d.__)("Last updated","wordpress-seo")},(0,d.__)("Last updated","wordpress-seo")),(0,w.createElement)("th",{scope:"col",abbr:(0,d.__)("Tracking","wordpress-seo")},(0,d.__)("Tracking","wordpress-seo")))),(0,w.createElement)("tbody",null,j.map(((e,t)=>(0,w.createElement)(Bt,{key:`trackable-keyphrase-${t}`,keyphrase:e,onTrackKeyphrase:B,onUntrackKeyphrase:O,rowData:L(e),isFocusKeyphrase:e===f.trim().toLowerCase(),websiteId:y,isDisabled:!s,isLoading:K||S.indexOf(e.toLowerCase())>=0,isSelected:E.includes(e),onSelectKeyphrases:x})))))),(0,w.createElement)("p",{style:{marginBottom:0,position:"relative"}},(0,w.createElement)(Nt,{href:wpseoAdminGlobalL10n["links.wincher.login"]},(0,d.sprintf)(/* translators: %s expands to Wincher */
(0,d.__)("Get more insights over at %s","wordpress-seo"),"Wincher")),(0,w.createElement)(qt,null,(0,d.__)("* focus keyphrase","wordpress-seo"))))};Wt.propTypes={addTrackedKeyphrase:_().func.isRequired,isLoggedIn:_().bool,isNewlyAuthenticated:_().bool,keyphrases:_().array,newRequest:_().func.isRequired,removeTrackedKeyphrase:_().func.isRequired,setRequestFailed:_().func.isRequired,setKeyphraseLimitReached:_().func.isRequired,setRequestSucceeded:_().func.isRequired,setTrackedKeyphrases:_().func.isRequired,setHasTrackedAll:_().func.isRequired,trackAll:_().bool,trackedKeyphrases:_().object,websiteId:_().string,permalink:_().string.isRequired,focusKeyphrase:_().string,startAt:_().string,selectedKeyphrases:_().arrayOf(_().string).isRequired,onSelectKeyphrases:_().func.isRequired},Wt.defaultProps={isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],trackAll:!1,websiteId:"",focusKeyphrase:""};const jt=Wt,Ht=(0,pe.compose)([(0,me.withSelect)((e=>{const{getWincherWebsiteId:t,getWincherTrackableKeyphrases:s,getWincherLoginStatus:r,getWincherPermalink:n,getFocusKeyphrase:o,isWincherNewlyAuthenticated:a,shouldWincherTrackAll:i}=e("yoast-seo/editor");return{focusKeyphrase:o(),keyphrases:s(),isLoggedIn:r(),trackAll:i(),websiteId:t(),isNewlyAuthenticated:a(),permalink:n()}})),(0,me.withDispatch)((e=>{const{setWincherNewRequest:t,setWincherRequestSucceeded:s,setWincherRequestFailed:r,setWincherSetKeyphraseLimitReached:n,setWincherTrackedKeyphrases:o,setWincherTrackingForKeyphrase:a,setWincherTrackAllKeyphrases:i,unsetWincherTrackingForKeyphrase:l}=e("yoast-seo/editor");return{newRequest:()=>{t()},setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{r(e)},setKeyphraseLimitReached:e=>{n(e)},addTrackedKeyphrase:e=>{a(e)},removeTrackedKeyphrase:e=>{l(e)},setTrackedKeyphrases:e=>{o(e)},setHasTrackedAll:()=>{i(!1)}}}))])(jt),Ut=(0,I.makeOutboundLink)(),Yt=(0,I.makeOutboundLink)(),zt=()=>{const e=(0,d.sprintf)(/* translators: %1$s expands to a link to Wincher, %2$s expands to a link to the keyphrase tracking article on Yoast.com */
(0,d.__)("With %1$s you can track the ranking position of your page in the search results based on your keyphrase(s). %2$s","wordpress-seo"),"{{wincherLink/}}","{{wincherReadMoreLink/}}");return(0,w.createElement)("p",null,(0,Ge.Z)({mixedString:e,components:{wincherLink:(0,w.createElement)(Ut,{href:wpseoAdminGlobalL10n["links.wincher.website"]},"Wincher"),wincherReadMoreLink:(0,w.createElement)(Yt,{href:wpseoAdminL10n["shortlinks.wincher.seo_performance"]},(0,d.__)("Read more about keyphrase tracking with Wincher","wordpress-seo"))}}))},Vt=()=>(0,w.createElement)(R.Alert,{type:"error"},(0,d.__)("No keyphrase has been set. Please set a keyphrase first.","wordpress-seo")),Gt=()=>(0,w.createElement)(R.Alert,{type:"info"},(0,d.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,d.__)("Automatic tracking of keyphrases is enabled. Your keyphrase(s) will automatically be tracked by %s when you publish your post.","wordpress-seo"),"Wincher"));class Qt{constructor(e,t={},s={}){this.url=e,this.origin=new URL(e).origin,this.eventHandlers=Object.assign({success:{type:"",callback:()=>{}},error:{type:"",callback:()=>{}}},t),this.options=Object.assign({height:570,width:340,title:""},s),this.popup=null,this.createPopup=this.createPopup.bind(this),this.messageHandler=this.messageHandler.bind(this),this.getPopup=this.getPopup.bind(this)}createPopup(){const{height:e,width:t,title:s}=this.options,r=["top="+(window.top.outerHeight/2+window.top.screenY-e/2),"left="+(window.top.outerWidth/2+window.top.screenX-t/2),"width="+t,"height="+e,"resizable=1","scrollbars=1","status=0"];this.popup&&!this.popup.closed||(this.popup=window.open(this.url,s,r.join(","))),this.popup&&this.popup.focus(),window.addEventListener("message",this.messageHandler,!1)}async messageHandler(e){const{data:t,source:s,origin:r}=e;r===this.origin&&this.popup===s&&(t.type===this.eventHandlers.success.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.success.callback(t)),t.type===this.eventHandlers.error.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.error.callback(t)))}getPopup(){return this.popup}isClosed(){return!this.popup||this.popup.closed}focus(){this.isClosed()||this.popup.focus()}}const Xt=e=>{const t=(0,d.sprintf)(/* translators: %s expands to a link to open the Wincher login popup. */
(0,d.__)("It seems like something went wrong when retrieving your website's data. Please %s and try again.","wordpress-seo"),"{{reconnectToWincher/}}","Wincher");return(0,w.createElement)(R.Alert,{type:"error",className:e.className},(0,Ge.Z)({mixedString:t,components:{reconnectToWincher:(0,w.createElement)("a",{href:"#",onClick:t=>{t.preventDefault(),e.onReconnect()}},(0,d.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,d.__)("reconnect to %s","wordpress-seo"),"Wincher"))}}))};Xt.propTypes={onReconnect:_().func.isRequired,className:_().string},Xt.defaultProps={className:""};const Zt=Xt,Jt=()=>(0,w.createElement)(R.Alert,{type:"error"},(0,d.__)("Before you can track your SEO performance make sure to set either the post’s title and save it as a draft or manually set the post’s slug.","wordpress-seo")),es=window.yoast["chart.js"],ts="label";function ss(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function rs(e,t){e.labels=t}function ns(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ts;const r=[];e.datasets=t.map((t=>{const n=e.datasets.find((e=>e[s]===t[s]));return n&&t.data&&!r.includes(n)?(r.push(n),Object.assign(n,t),n):{...t}}))}function os(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ts;const s={labels:[],datasets:[]};return rs(s,e.labels),ns(s,e.datasets,t),s}function as(e,t){const{height:s=150,width:r=300,redraw:n=!1,datasetIdKey:o,type:a,data:i,options:l,plugins:c=[],fallbackContent:d,updateMode:u,...p}=e,m=(0,w.useRef)(null),h=(0,w.useRef)(),g=()=>{m.current&&(h.current=new es.Chart(m.current,{type:a,data:os(i,o),options:l&&{...l},plugins:c}),ss(t,h.current))},y=()=>{ss(t,null),h.current&&(h.current.destroy(),h.current=null)};return(0,w.useEffect)((()=>{!n&&h.current&&l&&function(e,t){const s=e.options;s&&t&&Object.assign(s,t)}(h.current,l)}),[n,l]),(0,w.useEffect)((()=>{!n&&h.current&&rs(h.current.config.data,i.labels)}),[n,i.labels]),(0,w.useEffect)((()=>{!n&&h.current&&i.datasets&&ns(h.current.config.data,i.datasets,o)}),[n,i.datasets]),(0,w.useEffect)((()=>{h.current&&(n?(y(),setTimeout(g)):h.current.update(u))}),[n,l,i.labels,i.datasets,u]),(0,w.useEffect)((()=>{h.current&&(y(),setTimeout(g))}),[a]),(0,w.useEffect)((()=>(g(),()=>y())),[]),w.createElement("canvas",Object.assign({ref:m,role:"img",height:s,width:r},p),d)}const is=(0,w.forwardRef)(as);function ls(e,t){return es.Chart.register(t),(0,w.forwardRef)(((t,s)=>w.createElement(is,Object.assign({},t,{ref:s,type:e}))))}const cs=ls("line",es.LineController),ds={datetime:"MMM D, YYYY, h:mm:ss a",millisecond:"h:mm:ss.SSS a",second:"h:mm:ss a",minute:"h:mm a",hour:"hA",day:"MMM D",week:"ll",month:"MMM YYYY",quarter:"[Q]Q - YYYY",year:"YYYY"};es._adapters._date.override("function"==typeof Ve()?{_id:"moment",formats:function(){return ds},parse:function(e,t){return"string"==typeof e&&"string"==typeof t?e=Ve()(e,t):e instanceof Ve()||(e=Ve()(e)),e.isValid()?e.valueOf():null},format:function(e,t){return Ve()(e).format(t)},add:function(e,t,s){return Ve()(e).add(t,s).valueOf()},diff:function(e,t,s){return Ve()(e).diff(Ve()(t),s)},startOf:function(e,t,s){return e=Ve()(e),"isoWeek"===t?(s=Math.trunc(Math.min(Math.max(0,s),6)),e.isoWeekday(s).startOf("day").valueOf()):e.startOf(t).valueOf()},endOf:function(e,t){return Ve()(e).endOf(t).valueOf()}}:{}),Math.PI,Number.POSITIVE_INFINITY,Math.log10,Math.sign,"undefined"==typeof window||window.requestAnimationFrame,new Map,Object.create(null),Object.create(null),Number.EPSILON;const us=["top","right","bottom","left"];function ps(e,t,s){const r={};s=s?"-"+s:"";for(let n=0;n<4;n++){const o=us[n];r[o]=parseFloat(e[t+"-"+o+s])||0}return r.width=r.left+r.right,r.height=r.top+r.bottom,r}!function(){let e=!1;try{const t={get passive(){return e=!0,!1}};window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch(e){}}(),es.Chart.register(es.CategoryScale,es.LineController,es.LineElement,es.PointElement,es.LinearScale,es.TimeScale,es.Legend,es.Tooltip);const ms=["#ff983b","#ffa3f7","#3798ff","#ff3b3b","#acce81","#b51751","#3949ab","#26c6da","#ccb800","#de66ff","#4db6ac","#ffab91","#45f5f1","#77f210","#90a4ae","#ffd54f","#006b5e","#8ec7d2","#b1887c","#cc9300"];function hs({datasets:e,isChartShown:t,keyphrases:s}){if(!t)return null;const r=(0,k.useMemo)((()=>Object.fromEntries([...s].sort().map(((e,t)=>[e,ms[t%ms.length]])))),[s]),n=e.map((e=>{const t=r[e.label];return{...e,data:e.data.map((({datetime:e,value:t})=>({x:e,y:t}))),lineTension:0,pointRadius:1,pointHoverRadius:4,borderWidth:2,pointHitRadius:6,backgroundColor:t,borderColor:t}})).filter((e=>!1!==e.selected));return(0,w.createElement)(cs,{height:100,data:{datasets:n},options:{plugins:{legend:{display:!0,position:"bottom",labels:{color:"black",usePointStyle:!0,boxHeight:7,boxWidth:7},onClick:l.noop},tooltip:{enabled:!0,callbacks:{title:e=>Ve()(e[0].raw.x).utc().format("YYYY-MM-DD")},titleAlign:"center",mode:"xPoint",position:"nearest",usePointStyle:!0,boxHeight:7,boxWidth:7,boxPadding:2}},scales:{x:{bounds:"ticks",type:"time",time:{unit:"day",minUnit:"day"},grid:{display:!1},ticks:{autoSkipPadding:50,maxRotation:0,color:"black"}},y:{bounds:"ticks",offset:!0,reverse:!0,ticks:{precision:0,color:"black"},max:101}}}})}es.Interaction.modes.xPoint=(e,t,s,r)=>{const n=function(e,t){if("native"in e)return e;const{canvas:s,currentDevicePixelRatio:r}=t,n=(m=s).ownerDocument.defaultView.getComputedStyle(m,null),o="border-box"===n.boxSizing,a=ps(n,"padding"),i=ps(n,"border","width"),{x:l,y:c,box:d}=function(e,t){const s=e.touches,r=s&&s.length?s[0]:e,{offsetX:n,offsetY:o}=r;let a,i,l=!1;if(((e,t,s)=>(e>0||t>0)&&(!s||!s.shadowRoot))(n,o,e.target))a=n,i=o;else{const e=t.getBoundingClientRect();a=r.clientX-e.left,i=r.clientY-e.top,l=!0}return{x:a,y:i,box:l}}(e,s),u=a.left+(d&&i.left),p=a.top+(d&&i.top);var m;let{width:h,height:g}=t;return o&&(h-=a.width+i.width,g-=a.height+i.height),{x:Math.round((l-u)/h*s.width/r),y:Math.round((c-p)/g*s.height/r)}}(t,e);let o=[];if(es.Interaction.evaluateInteractionItems(e,"x",n,((e,t,s)=>{e.inXRange(n.x,r)&&o.push({element:e,datasetIndex:t,index:s})})),0===o.length)return o;const a=o.reduce(((e,t)=>Math.abs(n.x-e.element.x)<Math.abs(n.x-t.element.x)?e:t)).element.x;return o=o.filter((e=>e.element.x===a)),o.some((e=>Math.abs(e.element.y-n.y)<10))?o:[]},hs.propTypes={datasets:_().arrayOf(_().shape({label:_().string.isRequired,data:_().arrayOf(_().shape({datetime:_().string.isRequired,value:_().number.isRequired})).isRequired,selected:_().bool})).isRequired,isChartShown:_().bool.isRequired,keyphrases:_().array.isRequired};const gs=({response:e,onLogin:t})=>[401,403,404].includes(e.status)?(0,w.createElement)(Zt,{onReconnect:t}):(0,w.createElement)(Je,null);gs.propTypes={response:_().object.isRequired,onLogin:_().func.isRequired};const ys=({isSuccess:e,response:t,allKeyphrasesMissRanking:s,onLogin:r,keyphraseLimitReached:n,limit:o})=>n?(0,w.createElement)(Ze,{limit:o}):(0,l.isEmpty)(t)||e?s?(0,w.createElement)(ft,null):null:(0,w.createElement)(gs,{response:t,onLogin:r});ys.propTypes={isSuccess:_().bool.isRequired,allKeyphrasesMissRanking:_().bool.isRequired,response:_().object,onLogin:_().func.isRequired,keyphraseLimitReached:_().bool.isRequired,limit:_().number.isRequired},ys.defaultProps={response:{}};let fs=null;const bs=async e=>{if(fs&&!fs.isClosed())return void fs.focus();const{url:t}=await async function(){return await rt({path:"yoast/v1/wincher/authorization-url",method:"GET"})}();fs=new Qt(t,{success:{type:"wincher:oauth:success",callback:t=>(async(e,t)=>{const{onAuthentication:s,setRequestSucceeded:r,setRequestFailed:n,keyphrases:o,addTrackedKeyphrase:a,setKeyphraseLimitReached:i}=e;await st((()=>async function(e){const{code:t,websiteId:s}=e;return await rt({path:"yoast/v1/wincher/authenticate",method:"POST",data:{code:t,websiteId:s}})}(t)),(async e=>{s(!0,!0,t.websiteId.toString()),r(e);const l=(Array.isArray(o)?o:[o]).map((e=>e.toLowerCase()));await st((()=>nt(l)),(e=>{r(e),a(e.results)}),(e=>{400===e.status&&e.limit&&i(e.limit),n(e)}),201);const c=fs.getPopup();c&&c.close()}),(async e=>n(e)))})(e,t)},error:{type:"wincher:oauth:error",callback:()=>e.onAuthentication(!1,!1)}},{title:"Wincher_login",width:500,height:700}),fs.createPopup()},ws=e=>e.isLoggedIn?null:(0,w.createElement)("p",null,(0,w.createElement)(R.NewButton,{onClick:e.onLogin,variant:"primary"},(0,d.sprintf)(/* translators: %s expands to Wincher */
(0,d.__)("Connect with %s","wordpress-seo"),"Wincher")));ws.propTypes={isLoggedIn:_().bool.isRequired,onLogin:_().func.isRequired};const ks=E().div`
	p {
		margin: 1em 0;
	}
`,vs=E().div`
	${e=>e.isDisabled&&"\n\t\topacity: .5;\n\t\tpointer-events: none;\n\t"};
`,Es=E().div`
	font-weight: var(--yoast-font-weight-bold);
	color: var(--yoast-color-label);
	font-size: var(--yoast-font-size-default);
`,xs=E().div.attrs({className:"yoast-field-group"})`
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 14px;
`,_s=E().div`
	margin: 8px 0;
`,Rs=Ve().utc().startOf("day"),Is=[{name:(0,d.__)("Last day","wordpress-seo"),value:Ve()(Rs).subtract(1,"days").format(),defaultIndex:1},{name:(0,d.__)("Last week","wordpress-seo"),value:Ve()(Rs).subtract(1,"week").format(),defaultIndex:2},{name:(0,d.__)("Last month","wordpress-seo"),value:Ve()(Rs).subtract(1,"month").format(),defaultIndex:3},{name:(0,d.__)("Last year","wordpress-seo"),value:Ve()(Rs).subtract(1,"year").format(),defaultIndex:0}],Ts=e=>{const{onSelect:t,selected:s,options:r,isLoggedIn:n}=e;return n?r.length<1?null:(0,w.createElement)("select",{className:"components-select-control__input",id:"wincher-period-picker",value:(null==s?void 0:s.value)||r[0].value,onChange:t},r.map((e=>(0,w.createElement)("option",{key:e.name,value:e.value},e.name)))):null};Ts.propTypes={onSelect:_().func.isRequired,selected:_().object,options:_().array.isRequired,isLoggedIn:_().bool.isRequired};const Ss=e=>{const{trackedKeyphrases:t,isLoggedIn:s,keyphrases:r,shouldTrackAll:n,permalink:o,historyDaysLimit:a}=e;if(!o&&s)return(0,w.createElement)(Jt,null);if(0===r.length)return(0,w.createElement)(Vt,null);const i=Ve()(Rs).subtract(a,"days"),c=Is.filter((e=>Ve()(e.value).isSameOrAfter(i))),u=(0,l.orderBy)(c,(e=>e.defaultIndex),"desc")[0],[p,m]=(0,k.useState)(u),[h,g]=(0,k.useState)([]),y=h.length>0,f=(0,pe.usePrevious)(t);(0,k.useEffect)((()=>{if(!(0,l.isEmpty)(t)&&(0,l.difference)(Object.keys(t),Object.keys(f||[])).length){const e=Object.values(t).map((e=>e.keyword));g(e)}}),[t,f]),(0,k.useEffect)((()=>{m(u)}),[null==u?void 0:u.name]);const b=(0,k.useCallback)((e=>{const t=Is.find((t=>t.value===e.target.value));t&&m(t)}),[m]),v=(0,k.useMemo)((()=>(0,l.isEmpty)(h)||(0,l.isEmpty)(t)?[]:Object.values(t).filter((e=>{var t;return!(null==e||null===(t=e.position)||void 0===t||!t.history)})).map((e=>{var t;return{label:e.keyword,data:e.position.history,selected:h.includes(e.keyword)&&!(0,l.isEmpty)(null===(t=e.position)||void 0===t?void 0:t.history)}}))),[h,t]);return(0,w.createElement)(vs,{isDisabled:!s},(0,w.createElement)("p",null,(0,d.__)("You can enable / disable tracking the SEO performance for each keyphrase below.","wordpress-seo")),s&&n&&(0,w.createElement)(Gt,null),(0,w.createElement)(xs,null,(0,w.createElement)(Ts,{selected:p,onSelect:b,options:c,isLoggedIn:s})),(0,w.createElement)(_s,null,(0,w.createElement)(hs,{isChartShown:y,datasets:v,keyphrases:r})),(0,w.createElement)(Ht,{startAt:null==p?void 0:p.value,selectedKeyphrases:h,onSelectKeyphrases:g,trackedKeyphrases:t}))};function Cs(e){const{isNewlyAuthenticated:t,isLoggedIn:s}=e,r=(0,k.useCallback)((()=>{bs(e)}),[bs,e]),n=dt(s);return(0,w.createElement)(ks,null,t&&(0,w.createElement)(yt,null),s&&(0,w.createElement)(gt,{trackingInfo:n}),(0,w.createElement)(Es,null,(0,d.__)("SEO performance","wordpress-seo"),(0,w.createElement)(R.HelpIcon,{linkTo:wpseoAdminL10n["shortlinks.wincher.seo_performance"]
/* translators: Hidden accessibility text. */,linkText:(0,d.__)("Learn more about the SEO performance feature.","wordpress-seo")})),(0,w.createElement)(zt,null),(0,w.createElement)(ws,{isLoggedIn:s,onLogin:r}),(0,w.createElement)(ys,{...e,onLogin:r}),(0,w.createElement)(Ss,{...e,historyDaysLimit:(null==n?void 0:n.historyDays)||31}))}Ss.propTypes={trackedKeyphrases:_().object,keyphrases:_().array.isRequired,isLoggedIn:_().bool.isRequired,shouldTrackAll:_().bool.isRequired,permalink:_().string.isRequired,historyDaysLimit:_().number},Cs.propTypes={trackedKeyphrases:_().object,addTrackedKeyphrase:_().func.isRequired,isLoggedIn:_().bool,isNewlyAuthenticated:_().bool,keyphrases:_().array,response:_().object,shouldTrackAll:_().bool,permalink:_().string,historyDaysLimit:_().number},Cs.defaultProps={trackedKeyphrases:null,isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],response:{},shouldTrackAll:!1,permalink:"",historyDaysLimit:0};const Ls=(0,pe.compose)([(0,me.withSelect)((e=>{const{isWincherNewlyAuthenticated:t,getWincherKeyphraseLimitReached:s,getWincherLimit:r,getWincherHistoryDaysLimit:n,getWincherLoginStatus:o,getWincherRequestIsSuccess:a,getWincherRequestResponse:i,getWincherTrackableKeyphrases:l,getWincherTrackedKeyphrases:c,getWincherAllKeyphrasesMissRanking:d,getWincherPermalink:u,shouldWincherAutomaticallyTrackAll:p}=e("yoast-seo/editor");return{keyphrases:l(),trackedKeyphrases:c(),allKeyphrasesMissRanking:d(),isLoggedIn:o(),isNewlyAuthenticated:t(),isSuccess:a(),keyphraseLimitReached:s(),limit:r(),response:i(),shouldTrackAll:p(),permalink:u(),historyDaysLimit:n()}})),(0,me.withDispatch)((e=>{const{setWincherWebsiteId:t,setWincherRequestSucceeded:s,setWincherRequestFailed:r,setWincherTrackingForKeyphrase:n,setWincherSetKeyphraseLimitReached:o,setWincherLoginStatus:a}=e("yoast-seo/editor");return{setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{r(e)},addTrackedKeyphrase:e=>{n(e)},setKeyphraseLimitReached:e=>{o(e)},onAuthentication:(e,s,r)=>{t(r),a(e,s)}}}))])(Cs);function Ps(e,t,s,r){return new Promise(((n,o)=>{jQuery.ajax({type:e,url:t,beforeSend:s?e=>{e.setRequestHeader("X-WP-Nonce",s)}:null,data:r,dataType:"json",success:n,error:o})}))}const As=window.wp.sanitize,Os="SNIPPET_EDITOR_UPDATE_REPLACEMENT_VARIABLE",Bs="SNIPPET_EDITOR_UPDATE_REPLACEMENT_VARIABLES_BATCH";function Ns(e,t,s="",r=!1){const n="string"==typeof t?(0,I.decodeHTML)(t):t;return{type:Os,name:e,value:n,label:s,hidden:r}}function qs(e){return e.charAt(0).toUpperCase()+e.slice(1)}const{stripHTMLTags:Ms}=I.strings,Fs=["slug","content","contentImage","snippetPreviewImageURL"];function Ks(e,t){(0,l.forEach)(e,((e,s)=>{Fs.includes(s)||t.dispatch(Ns(s,e))}))}function Ds(e){if(!["ct_","cf_","pt_"].includes(e.substring(0,3)))return e.replace(/_/g," ");const t=e.slice(0,3);switch(-1!==(e=e.slice(3)).indexOf("desc_")&&(e=e.slice(5)+" description"),t){case"ct_":e+=" (custom taxonomy)";break;case"cf_":e+=" (custom field)";break;case"pt_":e="Post type ("+(e=e.replace("single","singular"))+")"}return e}function $s(e){return qs(e=Ds(e))}function Ws(e,t){return e.push({name:t.name,label:t.label||$s(t.name),value:t.value}),e}function js(e,t="_"){return e.replace(/\s/g,t)}function Hs(e){return{name:"cf_"+js(e),label:qs(e+" (custom field)")}}function Us(e){const t=js(e);return{name:"ct_"+t,label:qs(e+" (custom taxonomy)"),descriptionName:"ct_desc_"+t,descriptionLabel:qs(e+" description (custom taxonomy)")}}function Ys(e,t){if(!e.custom_taxonomies)return e;const s={};return(0,l.forEach)(e.custom_taxonomies,((e,t)=>{const{name:r,label:n,descriptionName:o,descriptionLabel:a}=Us(t),i="string"==typeof e.name?(0,I.decodeHTML)(e.name):e.name,l="string"==typeof e.description?(0,I.decodeHTML)(e.description):e.description;s[r]={value:i,label:n},s[o]={value:l,label:a}})),t.dispatch(function(e){return{type:Bs,updatedVariables:e}}(s)),(0,l.omit)({...e},"custom_taxonomies")}function zs(e,t){return e.custom_fields?((0,l.forEach)(e.custom_fields,((e,s)=>{const{name:r,label:n}=Hs(s);t.dispatch(Ns(r,e,n))})),(0,l.omit)({...e},"custom_fields")):e}function Vs(e,t=156){return(e=(e=(0,As.stripTags)(e)).trim()).length<=t||(e=e.substring(0,t),/\s/.test(e)&&(e=e.substring(0,e.lastIndexOf(" ")))),e}const Gs=function(e){const t=(0,l.get)(window,["YoastSEO","app","pluggable"],!1);if(!t||!(0,l.get)(window,["YoastSEO","app","pluggable","loaded"],!1))return function(e){const t=(0,l.get)(window,["YoastSEO","wp","replaceVarsPlugin","replaceVariables"],l.identity);return{url:e.url,title:Ms(t(e.title)),description:Ms(t(e.description)),filteredSEOTitle:e.filteredSEOTitle?Ms(t(e.filteredSEOTitle)):""}}(e);const s=t._applyModifications.bind(t);return{url:e.url,title:Ms(s("data_page_title",e.title)),description:Ms(s("data_meta_desc",e.description)),filteredSEOTitle:e.filteredSEOTitle?Ms(s("data_page_title",e.filteredSEOTitle)):""}};var Qs="score-text",Xs="image yoast-logo svg",Zs=jQuery;function Js(e,t,s=null){var r,n,o,a,i;if(null!==s)return(0,l.get)(s,t,"");const c=(0,me.select)("yoast-seo/editor").getIsPremium(),u={na:(0,d.__)("Not available","wordpress-seo"),bad:(0,d.__)("Needs improvement","wordpress-seo"),ok:(0,d.__)("OK","wordpress-seo"),good:(0,d.__)("Good","wordpress-seo")},p={keyword:{label:c?(0,d.__)("Premium SEO analysis:","wordpress-seo"):(0,d.__)("SEO analysis:","wordpress-seo"),anchor:"yoast-seo-analysis-collapsible-metabox",status:u},content:{label:(0,d.__)("Readability analysis:","wordpress-seo"),anchor:"yoast-readability-analysis-collapsible-metabox",status:u},"inclusive-language":{label:(0,d.__)("Inclusive language:","wordpress-seo"),anchor:"yoast-inclusive-language-analysis-collapsible-metabox",status:{...u,ok:(0,d.__)("Potentially non-inclusive","wordpress-seo")}}};return null!=p&&null!==(r=p[e])&&void 0!==r&&null!==(n=r.status)&&void 0!==n&&n[t]?`<a href="#${null===(o=p[e])||void 0===o?void 0:o.anchor}">${null===(a=p[e])||void 0===a?void 0:a.label}</a> <strong>${null===(i=p[e])||void 0===i?void 0:i.status[t]}</strong>`:""}window.yoast=window.yoast||{},window.yoast.editorModules={analysis:{getL10nObject:c,getContentLocale:function(){const e=c();return(0,l.get)(e,"contentLocale","en_US")},getIndicatorForScore:function(e){return(0,l.isNil)(e)||(e/=10),function(e){switch(e){case"feedback":return{className:"na",screenReaderText:(0,d.__)("Not available","wordpress-seo"),screenReaderReadabilityText:(0,d.__)("Not available","wordpress-seo"),screenReaderInclusiveLanguageText:(0,d.__)("Not available","wordpress-seo")};case"bad":return{className:"bad",screenReaderText:(0,d.__)("Needs improvement","wordpress-seo"),screenReaderReadabilityText:(0,d.__)("Needs improvement","wordpress-seo"),screenReaderInclusiveLanguageText:(0,d.__)("Needs improvement","wordpress-seo")};case"ok":return{className:"ok",screenReaderText:(0,d.__)("OK SEO score","wordpress-seo"),screenReaderReadabilityText:(0,d.__)("OK","wordpress-seo"),screenReaderInclusiveLanguageText:(0,d.__)("Potentially non-inclusive","wordpress-seo")};case"good":return{className:"good",screenReaderText:(0,d.__)("Good SEO score","wordpress-seo"),screenReaderReadabilityText:(0,d.__)("Good","wordpress-seo"),screenReaderInclusiveLanguageText:(0,d.__)("Good","wordpress-seo")};default:return{className:"loading",screenReaderText:"",screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""}}}(u.interpreters.scoreToRating(e))},constants:e,refreshAnalysis:t},components:{HelpLink:z,TopLevelProviders:ue,higherorder:{withYoastSidebarPriority:e=>{const t=t=>{const{renderPriority:s,...r}=t;return(0,w.createElement)(e,{...r})};return t.propTypes={renderPriority:_().number},t}},contentAnalysis:{KeywordInput:W,mapResults:r},contexts:{location:{LocationContext:a.LocationContext,LocationProvider:a.LocationProvider,LocationConsumer:a.LocationConsumer}},SidebarItem:ce,SidebarCollapsible:ie,MetaboxCollapsible:e=>(0,w.createElement)(V,{hasPadding:!0,hasSeparator:!0,...e}),Modal:Z,portals:{Portal:se,ImageSelectPortal:re,ScoreIconPortal:oe}},containers:{EditorModal:be,PersistentDismissableAlert:we,Results:We,SEMrushRelatedKeyphrases:Ye,WincherSEOPerformance:Ls},helpers:{ajaxHelper:n,createInterpolateElement:Re,createWatcher:(e,t)=>{let s=e();return()=>{const r=e();(0,l.isEqual)(r,s)||(s=r,t((0,l.clone)(r)))}},isBlockEditor:function(){return window.wpseoScriptData&&"1"===window.wpseoScriptData.isBlockEditor},i18n:{setTextdomainL10n:function(e,t="wpseoYoastJSL10n"){const s=(0,l.get)(window,[t,e,"locale_data",e],!1);"yoast-components"===e&&(e="wordpress-seo"),!1===s?(0,d.setLocaleData)({"":{}},e):(0,d.setLocaleData)(s,e)}},replacementVariableHelpers:o,publishBox:{updateScore:function(e,t,s=null){var r=Zs("#"+e+"-score"),n=Xs+" "+t;r.children(".image").attr("class",n);var o=Js(e,t,s);r.children("."+Qs).html(o)},createScoresInPublishBox:function(e,t,s=null){const r=Zs("<div />",{class:"misc-pub-section yoast yoast-seo-score "+e+"-score",id:e+"-score"}),n=Zs("<span />",{class:Qs,html:Js(e,t,s)}),o=Zs("<span>").attr("class",Xs+" na");r.append(o).append(n),Zs("#yoast-seo-publishbox-section").append(r)},scrollToCollapsible:function(e){const t=Zs("#wpadminbar"),s=Zs(e);if(!t||!s)return;const r="fixed"===t.css("position")?t.height():0;Zs([document.documentElement,document.body]).animate({scrollTop:s.offset().top-r},1e3),s.trigger("focus"),0===s.parent().siblings().length&&s.trigger("click")}},updateAdminBar:function(e){jQuery("#wp-admin-bar-wpseo-menu .wpseo-score-icon").attr("title",e.screenReaderText).attr("class","wpseo-score-icon "+e.className).find(".wpseo-score-text").text(e.screenReaderText)},updateTrafficLight:function(e){var t=jQuery(".yst-traffic-light"),s=t.closest(".wpseo-meta-section-link"),r=jQuery("#wpseo-traffic-light-desc"),n=e.className||"na";t.attr("class","yst-traffic-light "+n),s.attr("aria-describedby","wpseo-traffic-light-desc"),r.length>0?r.text(e.screenReaderText):s.closest("li").append("<span id='wpseo-traffic-light-desc' class='screen-reader-text'>"+e.screenReaderText+"</span>")}}}})()})();