(()=>{"use strict";var e={n:t=>{var s=t&&t.__esModule?()=>t.default:()=>t;return e.d(s,{a:s}),s},d:(t,s)=>{for(var r in s)e.o(s,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:s[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{AnalysisList:()=>I,AnalysisResult:()=>x,ContentAnalysis:()=>A,SiteSEOReport:()=>T,renderRatingToColor:()=>w});const s=window.React;var r=e.n(s);const o=window.wp.i18n,n=window.yoast.styledComponents;var i=e.n(n);const a=window.yoast.propTypes;var l=e.n(a);const u=window.lodash.noop;var d=e.n(u);const m=window.yoast.styleGuide,p=window.lodash,g=window.yoast.componentsNew,c=window.yoast.helpers,{stripTagsFromHtmlString:h}=c.strings,B=["a","b","strong","em","i"],k=i().div`
	display: grid;
	grid-template-rows: 1fr;
	max-width: 32px;
	// This gap value is half the gap value between assessment result list items, which is 12px.
	gap: 6px;
`,b=i().li`
	// This is the height of the IconButtonToggle.
	min-height: 24px;
	margin-bottom: 12px;
	padding: 0;
	display: flex;
	align-items: flex-start;
	position: relative;
	gap: 12px;
`,C=i()(g.SvgIcon)`
	margin: 3px 0 0 0;
`,y=i().p`
	margin: 0;
	flex: 1 1 auto;
	color: ${e=>e.suppressedText?"rgba(30,30,30,0.5)":"inherit"};
`,f=({ariaLabel:e,id:t,className:r,status:o,onClick:n,isPressed:i})=>(0,s.createElement)(g.IconButtonToggle,{marksButtonStatus:o,className:r,onClick:n,id:t,icon:"eye",pressed:i,ariaLabel:e}),R=({markButtonFactory:e,...t})=>{const{ariaLabelMarks:r,ariaLabelEdit:o,bulletColor:n,buttonIdMarks:i,buttonIdEdit:a,editButtonClassName:l,hasAIFixes:u,hasBetaBadgeLabel:d,hasEditButton:m,hasMarksButton:p,id:c,isPremium:R,marker:x,marksButtonStatus:E,marksButtonClassName:w,onButtonClickMarks:I,onButtonClickEdit:v,onResultChange:N,pressed:_,renderHighlightingUpsell:A,renderAIOptimizeButton:H,shouldUpsellHighlighting:S,suppressedText:M,text:T}=t,[P,L]=(0,s.useState)(!1),O=(0,s.useCallback)((()=>L(!1)),[]),U=(0,s.useCallback)((()=>L(!0)),[]);e=e||f;let z=null;return function(e){return!e.hasMarksButton||"hidden"===e.marksButtonStatus}(t)||(z=e({onClick:S?U:I,status:E,className:w,id:i,isPressed:_,ariaLabel:r})),(0,s.useEffect)((()=>{N(c,x,p)}),[c,x,p]),(0,s.createElement)(b,null,(0,s.createElement)(C,{icon:"circle",color:n,size:"13px"}),(0,s.createElement)(y,{suppressedText:M},d&&(0,s.createElement)(g.BetaBadge,null),(0,s.createElement)("span",{dangerouslySetInnerHTML:{__html:h(T,B)}})),(0,s.createElement)(k,null,z,A(P,O),m&&R&&(0,s.createElement)(g.IconCTAEditButton,{className:l,onClick:v,id:a,icon:"edit",ariaLabel:o}),H(u,c)))};R.propTypes={text:l().string.isRequired,suppressedText:l().bool,bulletColor:l().string.isRequired,hasMarksButton:l().bool.isRequired,hasEditButton:l().bool,hasAIButton:l().bool,hasAIFixes:l().bool,buttonIdMarks:l().string.isRequired,buttonIdEdit:l().string,pressed:l().bool.isRequired,ariaLabelMarks:l().string.isRequired,ariaLabelEdit:l().string,onButtonClickMarks:l().func.isRequired,onButtonClickEdit:l().func,marksButtonStatus:l().string,marksButtonClassName:l().string,markButtonFactory:l().func,editButtonClassName:l().string,hasBetaBadgeLabel:l().bool,isPremium:l().bool,onResultChange:l().func,id:l().string,marker:l().oneOfType([l().func,l().array]),shouldUpsellHighlighting:l().bool,renderHighlightingUpsell:l().func,renderAIOptimizeButton:l().func},R.defaultProps={suppressedText:!1,marksButtonStatus:"enabled",marksButtonClassName:"",editButtonClassName:"",hasBetaBadgeLabel:!1,hasEditButton:!1,hasAIFixes:!1,buttonIdEdit:"",ariaLabelEdit:"",onButtonClickEdit:p.noop,isPremium:!1,onResultChange:p.noop,id:"",marker:p.noop,shouldUpsellHighlighting:!1,renderHighlightingUpsell:p.noop,renderAIOptimizeButton:p.noop};const x=R,E=i().ul`
	margin: 8px 0;
	padding: 0;
	list-style: none;
`;function w(e){switch(e){case"good":return m.colors.$color_good;case"OK":return m.colors.$color_ok;case"bad":return m.colors.$color_bad;default:return m.colors.$color_score_icon}}function I(e){return(0,s.createElement)(E,{role:"list"},e.results.map((t=>{const r=w(t.rating),n=t.markerId===e.marksButtonActivatedResult,i=t.id+"Mark",a=t.id+"Edit";let l="";l="disabled"===e.marksButtonStatus?(0,o.__)("Highlighting is currently disabled","wordpress-seo"):n?(0,o.__)("Remove highlight from the text","wordpress-seo"):(0,o.__)("Highlight this result in the text","wordpress-seo");const u=t.editFieldName,d=""===u?"":(0,o.sprintf)(
/* Translators: %1$s refers to the name of the field that should be edited (keyphrase, meta description,
       slug or SEO title). */
(0,o.__)("Edit your %1$s","wordpress-seo"),u);return(0,s.createElement)(x,{key:t.id,id:t.id,text:t.text,marker:t.marker,bulletColor:r,hasMarksButton:t.hasMarks,hasEditButton:t.hasJumps,hasAIFixes:t.hasAIFixes,ariaLabelMarks:l,ariaLabelEdit:d,pressed:n,suppressedText:"upsell"===t.rating,buttonIdMarks:i,buttonIdEdit:a,onButtonClickMarks:()=>e.onMarksButtonClick(t.id,t.marker),onButtonClickEdit:()=>e.onEditButtonClick(t.id),marksButtonClassName:e.marksButtonClassName,editButtonClassName:e.editButtonClassName,marksButtonStatus:e.marksButtonStatus,hasBetaBadgeLabel:t.hasBetaBadge,isPremium:e.isPremium,onResultChange:e.onResultChange,markButtonFactory:e.markButtonFactory,shouldUpsellHighlighting:e.shouldUpsellHighlighting,renderAIOptimizeButton:e.renderAIOptimizeButton,renderHighlightingUpsell:e.renderHighlightingUpsell})})))}I.propTypes={results:l().array.isRequired,marksButtonActivatedResult:l().string,marksButtonStatus:l().string,marksButtonClassName:l().string,editButtonClassName:l().string,markButtonFactory:l().func,onMarksButtonClick:l().func,onEditButtonClick:l().func,isPremium:l().bool,onResultChange:l().func,shouldUpsellHighlighting:l().bool,renderHighlightingUpsell:l().func,renderAIOptimizeButton:l().func},I.defaultProps={marksButtonActivatedResult:"",marksButtonStatus:"enabled",marksButtonClassName:"",editButtonClassName:"",onMarksButtonClick:d(),onEditButtonClick:d(),isPremium:!1,onResultChange:d(),shouldUpsellHighlighting:!1,renderHighlightingUpsell:d(),renderAIOptimizeButton:d()};const v=i().div`
	width: 100%;
	background-color: white;
	border-bottom: 1px solid transparent; // Avoid parent and child margin collapsing.
`,N=i()(g.Collapsible)`
	margin-bottom: 8px;

	${g.StyledIconsButton} {
		padding: 8px 0;
		color: ${m.colors.$color_blue};
		margin: -2px 8px 0 -2px; // Compensate icon size set to 18px.
	}
`;class _ extends r().Component{renderCollapsible(e,t,r){return(0,s.createElement)(N,{initialIsOpen:!0,title:`${e} (${r.length})`,prefixIcon:{icon:"angle-up",color:m.colors.$color_grey_dark,size:"18px"},prefixIconCollapsed:{icon:"angle-down",color:m.colors.$color_grey_dark,size:"18px"},suffixIcon:null,suffixIconCollapsed:null,headingProps:{level:t,fontSize:"13px",fontWeight:"500",color:"#1e1e1e"}},(0,s.createElement)(I,{results:r,marksButtonActivatedResult:this.props.activeMarker,marksButtonStatus:this.props.marksButtonStatus,marksButtonClassName:this.props.marksButtonClassName,editButtonClassName:this.props.editButtonClassName,markButtonFactory:this.props.markButtonFactory,onMarksButtonClick:this.props.onMarkButtonClick,onEditButtonClick:this.props.onEditButtonClick,renderAIOptimizeButton:this.props.renderAIOptimizeButton,isPremium:this.props.isPremium,onResultChange:this.props.onResultChange,shouldUpsellHighlighting:this.props.shouldUpsellHighlighting,renderHighlightingUpsell:this.props.renderHighlightingUpsell}))}render(){const{problemsResults:e,improvementsResults:t,goodResults:r,considerationsResults:n,errorsResults:i,upsellResults:a,headingLevel:l,resultCategoryLabels:u}=this.props,d=i.length,m=e.length,p=t.length,g=n.length,c=r.length,h=a.length,B={errors:(0,o.__)("Errors","wordpress-seo"),problems:(0,o.__)("Problems","wordpress-seo"),improvements:(0,o.__)("Improvements","wordpress-seo"),considerations:(0,o.__)("Considerations","wordpress-seo"),goodResults:(0,o.__)("Good results","wordpress-seo")},k=Object.assign(B,u);return(0,s.createElement)(v,null,d>0&&this.renderCollapsible(k.errors,l,i),m+h>0&&this.renderCollapsible(k.problems,l,[...a,...e]),p>0&&this.renderCollapsible(k.improvements,l,t),g>0&&this.renderCollapsible(k.considerations,l,n),c>0&&this.renderCollapsible(k.goodResults,l,r))}}_.propTypes={onMarkButtonClick:l().func,onEditButtonClick:l().func,problemsResults:l().array,improvementsResults:l().array,goodResults:l().array,considerationsResults:l().array,errorsResults:l().array,upsellResults:l().array,headingLevel:l().number,marksButtonStatus:l().string,marksButtonClassName:l().string,markButtonFactory:l().func,editButtonClassName:l().string,activeMarker:l().string,isPremium:l().bool,resultCategoryLabels:l().shape({errors:l().string,problems:l().string,improvements:l().string,considerations:l().string,goodResults:l().string}),onResultChange:l().func,shouldUpsellHighlighting:l().bool,renderHighlightingUpsell:l().func,renderAIOptimizeButton:l().func},_.defaultProps={onMarkButtonClick:()=>{},onEditButtonClick:()=>{},problemsResults:[],improvementsResults:[],goodResults:[],considerationsResults:[],errorsResults:[],upsellResults:[],headingLevel:4,marksButtonStatus:"enabled",marksButtonClassName:"",markButtonFactory:null,editButtonClassName:"",activeMarker:"",isPremium:!1,resultCategoryLabels:{},onResultChange:()=>{},shouldUpsellHighlighting:!1,renderHighlightingUpsell:()=>{},renderAIOptimizeButton:()=>{}};const A=_,H=i().div`
`,S=i().p`
	font-size: 14px;
`,M=e=>(0,s.createElement)(H,{className:e.className},(0,s.createElement)(S,{className:`${e.className}__text`},e.seoAssessmentText),(0,s.createElement)(g.StackedProgressBar,{className:"progress",items:e.seoAssessmentItems,barHeight:e.barHeight}),(0,s.createElement)(g.ScoreAssessments,{className:"assessments",items:e.seoAssessmentItems}));M.propTypes={className:l().string,seoAssessmentText:l().string,seoAssessmentItems:l().arrayOf(l().shape({html:l().string.isRequired,value:l().number.isRequired,color:l().string.isRequired})),barHeight:l().string},M.defaultProps={className:"seo-assessment",seoAssessmentText:"SEO Assessment",seoAssessmentItems:null,barHeight:"24px"};const T=M;(window.yoast=window.yoast||{}).analysisReport=t})();