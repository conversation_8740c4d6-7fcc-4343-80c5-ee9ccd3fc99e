<?php 
new dtc_admin_idp_table;


class dtc_admin_idp_table{
	
	public function __construct(){

		add_action( 'wp_ajax_dtc_idp_get_table', array('dtc_admin_idp_table','ajax_get_table'));
		add_action( 'wp_ajax_dtc_idp_edit_stats', array('dtc_admin_idp_table','ajax_edit_stats'));
		add_action( 'wp_ajax_dtc_idp_confirm_player', array('dtc_admin_idp_table','ajax_delete_player'));
		add_action( 'wp_ajax_dtc_idp_save_table', array('dtc_admin_idp_table','ajax_save_table'));
		add_action( 'wp_ajax_dtc_idp_add_players', array('dtc_admin_idp_table','ajax_add_players'));
		add_action( 'wp_ajax_dtc_idp_delete_player', array('dtc_admin_idp_table','ajax_delete_players'));
		add_action( 'wp_ajax_dtc_idp_get_player_photo', array('dtc_admin_idp_table','ajax_get_player_photo'));


	}
	
	function ajax_get_player_photo(){
		global $wpdb;
		
		$j = array();
		$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_players_idp where id = %d", $_POST['player_id']), ARRAY_A);
		
		$j['image']= dtc_get_player_image($r[0]['id'],$r[0]['list']);
		$j['name']= $r[0]['name'];
		
		echo json_encode($j);
		die();
	}
	
	function ajax_delete_players(){
		global $wpdb;
		
	if($_POST['player_id'] != ''){
	
	

	$wpdb->query($wpdb->prepare("DELETE FROM ".$wpdb->prefix . "dtc_players_idp WHERE id = %d",$_POST['player_id']));



	$this->update_averages();
	$this->update_ranks();
		}
			
	
	
	die();	
	}
	
	function ajax_add_players(){
		global $wpdb;
		
		if(count($_POST['player'])>0){
			
			foreach($_POST['player'] as $player){
				
				if($player['name'] != ''){
							
						$wpdb->insert("".$wpdb->prefix . "dtc_players_idp",$player);	
					
				}
				
			$this->update_averages();
			$this->update_ranks();
			}
			
	
		}
		
	die();	
	}
	public function update_averages(){
		global $wpdb;
	$r = $wpdb->get_results("SELECT * FROM  ".$wpdb->prefix . "dtc_players_idp order by adp asc", ARRAY_A);

		$dtc = array();
		$dtc = get_option('dtc_array');
		if($r){
			$dtc_count = 0;
			for ($i = 0; $i < count($r); $i++) {
						
						$update['adp_dtc'] = $dtc[$dtc_count];
						
						$update['average'] = dtc_get_average(array($r[$i]['yse'],$r[$i]['nyh'],$r[$i]['nrm']));
						$where['id'] = $r[$i]['id'];
						$wpdb->update("".$wpdb->prefix . "dtc_players_idp",$update,$where);
						$dtc_count++;	
			}
		}
		
		
		unset($r);
		unset($where);
		unset($update);
		
		$r = $wpdb->get_results("SELECT * FROM  ".$wpdb->prefix . "dtc_players_idp order by average desc", ARRAY_A);

	
		if($r){
			$dtc_count = 0;
			for ($i = 0; $i < count($r); $i++) {
						
						$update['adp_dtc'] = $dtc[$dtc_count];
						
						$update['average'] = dtc_get_average(array($r[$i]['yse'],$r[$i]['nyh'],$r[$i]['nrm']));
						$where['id'] = $r[$i]['id'];
						$wpdb->update("".$wpdb->prefix . "dtc_players_idp",$update,$where);
						$dtc_count++;	
			}
		}
		
		
		
		
		
		
	}	
		public function update_player_rank($player_id,$rank,$old_rank,$r,$sub= false){
			global $wpdb;
			if($sub == true){
			$update['sub_rank'] = $rank;
				
				if($old_rank == 0){
				$update['sub_rank_trend'] = 0;	
				}else{
					if($old_rank <$rank){	
					$trend = $rank - $old_rank;
					$trend = -1 * abs($trend);										
					if($trend != 0){	
					$update['sub_rank_trend'] = $trend;		
					}
					}elseif($old_rank >$rank){
					if($trend != 0){	
					$trend = $old_rank - $rank;;	
					}
					$update['sub_rank_trend'] = $trend;		
					}elseif($old_rank == $rank){
					#$update['sub_rank_trend'] = 0;	
					}
				
				}
			
			}else{
			$update['rank'] = $rank;
			
				if($old_rank == 0){
				$update['rank_trend'] = 0;	
				}else{
					if($old_rank <$rank){	
					$trend = $rank - $old_rank;										
					$trend = -1 * abs($trend);
					if($trend != 0){
					$update['rank_trend'] = $trend;	
					}
					}elseif($old_rank >$rank){
					$trend = $old_rank - $rank;;	
					if($trend != 0){	
					$update['rank_trend'] = $trend;		
					}
					}elseif($old_rank == $rank){
					#$update['rank_trend'] = 0;	
					}
				
				}
				
			}
			$where['id'] = $player_id;
			$wpdb->update("".$wpdb->prefix . "dtc_players_idp", $update,$where);
		
		
			
			
		$date = gmdate("Y-m-01");
		$thismonth = date("n") ;
		$newdate = date("n",strtotime ( '+1 month' , strtotime ( $date ) )) ;	
		$query = $wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_players_history where pid = %d and year = %d and type = 'idp' order by id desc limit 1", $player_id,date("Y"));
		
		$stats_exist = $wpdb->get_row($query, ARRAY_A);
		
		if(empty($stats_exist)){
				$update_history['type'] = 'idp';	
				$update_history['pid'] = $player_id;
			    $update_history['year'] = date("Y");	
				$update_history[$thismonth ] = $rank;	
				$update_history['v'.$thismonth ] = $r['average'];
				$wpdb->insert("".$wpdb->prefix . "dtc_players_history", $update_history);
		}else{
			#print_r($stats_exist);
			#update historicals
			if(gmdate('t') == gmdate('d') || empty($stats_exist[$thismonth ]) || empty($stats_exist['v'.$thismonth ])){
   		
		
				if( empty($stats_exist[$thismonth ]) ){
				$update_history[$thismonth ] = $rank;	
				$update_history['v'.$thismonth ] = $r['average'];
				}else{
				$update_history[$newdate] = $rank;
				$update_history['v'.$newdate] = $r['average'];
				}
				$where_history['id'] = $stats_exist['id'];
				#print_r($update_history);
				$wpdb->update("".$wpdb->prefix . "dtc_players_history", $update_history,$where_history);
				
					
			}	
			
		}
			
				
		}
		



	public function update_ranks(){
		global $wpdb;

		$r = $wpdb->get_results("SELECT * FROM  ".$wpdb->prefix . "dtc_players_idp order by average desc", ARRAY_A);
	
		if($r){
			$dtc_count = 0;
			$rank = 1;
			for ($i = 0; $i < count($r); $i++) {

				$this->update_player_rank($r[$i]['id'],$rank,$r[$i]['rank'],$r[$i],false);
				$rank++;
			}
		}
		unset($rank);
		unset($r);
		
		
		$positions = array("LB","DB","S","LB");
		
		foreach($positions as $position){
			
			$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_players_idp where position = %s order by average desc", $position), ARRAY_A);	
			if($r){
			$dtc_count = 0;
			$rank = 1;
			for ($i = 0; $i < count($r); $i++) {

				$this->update_player_rank($r[$i]['id'],$rank,$r[$i]['sub_rank'],$r[$i],true);
				$rank++;
			}
		}
				unset($rank);
				unset($r);
		}
		
		
	}
		
		
	
	function ajax_save_table(){
		global $wpdb;	
	 ini_set('memory_limit', '1024M');

	if(count($_POST['update_stats'])>0){
			foreach($_POST['update_stats'] as $player_id=>$stats){
				
					$update['yse'] = $stats['yse'];
					$update['nyh'] = $stats['nyh'];
					$update['nrm'] = $stats['nrm'];
					$update['tier'] = $stats['tier'];
					$update['qbtier'] = $stats['qbtier'];
					$update['birthdate'] = $stats['birthdate'];
					$update['name'] = $stats['name'];
					$update['team'] = $stats['team'];
					$update['position'] = $stats['position'];
					$update['nonppr'] =  ($stats['nonppr'] == '1' ? '1' : '0');
				$update['rbppc'] =  ($stats['rbppc'] == '1' ? '1' : '0');
					
					if(is_array($stats['badges'])){
					$update['badge_id'] = serialize($stats['badges']);
					}
					$where['id'] = $player_id;
					
					
					
				
					
					$wpdb->update("".$wpdb->prefix . "dtc_players_idp",$update,$where);
					
					
    if($wpdb->last_error !== '') :

        $str   = htmlspecialchars( $wpdb->last_result, ENT_QUOTES );
        $query = htmlspecialchars( $wpdb->last_query, ENT_QUOTES );

      echo "<div id='error'>
        <p class='wpdberror'><strong>WordPress database error:</strong> [$str]<br />
        <code>$query</code></p>
        </div>";

    endif;
					
					unset($update);unset($where);
			}
	}
	$this->update_averages();
	$this->update_ranks();
	die();
		
	}
	function ajax_check_lock(){
	global $wpdb;	

	if($_POST['id'] != ''){
		echo $wpdb->prepare("DELETE FROM ".$wpdb->prefix . "dtc_players_idp WHERE id =%d",$_POST['id']);
	$wpdb->query($wpdb->prepare("DELETE FROM ".$wpdb->prefix . "dtc_players_idp WHERE id =%d",$_POST['id']));
	$this->update_averages();
	
	}
	die();	
	}
	function ajax_delete_player(){
	global $wpdb;	

	if($_POST['id'] != ''){
		echo $wpdb->prepare("DELETE FROM ".$wpdb->prefix . "dtc_players_idp WHERE id =%d",$_POST['id']);
	$wpdb->query($wpdb->prepare("DELETE FROM ".$wpdb->prefix . "dtc_players_idp WHERE id =%d",$_POST['id']));
	$this->update_averages();
	
	}
	die();	
	}
	function ajax_edit_stats(){
	global $wpdb;	
	
	
	$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_players_idp where id = %d",$_POST['id']), ARRAY_A);		
	
	
	$update['yse'] = $_POST['yse'];
	$update['nyh'] = $_POST['nyh'];
	$update['nrm'] = $_POST['nrm'];
	$update['average'] = dtc_get_average(array($update['yse'],$update['nyh'],$update['nrm']));
	$where['id'] = $_POST['id'];
	$wpdb->update("".$wpdb->prefix . "dtc_players_idp",$update,$where);
	die();	
	}
	function ajax_get_table(){
		 $this->get_table();
		 die();
	}
	
	function get_tier_dropdown($tier_id,$id){
		
		$tiers = array(1,2,3);
		$tier = '<select name="update_stats['.$id.'][tier]"><option value="0">Tier</option>';
		
				foreach($tiers as $tierd){
					
					if($tier_id == $tierd){
					$selected = 'selected="selected"';	
					}else{
					$selected = '';	
					}
				$tier.= '<option value="'.$tierd.'" '.$selected.'>Tier '.$tierd.'</option>';	
					
				}
		$tier .='</select>';
	return $tier;	
	}
	function get_qb_tier_dropdown($tier_id,$id){
		
		$tiers = array(1,2,3,4,5);
		$tier = '<select name="update_stats['.$id.'][qbtier]"><option value="0">QB Tier</option>';
		
				foreach($tiers as $tierd){
					
					if($tier_id == $tierd){
					$selected = 'selected="selected"';	
					}else{
					$selected = '';	
					}
				$tier.= '<option value="'.$tierd.'" '.$selected.'>QB Tier '.$tierd.'</option>';	
					
				}
		$tier .='</select>';
	return $tier;	
	}
	
	public function get_table() {
		global $wpdb;

		$where = '';
		if ($_POST['action'] == 'dtc_idp_get_table') {
			$sort = 'ORDER by ' . $_POST['dtc_sort'] . ' ' . $_POST['dtc_sort_type'] . '';

			if ($_POST['dtc_filter'] != '') {
				if ($_POST['dtc_filter'] != 'ALL') {
					$where = ' AND position = "' . $_POST['dtc_filter'] . '"';
				}
			}

			if ($_POST['search'] != '') {
				$where .= ' AND name like  "%' . $_POST['search'] . '%"';
			}

			if ($_POST['dtc_sort_type'] == 'desc') {
				$sort_type = 'asc';
			} else {
				$sort_type = 'desc';
			}

			if ($_POST['dtc_sort'] == '') {
				$sort = 'ORDER by average desc';
			}
		} else {
			$sort = 'ORDER by average desc';
		}

		if ($_POST['dtc_filter'] != '') {

			$selected_filter = $_POST['dtc_filter'];
		} else {
			$selected_filter = 'ALL';
		}

		if ($_POST['dtc_sort'] != '') {
			$selected_sort = $_POST['dtc_sort'];
		} else {
			$selected_sort = 'average';
		}

		if ($_POST['dtc_sort_type'] != '') {
			$selected_sort_type = $_POST['dtc_sort_type'];
		} else {
			$selected_sort_type = 'desc';
		}

		$query = "SELECT * FROM  " . $wpdb->prefix . "dtc_players_idp
					WHERE id !=0 
					" . $where . "
					" . $sort . "
					limit 1000";
		
		$players = $wpdb->get_results($query, ARRAY_A);

		echo '
			<div class="dtc-filter-menu">
				<p><strong>Filter</strong></p>
				<a href="#" class="dtc-idp-filter-position" data-id="ALL" data-sort="' . $selected_sort . '" data-sort-type="' . $selected_sort_type . '">Show All</a>
				<a href="#" class="dtc-idp-filter-position" data-id="LB" data-sort="' . $selected_sort . '" data-sort-type="' . $selected_sort_type . '">LB</a>
				<a href="#" class="dtc-idp-filter-position" data-id="DE" data-sort="' . $selected_sort . '" data-sort-type="' . $selected_sort_type . '">DE</a>
				<a href="#" class="dtc-idp-filter-position" data-id="S" data-sort="' . $selected_sort . '" data-sort-type="' . $selected_sort_type . '">S</a>
				<a href="#" class="dtc-idp-filter-position" data-id="DT" data-sort="' . $selected_sort . '" data-sort-type="' . $selected_sort_type . '">DT</a>		
				<a href="#" class="dtc-idp-filter-position" data-id="CB" data-sort="' . $selected_sort . '" data-sort-type="' . $selected_sort_type . '">CB</a>		
				
				<div style="clear:both"></div>
			</div>
		';

		echo '
				<div class="mobile-table-sort">
					<p><strong>Sort</strong></p>
					
					<a href="#" class="dtc-idp-sort-item" data-id="average" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">Rank</a>
					<a href="#" class="dtc-idp-sort-item" data-id="name" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">Name</a>
					<a href="#" class="dtc-idp-sort-item" data-id="position" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">Position</a>
					<a href="#" class="dtc-idp-sort-item" data-id="adp_dtc" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">ADP</a>
					<a href="#" class="dtc-idp-sort-item" data-id="yse" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">YSE</a>
					<a href="#" class="dtc-idp-sort-item" data-id="nyh" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">NYH</a>
					<a href="#" class="dtc-idp-sort-item" data-id="nrm" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">NRM</a>
					<a href="#" class="dtc-idp-sort-item" data-id="average" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">AVG</a>
					<div style="clear:both"></div>
				</div>
		';

		if ( $_POST['show_edit'] == 1 ) {
			$this->get_historical_data_table( $players, $_REQUEST );
		} else {
			$this->get_regular_table( $players, $sort_type, $selected_filter );
		}

		?>

		<script type="text/javascript">
			jQuery(document).ready(function($) {
				/*
				$('#dtc-main-table').Tabledit({
					url:ajaxurl,
					restoreButton: false,
					editButton: false,
				
					hideIdentifier: true,
					onSuccess: function(data, textStatus, jqXHR) {
					
					$.post(ajaxurl, {'action':'dtc_get_table'}, function(response) {
							$(".dtc-player-table").html(response);
						});
					
					},
				
				buttons: {
						edit: {
							class: 'btn btn-sm btn-default',
							html: '<span class="dashicons dashicons-welcome-write-blog dtc-check-lock"></span>',
							action: 'dtc_edit_stats'
						},
						delete: {
							class: 'btn btn-sm btn-default',
							html: '<span class="dashicons dashicons-trash"></span>',
							action: 'dtc_confirm_player'
						},
						confirm: {
							class: 'btn btn-sm btn-default',
							html: 'Confirm Delete',
							action: 'dtc_confirm_player'
						},
						save: {
							class: 'button',
							html: 'Save'
						},
					
					},
					columns: {
						identifier: [0, 'id'],
						editable: [ [5, 'yse'], [6, 'nyh'], [7, 'nrm']]
					}
				});
				*/
			});
		</script>

		<?php
	}

	function get_regular_table( $players, $sort_type, $selected_filter, $badges = [] ) {
		echo ' 
			<form class="dtc-data-form">
				<input type="hidden" name="action" value="dtc_idp_save_table">
				<table class="wp-list-table widefat fixed posts" cellspacing="0" id="dtc-main-table" style="table-layout:auto">
					<thead>
						<tr class="dtc-table-header" cope="col">
							<th style="display:none" cope="col">ID</th>
						
							<th cope="col" style="width:auto;"><a href="#" class="dtc-idp-sort-item" data-id="average" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">Rank</a></th>
						
							<th cope="col" style="width:auto;"><a href="#" class="dtc-idp-sort-item" data-id="name" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">Name</a></th>
							
							<th cope="col" style="width:auto;"><a href="#" class="dtc-idp-sort-item" data-id="position" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">Position</a></th>
							<th cope="col" style="width:auto;"><a href="#" class="dtc-idp-sort-item" data-id="team" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">Team</a></th>
							<th cope="col" style="width:auto;">Birthdate</th>
							
							<th cope="col" style="width:auto;"><a href="#" class="dtc-idp-sort-item" data-id="yse" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">YSE</a></th>
							<th cope="col" style="width:auto;"><a href="#" class="dtc-idp-sort-item" data-id="nyh" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">NYH</a></th>
							<th cope="col" style="width:auto;"><a href="#" class="dtc-idp-sort-item" data-id="nrm" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">NRM</a></th>
							<th cope="col" style="width:auto;"><a href="#" class="dtc-idp-sort-item" data-id="average" data-sort="' . $sort_type . '" data-filter="' . $selected_filter . '">AVG</a></th>
							<th style="width:auto;"></th>
						</tr>
					</thead>
		';

		$offset = 0;
		if ($players) {
			$rank = 1;
			for ($i = 0; $i < count($players); $i++) {
				$checked =  ($players[$i]['nonppr'] == '1' ? 'checked="checked"' : '');
				$rbchecked =  ($players[$i]['rbppc'] == '1' ? 'checked="checked"' : '');

				if ($players[$i]['birthdate'] == '0000-00-00') {
					$date = '';
				} else {
					$date = $players[$i]['birthdate'];
				}

				$player_rank = $offset + $rank;

				echo '<tr class="row-' . $players[$i]['position'] . '">
						<td style="display:none">' . $players[$i]['id'] . '<input type="hidden" name="update_stats[' . $players[$i]['id'] . '][name]" value="' . $players[$i]['name'] . '"></td>
						<td  data-label="Rank">' . $player_rank . /* $players[$i]['rank'] . */ ' ' . dtc_show_rank($players[$i]['rank_trend']) . '</td>
					
						
						<td  data-label="Name" style=";text-align:left;min-width:200px" ><input style="max-width:none;width:100%;margin-top:1px" type="text" name="update_stats[' . $players[$i]['id'] . '][name]" value="' . stripslashes($players[$i]['name']) . '"> <a href="#" data-id="' . $players[$i]['id'] . '" class="update-player-photo button" data-type="idp" >Edit Photo</a>
						' . dtc_admin_table::get_badge_dropdown($badges, $players[$i]['id']) . '

						
						</td>
				';

				echo '
						<td  data-label="Position" style="max-width:50px"><span class="position-' . $players[$i]['position'] . '"><input  style="width:50px"    type="text" name="update_stats[' . $players[$i]['id'] . '][position]" value="' . $players[$i]['position'] . '"></span></td>
						<td  data-label="Team"  style="max-width:50px" ><span class="position-' . $players[$i]['team'] . '"><input  style="width:50px" type="text" name="update_stats[' . $players[$i]['id'] . '][team]" value="' . $players[$i]['team'] . '"></span></td>
						<td  data-label="Birthdate"><input style="max-width:100px;width:100px;" class="dtc_datepicker" type="text" name="update_stats[' . $players[$i]['id'] . '][birthdate]" value="' . $date . '"></td>
						
						<td  data-label="YSE" style="min-width:50px;"><input type="text" name="update_stats[' . $players[$i]['id'] . '][yse]" value="' . $players[$i]['yse'] . '"></td>
						<td  data-label="NYH" style="min-width:50px;"><input type="text" name="update_stats[' . $players[$i]['id'] . '][nyh]" value="' . $players[$i]['nyh'] . '"></td>
						<td  data-label="NRM" style="min-width:50px;"><input type="text" name="update_stats[' . $players[$i]['id'] . '][nrm]" value="' . $players[$i]['nrm'] . '"></td>
						<td  data-label="AVG" style="min-width:50px;">' . $players[$i]['average'] . '</td>
						<td><a href="#" class="dtc-idp-delete-player" data-id="' . $players[$i]['id'] . '" class="button"><span class="dashicons dashicons-dismiss"></span></a></td>
					</tr>
				';
				$rank += 1;
			}
		}
		
		echo '
				</table>
			</form>
		';
	}

	function get_historical_data_table( $players, $request_data ) {
		$request_data['show_year'] = '2022';
		$months = array(
			1 => 'January',
			2 => 'Feburary',
			3 => 'March',
			4 => 'April',
			5 => 'May',
			6 => 'June',
			7 => 'July',
			8 => 'August',
			9 => 'September',
			10 => 'October',
			11 => 'November',
			12 => 'December'
		);

		echo ' 
	  		<form class="dtc-data-form">
		  		<input type="hidden" name="action" value="dtc_save_history">
				<input type="hidden" name="year" value="' . $request_data['show_year'] . '" id="show_year">
				
				<table class="wp-list-table widefat fixed posts cell-border compact stripe" cellspacing="0" id="dtc-main-table">
			  		<thead>
						<tr class="dtc-table-header" cope="col">
						<th style="display:none" cope="col" >ID</th>
					
					
						<th cope="col" style="width:210px" >Name</th>
						<th cope="col" style="width:50px" >Type</th>
		';

		foreach ($months as $k => $month) {
			echo '<th cope="col" >' . $month . '</th>';
		}

		echo '
						</tr>
					</thead>	
					';

		if ($players) {
			$rank = 1;
			for ($i = 0; $i < count($players); $i++) {

				unset($player);
				$player_stats = dtc_get_player_stats($players[$i]['id'], $request_data['show_year'], $players[$i]['type']);

				echo '
					<tr class="row-' . $players[$i]['position'] . '">
				 	   <td style="display:none">' . $players[$i]['id'] . '<input type="hidden" name="update_history[' . $players[$i]['id'] . '][name]" value="' . $players[$i]['name'] . '"></td>
						<td  data-label="Name" style="width:210px"  ><strong>' . stripslashes($players[$i]['name']) . '</strong></td><td>Rank</td>
						';

				foreach ($months as $k => $month) {
					if (!isset($player_stats[$k])) {
						$player_stats[$k] = 0;
					}

					echo '<td  data-label="' . $month . '"  ><input type="text" style="width:100%;max-width:none" name="update_history[' . $players[$i]['id'] . '][' . $k . ']" value="' . $player_stats[$k] . '"><span style="display:none">' . $player_stats[$k] . '</span>
						</td>
					';
				}

				echo '
					</tr>
				';



				echo '
					<tr class="row-' . $players[$i]['position'] . '">
				 		<td style="display:none">' . $players[$i]['id'] . '<input type="hidden" name="update_history[' . $players[$i]['id'] . '][name]" value="' . $players[$i]['name'] . '"></td>

						<td  data-label="Name" style="width:210px" > ' . $players[$i]['position'] . ' - ' . $players[$i]['team'] . '</td><td>Value</td>
				';

				foreach ($months as $k => $month) {
					if (!isset($player_stats[$k])) {
						$player_stats[$k] = 0;
					}

					echo '<td  data-label="' . $month . '"  ><input type="text" style="width:100%;max-width:none" name="update_history[' . $players[$i]['id'] . '][v' . $k . ']" value="' . $player_stats['v' . $k] . '"><span style="display:none">' . $player_stats['v' . $k] . '</span></td>';
				}

				echo '
					</tr>
				';
			}
		}

		echo '	</table>
			</form>
		';
	}

	public function import_players(){




		if($_POST['dtc-import-players'] != ''){
		global $wpdb;
		$table = $_POST['table'];
		require_once(''.DTC_DIR.'classes/PHPExcel.php');
		$worksheets = array();
		$filename = $_FILES['players']['tmp_name'];
		$type = PHPExcel_IOFactory::identify($filename);
		$objReader = PHPExcel_IOFactory::createReader($type);
		$objPHPExcel = $objReader->load($filename);
		$dtc = get_option('dtc_array');
		foreach ($objPHPExcel->getWorksheetIterator() as $worksheet) {
			$worksheets[$worksheet->getTitle()] = $worksheet->toArray();
		}
		
	
			if(count($worksheets)>0){
				
				$inserted= 0;
				$updated = 0;
				foreach($worksheets as $r){
							
							if(count($r)>0){
								$dtc_count = 0;
								 for ($i = 0; $i < count($r); $i++) {
									
										$headers = $r[0];
										
										foreach($headers as $key=>$header){
										$insert[$header] = 	$r[$i][$key];
										}
										
										if($insert['name'] != 'name' && $insert['dtc'] != 'dtc'){
											
											if($table == "".$wpdb->prefix . "dtc_players_idp"){
											$insert['adp_dtc'] = $dtc[$dtc_count];	
											}
											$dtc_count++;
										
											if(dtc_player_exists($insert['name'],$insert['position'],$table) == false){
											$wpdb->insert($table, $insert);
											$inserted++;
											}else{
											$where['name'] = $insert['name'];
											$where['position'] = $insert['position'];
											$wpdb->update($table, $insert,$where);	
											$updated++;
											}
											unset($where);
											unset($insert);
										}
								
								 }
							}
					
				}
				
			}
			$this->update_averages();
			$this->update_ranks();
		
			echo '   <div class="notice notice-success is-dismissible">
        <p>'.__( 'Inserted '.$inserted.' Players and Updated '.$updated.' Players', 'dtc' ).'</p>
    </div>';
		}
		
		
		if($_POST['dtc-import-dtc'] != ''){
			
			
		$dtc = $_POST['dtc_data'];
		$dtc_array = array();
		if($dtc){
		
		$dtc_array = explode(PHP_EOL, $dtc );
		
		}
		update_option('dtc_array',array_filter($dtc_array));
		}
		
		
	}
	
	public function view(){
		global $wpdb;
		$this->import_players();

		add_thickbox();
	 
		if($_POST['dtc-save-image'] != ''){	
			if($_FILES["upload-photo"]["name"] != ''){
			    $upload_path = wp_upload_dir();
				move_uploaded_file($_FILES["upload-photo"]["tmp_name"], $upload_path['basedir'].'/headshots_new/players/'.$_POST['update-player-photo-field-id'].'.png');

				echo '<div class="dtc-success">Updated Photo for '.$_POST['update-player-photo-field-id'].'</div>';
			}	
		}

		$dtc = array();
		$dtc = get_option('dtc_array');
		$dtc_data = implode("\n",$dtc);
		
		echo '	
		<style type="text/css">
		
		</style>
		<div id="dtc-import-players" style="display:none;">
			<h2>Import Players</h2>
			<form action="" method="post" enctype="multipart/form-data">
				<input type="hidden" name="table" value="'.$wpdb->prefix . 'dtc_players">
				Upload Players Excel Document <input type="file" name="players"> <input name="dtc-import-players" type="submit" value="Import">
			</form>
		</div>

		<div id="dtc-import-adp" style="display:none;">
			<h2>Import ADP</h2>
			<form action="" method="post" enctype="multipart/form-data">
				<input type="hidden" name="table" value="'.$wpdb->prefix . 'dtc_players">
				Upload Players Excel Document <input type="file" name="players"> <input name="dtc-import-players" type="submit" value="Import">
			</form>
		</div>

		<div id="dtc-edit-dtc" style="display:none;">
			<h2>Modify DTC</h2>
			<form action="" method="post" enctype="multipart/form-data">
				<textarea name="dtc_data" value="" style="width:100%;height:500px" class="dtc-editor">'.$dtc_data.'</textarea><br>
				<input name="dtc-import-dtc" type="submit" value="Save">
			</form>
		</div>

		<div id="dtc-add-players" style="display:none;">
			<h2>Add Players</h2>
			<form action="" method="post" enctype="multipart/form-data" class="dtc-idp-add-players-form">
				<input type="hidden" name="action" value="dtc_idp_add_players">
				
				<table class="wp-list-table widefat fixed posts" cellspacing="0" id="dtc-main-table">
					<thead>
						<tr>
							<th>Name</th>
							<th>Position</th>
							<th>Team</th>
							<th>Birth Date</th>	  
							<th>ADP</th>
							<th>YSE</th>
							<th>NYH</th>
							<th>NRM</th>
						</tr>
					</thead>
					<tbody>
		';

		for ($i = 1; $i <= 10; $i++) {
				echo '
						<tr>
							<th>
								<input type="text" name="player['.$i.'][name]" value="">
							</th>
							
							<th>
								<input type="text" name="player['.$i.'][position]" value="">
							</th>
							
							<th>
								<input type="text" name="player['.$i.'][team]" value="">
							</th>
							
							<th>
								<input type="text" name="player['.$i.'][birthdate]" value="" class="dtc_datepicker">
							</th>
							
							<th>
								<input type="text" name="player['.$i.'][adp]" value="">
							</th>
							
							<th>
								<input type="text" name="player['.$i.'][yse]" value="">
							</th>
							
							<th>
								<input type="text" name="player['.$i.'][nyh]" value="">
							</th>
							
							<th>
								<input type="text" name="player['.$i.'][nrm]" value="">
							</th>
						</tr> 
					';	
		}
		
		echo'
					</tbody>
				</table>
				
				<input name="dtc-import-dtc" type="submit" value="Save">
			</form>
		</div>

		<div id="update-player-photo-modal" style="display:none;">
			<div class="update-player-photo-modal-inner">
				<form class="update-player-photo-form" enctype="multipart/form-data" method="post" action="">
					<input type="hidden" class="update-player-photo-field-id" name="update-player-photo-field-id" value="">
					
					<div class="update-player-photo-form-image">
					</div>
					
					<input type="hidden" name="update-player-photo-field"  class="update-player-photo-field">
					<p><input type="file" name="upload-photo"> <input type="submit" name="dtc-save-image" value="Upload Photo"></p>
				</form>

				<form class="update-player-photo-xy">
					<input type="hidden" name="action" value="dtc_update_player_axis">
					<input type="hidden" name="type" class="update-player-photo-field-type" >
					<input type="hidden" class="update-player-photo-field-id" name="player_id" value="">
					<p>Modify the position below</p>
					<strong>X:</strong> <input type="text" name="image_x" value="" class="update-player-photo-x" style="width:60px"> 
					<strong>Y:</strong><input type="text" name="image_y" value="" class="update-player-photo-y" style="width:60px"> 
					<strong>Scale:</strong> <input type="text" name="image_scale" value="" class="update-player-photo-scale" style="width:60px">% 
					<strong>Rotation:</strong><input type="text" name="image_rotation" value="" class="update-player-photo-rotation" style="width:60px">Deg
					<input type="submit" name="save_image_axis" value="Save Axis">
				</form>
			</div>
		</div>
		
		<div class="dtc-admin-wrapper">
		';
		
		dtc_admin_header('Calculator Values');
		
		echo '
			<div class="dtc-top-menu dtc-idp-table">
				<a href="#" class="search-player button" style="float:right">Search</a>
				<input class="dtc-admin-search" type="text" name="search" value="" style="float:right"> 
				<div style="clear:both"></div>
					
				<a href="#" class="dtc-idp-save-table" style="background-color:#f2a535">Save</a>
				<a  href="#TB_inline?width=600&height=650&inlineId=dtc-add-players" class="thickbox">Add Players</a> 
				<a  href="'. admin_url( 'admin-ajax.php' ).'?action=dtc_export_table&type=idp" >Export Players</a> 
				<div style="clear:both"></div>
			</div>
		';
		
		echo '
			<div style="padding:5px;background-color;#EFEFEF">
				<input type="checkbox" class="show-historical-data dtc-idp-table-input" value="1" > 
				Edit historical data 
				<select class="show-historical-data-year" style="display:none">
					<option value="2022">2022</option>
					<option value="2021">2021</option>
					<option value="2020">2020</option>
					<option value="2019">2019</option>
					<option value="2018">2018</option>
					<option value="2017">2017</option>
				</select> 
			
				<a href="#" data-table="#dtc-main-table" class="dtc-export-excel" style="text-decoration:none"><span class="dashicons dashicons-media-spreadsheet"></span></a>
			</div>
		';

		echo '
			<div class="dtc-player-table">
		';
			$this->get_table();
		echo '
			</div>
		';
		
		echo '
		</div>
		';
	}
}