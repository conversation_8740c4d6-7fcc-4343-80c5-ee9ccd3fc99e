<?php 
new dtc_admin_table;

class dtc_admin_table{
	
	public function __construct(){
		add_action( 'wp_ajax_dtc_get_table', array('dtc_admin_table','ajax_get_table'));
		add_action( 'wp_ajax_dtc_edit_stats', array('dtc_admin_table','ajax_edit_stats'));
		add_action( 'wp_ajax_dtc_confirm_player', array('dtc_admin_table','ajax_delete_player'));
		add_action( 'wp_ajax_dtc_save_table', array('dtc_admin_table','ajax_save_table'));

		add_action( 'wp_ajax_dtc_recalculate_ranks', array('dtc_admin_table','ajax_update_ranks'));

		add_action( 'wp_ajax_dtc_save_history', array('dtc_admin_table','ajax_save_history'));
		add_action( 'wp_ajax_dtc_add_players', array('dtc_admin_table','ajax_add_players'));
		add_action( 'wp_ajax_dtc_delete_player', array('dtc_admin_table','ajax_delete_players'));
		add_action( 'wp_ajax_dtc_get_player_photo', array('dtc_admin_table','ajax_get_player_photo'));

    	add_action( 'wp_ajax_dtc_update_player_axis', array('dtc_admin_table','ajax_save_player_photo_xy'));

		add_action( 'wp_ajax_dtc_export_table', array('dtc_admin_table','export_table'));

		add_action('cron_tools_button', array($this,'ranks_button'));
        #add_action('cron_tools_button', array($this,'averages_button'));
        add_action('cron_tools_button', array($this,'trades_button'));
	}
    
    public function ranks_button(){
        if ( ! empty( $_GET['run_ranks_tool'] ) && $_GET['run_ranks_tool'] == 1) {
            $this->update_ranks();
        }

        echo '
		<div style="padding:10px;margin:10px 0px">
       		<a href="admin.php?page=dtc-tools-cron&run_ranks_tool=1" class="run-rank-tool button primary">Recalculate Ranks</a>
       		<p>  Last Run: </p>
        </div>
		';
    }

	public function trades_button() {
		if ( ! empty( $_GET['run_trade_import'] ) && $_GET['run_trade_import'] == 1) {
			$dtc_mfl = new dtc_mfl;
        	$dtc_mfl->get_dtc_trades();
        }

        echo '
		<div style="padding:10px;margin:10px 0px">
       		<a href="admin.php?page=dtc-tools-cron&run_trade_import=1" class="run-rank-tool button primary">Reimport MFL Trades</a>
       		<p>  Last Run: </p>
        </div>
		';
    }
    
	public function export_table(){
		// 2nd db connection
		$wpdb_dtc = Database_DTC::connection();

		global $wpdb;
		$out = '';

		if ( current_user_can('manage_options')  || current_user_can('dtc_admin_idp') ) {
			if ( ! empty( $_GET['type'] ) ) {
				$type = '_'.$_GET['type'].'';
			} else {
				$type = '';
			}

			$r = $wpdb_dtc->get_results("SELECT * FROM  ". $wpdb_dtc->prefix . "dtc_players".$type." order by rank", ARRAY_A);

			if ( isset( $r[0] )) {
				foreach($r[0] as $k=>$v){
					$out .= '"'.$k.'",';
				}
			}

			$out .="\n";

			for ( $i = 0; $i < count($r); $i++ ) {
			 	foreach( $r[$i] as $val ){
					$out .= '"' . stripslashes( $val ) . '",';
				}

				$out .="\n";
			}

			// Output to browser with appropriate mime type, you choose ;)
			header("Content-type: text/x-csv");
			//header("Content-type: text/csv");
			//header("Content-type: application/csv");
			header("Content-Disposition: attachment; filename=players.csv");
			echo $out;
			exit;
		}
	}
    
    function ajax_save_player_photo_xy(){
    	global $wpdb;
        
       	$return = array();
		
		if ( $_POST['type'] != '' ) {
			$table = '_'.$_POST['type'].'';	
			$type = $_POST['type'];
		} else {
			$table = '';	
			$type = 'list';
		}    
        
        
    	$update['image_x'] =  $_POST['image_x'];
	  	$update['image_y'] =  $_POST['image_y']; 
	 	$update['image_scale'] =  $_POST['image_scale']; 
	 	$update['image_rotation'] =  $_POST['image_rotation']; 
     	$where['id'] = $_POST['player_id'];
        
   		$wpdb->update("".$wpdb->prefix . "dtc_players".$table."",$update,$where);
        $return['update'] = $update;
        $return['where'] = $where;
        $return['db'] = "".$wpdb->prefix . "dtc_players".$table."";
        $return['image_html'] = dtc_build_player_image($_POST['player_id'],$type,250);
        
		echo json_encode($return);
        die(); 
    }

	function ajax_get_player_photo(){
		global $wpdb;
		
		$j = array();

		if ($_POST['type'] != '') {
			$table = '_'.$_POST['type'].'';	
			$type = $_POST['type'];
		} else {
			$table = '';	
			$type = 'list';
		}

		$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_players".$table." where id = %d", $_POST['player_id']), ARRAY_A);
		$j['image']= dtc_get_player_image($r[0]['id'],$type);
        $j['image_html'] = dtc_build_player_image($r[0]['id'],$type,250);
		$j['image_x'] =  $r[0]['image_x'];
		$j['image_y'] =  $r[0]['image_y'];
		$j['image_scale'] =  $r[0]['image_scale'];
		$j['image_rotation'] =  $r[0]['image_rotation'];
		$j['name']= $r[0]['name'];

		// 2nd db connection test
		// $mydb = new wpdb('root','','dynastytradecalculator','127.0.0.1');
		// $rows = $mydb->get_results("select * from wp_dtc_players");
		
		// print_r($rows[0]);
		// wp_die('ok');
		echo json_encode($j);
		die();
	}
	
	function ajax_delete_players(){
		// 2nd db connection
		$wpdb_dtc = Database_DTC::connection();
		
		global $wpdb;
		
		if($_POST['player_id'] != ''){
			$wpdb_dtc->query($wpdb_dtc->prepare("DELETE FROM ".$wpdb_dtc->prefix . "dtc_players WHERE id = %d",$_POST['player_id']));

			// dtc_admin_table::update_averages();
			// dtc_admin_table::update_ranks(); ToDo rank by rtc_value ?
		}

		die();	
	}
	
	function ajax_add_players(){
		// 2nd db connection
		$wpdb_dtc = Database_DTC::connection();
		
		global $wpdb;
		
		if(count($_POST['player'])>0){
			foreach($_POST['player'] as $player){
				if($player['name'] != ''){
					$wpdb_dtc->insert("".$wpdb_dtc->prefix . "dtc_players",$player);
				}
				
				// dtc_admin_table::update_averages();
             	# dtc_admin_table::update_ranks();	
			}
		}
		
		die();	
	}

	public function update_averages(){
		global $wpdb;

		$r = $wpdb->get_results("SELECT * FROM  ".$wpdb->prefix . "dtc_players order by adp asc", ARRAY_A);

		$dtc = array();
		$dtc = get_option('dtc_array');

		if ( $r ) {
			$dtc_count = 0;
			for ($i = 0; $i < count($r); $i++) {
				$update['adp_dtc'] = $dtc[$dtc_count];

				$update['average'] = dtc_get_average(array($r[$i]['yse'],$r[$i]['nyh'],$r[$i]['nrm']));
				$where['id'] = $r[$i]['id'];
				$wpdb->update("".$wpdb->prefix . "dtc_players",$update,$where);
				$dtc_count++;
			}
		}
		
		unset($r);
		unset($where);
		unset($update);
		
		$r = $wpdb->get_results("SELECT * FROM  ".$wpdb->prefix . "dtc_players order by average desc", ARRAY_A);
	
		if ( $r ) {
			$dtc_count = 0;
			
			for ($i = 0; $i < count($r); $i++) {
				$update['adp_dtc'] = $dtc[$dtc_count];
				
				$update['average'] = dtc_get_average(array($r[$i]['yse'],$r[$i]['nyh'],$r[$i]['nrm']));
				$where['id'] = $r[$i]['id'];
				$wpdb->update("".$wpdb->prefix . "dtc_players",$update,$where);
				$dtc_count++;	
			}
		}
		
	}

	public function update_player_rank( $player_id, $rank, $old_rank, $r, $sub= false ) {
		global $wpdb;

		$trend = '';
		if ($sub == true) {
			$update['sub_rank'] = $rank;

			if ($old_rank == 0) {
				$update['sub_rank_trend'] = 0;
			} else {
				if ($old_rank <$rank) {
					$trend = $rank - $old_rank;
					$trend = -1 * abs($trend);

					if ($trend != 0) {
						$update['sub_rank_trend'] = $trend;
					}
				} elseif($old_rank >$rank) {
					if ($trend != 0) {
						$trend = $old_rank - $rank;;
					}

					$update['sub_rank_trend'] = $trend;
				} elseif($old_rank == $rank) {
					#$update['sub_rank_trend'] = 0;
				}

			}

		} else {
			$update['rank'] = $rank;

			if ( $old_rank == 0 ) {
				$update['rank_trend'] = 0;
			} else {
				if ( $old_rank <$rank ) {
					$trend = $rank - $old_rank;
					$trend = -1 * abs($trend);

					if ($trend != 0) {
						$update['rank_trend'] = $trend;
					}

				} elseif($old_rank >$rank) {
					$trend = $old_rank - $rank;

					if ($trend != 0) {
						$update['rank_trend'] = $trend;
					}
				} elseif($old_rank == $rank) {
					#$update['rank_trend'] = 0;
				}
			}
		}

		$where['id'] = $player_id;
		$wpdb->update("".$wpdb->prefix . "dtc_players", $update,$where);
	
		$date = gmdate("Y-m-01");
		$thismonth = date("n") ;
		$newdate = date("n",strtotime ( '+1 month' , strtotime ( $date ) )) ;	
		$stats_exist = $wpdb->get_row($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_players_history where pid = %d and year = %d and type='list' order by id desc limit 1", 
		$player_id,date("Y")), ARRAY_A);

		#print_r($stats_exist);
		#update historicals
		
		if (empty($stats_exist)) {
			$insert['type'] = 'list';
			$insert['pid'] = $player_id;
			$insert['year'] = date("Y");	
			$wpdb->insert("".$wpdb->prefix . "dtc_players_history",$insert);
			$stats_exist = [];
		}	
		
		if (gmdate('t') == gmdate('d') || empty($stats_exist[$thismonth ]) || empty($stats_exist['v'.$thismonth ])) {
			if ( empty($stats_exist[$thismonth ]) ) {
				$update_history[$thismonth ] = $rank;	
				$update_history['v'.$thismonth ] = $r['average'];
			} else {
				$update_history[$newdate] = $rank;
				$update_history['v'.$newdate] = $r['average'];
			}

			$where_history['id'] = $stats_exist['id'];
			#print_r($update_history);
			$wpdb->update("".$wpdb->prefix . "dtc_players_history", $update_history,$where_history);	
		}	
	}

	public function update_ranks() {
		global $wpdb;

		$r = $wpdb->get_results("SELECT * FROM  ".$wpdb->prefix . "dtc_players order by average desc", ARRAY_A);

		if ( $r ) {
			$dtc_count = 0;
			$rank = 1;

			for ($i = 0; $i < count($r); $i++) {
				$this->update_player_rank($r[$i]['id'],$rank,$r[$i]['rank'],$r[$i],false);
				$rank++;
			}
		}

		unset($rank);
		unset($r);

		$positions = array("QB","RB","WR","TE");

		foreach( $positions as $position ) {
			$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_players where position = %s order by average desc", $position), ARRAY_A);

			if ( $r ) {
				$dtc_count = 0;
				$rank = 1;

				for ($i = 0; $i < count($r); $i++) {
					$this->update_player_rank($r[$i]['id'],$rank,$r[$i]['sub_rank'],$r[$i],true);
					$rank++;
				}
			}

			unset($rank);
			unset($r);
		}

	}
		
	function ajax_save_history() {
		global $wpdb;	
		
		if ( count($_POST['update_history'])>0 ) {
			foreach( $_POST['update_history'] as $player_id=>$stats ) {
				$update[1] = $stats[1] ?? '';
				$update[2] = $stats[2] ?? '';
				$update[3] = $stats[3] ?? '';
				$update[4] = $stats[4] ?? '';
				$update[5] = $stats[5] ?? '';
				$update[6] = $stats[6] ?? '';
				$update[7] = $stats[7] ?? '';
				$update[8] = $stats[8] ?? '';
				$update[9] = $stats[9] ?? '';
				$update[10] = $stats[10] ?? '';
				$update[11] = $stats[11] ?? '';
				$update[12] = $stats[12] ?? '';
				
				
				
				$update['v1'] = $stats['v1'] ?? '';
				$update['v2'] = $stats['v2'] ?? '';
				$update['v3'] = $stats['v3'] ?? '';
				$update['v4'] = $stats['v4'] ?? '';
				$update['v5'] = $stats['v5'] ?? '';
				$update['v6'] = $stats['v6'] ?? '';
				$update['v7'] = $stats['v7'] ?? '';
				$update['v8'] = $stats['v8'] ?? '';
				$update['v9'] = $stats['v9'] ?? '';
				$update['v10'] = $stats['v10'] ?? '';
				$update['v11'] = $stats['v11'] ?? '';
				$update['v12'] = $stats['v12'] ?? '';
				
				
				$update['type'] = 'list';
				$update['year'] = $_POST['year'] ?? '';
				$update['pid'] = $player_id;
				
				$stats_exist = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_players_history where pid = %d and year = %d and type = 'list'", $player_id,$update['year']), ARRAY_A);
			
				if ($stats_exist == false) {
					print_r($update);
					$insert = $wpdb->insert("".$wpdb->prefix . "dtc_players_history", $update);
					print_r($insert);
					if($wpdb->last_error !== '') :
						$wpdb->print_error();
					endif;
				} else {
					$where['id'] = $stats_exist[0]['id'];
					$wpdb->update("".$wpdb->prefix . "dtc_players_history", $update,$where);
					
					if ( $wpdb->last_error !== '' ) :
    					$wpdb->print_error();
					endif;
				}		
			}
		}
		
		die();
	}

	function ajax_save_table() {
		// 2nd db connection
		$wpdb_dtc = Database_DTC::connection();
		
		global $wpdb;	
		ini_set('memory_limit', '1024M');
		print_r($_POST['update_stats'] );

		if (count($_POST['update_stats'])>0) {
			foreach($_POST['update_stats'] as $player_id=>$stats){
				// $update['yse'] = $stats['yse'];
				// $update['nyh'] = $stats['nyh'];
				// $update['nrm'] = $stats['nrm'];
				$update['tier'] = $stats['tier'];
				
				if( ! empty( $stats['badges'] ) && is_array($stats['badges'])){
					$update['badge_id'] = serialize($stats['badges']);
				} else {
					$update['badge_id'] = $stats['badges'] ?? '';
				}
				
				$update['qbtier'] = $stats['qbtier'] ?? '';
				$update['birthdate'] = $stats['birthdate'];
				$update['name'] = $stats['name'];
				$update['team'] = $stats['team'];
				$update['position'] = $stats['position'];
				$update['nonppr'] =  ( isset( $stats['nonppr'] ) && $stats['nonppr'] == '1' ? '1' : '0');
				$update['half_ppr'] =  ( isset( $stats['half_ppr'] ) && $stats['half_ppr'] == '1' ? '1' : '0');
				$update['rbppc'] =  ( isset( $stats['rbppc'] ) && $stats['rbppc'] == '1' ? '1' : '0');
				$where['id'] = $player_id;
				
				$wpdb_dtc->update("".$wpdb_dtc->prefix . "dtc_players",$update,$where);
				
				if($wpdb_dtc->last_error !== '') :

					// $str   = htmlspecialchars( $wpdb_dtc->last_result, ENT_QUOTES );
					$query = htmlspecialchars( $wpdb_dtc->last_query, ENT_QUOTES );

					echo "<div id='error'>
						<p class='wpdberror'><strong>WordPress database error:</strong> [error]<br />
						<code>$query</code></p>
						</div>";

				endif;
				
				unset($update);unset($where);
			}
		}

		// merging recalculate and save button. previously below 2 lines were commented out. Not sure why!
		$this->update_averages();
		$this->update_ranks();
		die();
	}

	function ajax_update_ranks(){
		// $this->update_averages();
		$this->update_ranks();
		die();
	}

	function ajax_check_lock() {
		global $wpdb;	

		if ( $_POST['id'] != '' ) {
			echo $wpdb->prepare("DELETE FROM ".$wpdb->prefix . "dtc_players WHERE id =%d",$_POST['id']);
			$wpdb->query($wpdb->prepare("DELETE FROM ".$wpdb->prefix . "dtc_players WHERE id =%d",$_POST['id']));	
			// dtc_admin_table::update_averages();
		}

		die();	
	}

	function ajax_delete_player() {
		global $wpdb;	

		if ( $_POST['id'] != '' ) {
			echo $wpdb->prepare("DELETE FROM ".$wpdb->prefix . "dtc_players WHERE id =%d",$_POST['id']);
			$wpdb->query($wpdb->prepare("DELETE FROM ".$wpdb->prefix . "dtc_players WHERE id =%d",$_POST['id']));	
			// dtc_admin_table::update_averages();
		}

		die();	
	}

	function ajax_edit_stats() {
		global $wpdb;	

		$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_players where id = %d",$_POST['id']), ARRAY_A);		

		$update['yse'] = $_POST['yse'];
		$update['nyh'] = $_POST['nyh'];
		$update['nrm'] = $_POST['nrm'];
		$update['average'] = dtc_get_average(array($update['yse'],$update['nyh'],$update['nrm']));
		$where['id'] = $_POST['id'];
		$wpdb->update("".$wpdb->prefix . "dtc_players",$update,$where);
		die();
	}

	function ajax_get_table() {
		$this->get_table();
		die();
	}
		
	public function get_badge_dropdown($selected ,$id){
		global $wpdb;

		$badges = $wpdb->get_results("SELECT * FROM  ".$wpdb->prefix . "dtc_badges order by name", ARRAY_A);

		$tier = '
		<a href="#" class="badge-chooser button" data-id="'. $id.'">Badges</a>
		<div id="badges-player-'.$id.'" style="height:100px; overflow-y: scroll;padding-left:10px;margin-top:5px;display:none">';

		foreach($badges as $badge) {
			if (in_array($badge['id'],$selected)) {
				$checked = 'checked="checked"';
			} else {
				$checked = '';
			}

			$tier.= '<input type="checkbox" name="update_stats['.$id.'][badges][]" value="'. $badge['id'].'" '.$checked.' /> '.$badge['name'].'<br>';
		}

		$tier .='</div>';

		return $tier;
	}

	public function get_tier_dropdown( $tier_id, $id ) {
		$tiers = array(1,2,3);
		$tier = '<select name="update_stats['.$id.'][tier]"><option value="0">10-Team Tier</option>';

		foreach($tiers as $tierd){
			if ( $tier_id == $tierd ) {
				$selected = 'selected="selected"';
			} else {
				$selected = '';
			}

			$tier.= '<option value="'.$tierd.'" '.$selected.'>10-Team Tier '.$tierd.'</option>';
		}

		$tier .='</select>';

		return $tier;
	}

	public function get_qb_tier_dropdown( $tier_id, $id ) {
		$tiers = array(1,2,3,4,5);

		$tier = '<select name="update_stats['.$id.'][qbtier]"><option value="0">SF/2QB QB Tier</option>';

		foreach( $tiers as $tierd ) {
			if ( $tier_id == $tierd ) {
				$selected = 'selected="selected"';
			} else {
				$selected = '';
			}

			$tier.= '<option value="'.$tierd.'" '.$selected.'>SF/2QB QB Tier '.$tierd.'</option>';
		}

		$tier .='</select>';

		return $tier;
	}

	public function get_table() {
		// 2nd db connection
		$wpdb_dtc = Database_DTC::connection();
		global $wpdb;

		$where = '';

		$dtc_sort = ! empty( $_POST['dtc_sort'] ) ? $_POST['dtc_sort'] : '';
		$dtc_sort_type = ! empty( $_POST['dtc_sort_type'] ) ? $_POST['dtc_sort_type'] : '';
		$dtc_filter = ! empty( $_POST['dtc_filter'] ) ? sanitize_text_field( $_POST['dtc_filter'] ) : '';

		if ( ! empty( $_POST['action'] ) && $_POST['action'] == 'dtc_get_table' ) {	
			$sort = 'ORDER by '. $dtc_sort . ' ' . $dtc_sort_type . '';	

			if ( ! empty( $_POST['dtc_filter']) && $_POST['dtc_filter'] != '' ) {
				if( $_POST['dtc_filter'] != 'ALL' ){
					$where .= ' AND position = "'.$_POST['dtc_filter'] .'"';	
				}
			}
			
			if ( ! empty( $_POST['search'] ) && $_POST['search'] != '' ) {
				$where .= ' AND name like  "%'.$_POST['search'] .'%"';		
			}
				
			if ( $dtc_sort_type == 'desc' ) {
				$sort_type = 'asc';	
			} else {
				$sort_type = 'desc';		
			}
				
			if ( $dtc_sort == '' ) {
				// $sort = 'ORDER by average desc';	
				$sort = 'ORDER by rtc_value desc';	
			}
		} else {
			// $sort = 'ORDER by average desc';	
			$sort = 'ORDER by rtc_value desc';	
		}
		
		if ( ! empty( $_POST['dtc_filter'] ) && $_POST['dtc_filter'] != '' ) {
			$selected_filter = $_POST['dtc_filter'];	
		} else {
			$selected_filter = 'QB';	
		}
		
		if ( $dtc_sort != '') {
			$selected_sort = $_POST['dtc_sort'];
		} else {
			// $selected_sort = 'average';	
			$selected_sort = 'rtc_value';	
		}
		
		if ( $dtc_sort_type != '' ) {
			$selected_sort_type = $_POST['dtc_sort_type'];
		} else {
			$selected_sort_type = 'desc';	
		}
		
  		$items_per_page = 50;

    	$page = isset( $_REQUEST['page'] ) ? abs( (int) $_REQUEST['page'] ) : 1;

    	$offset = ( $page * $items_per_page ) - $items_per_page;
		$offset_end = $offset + $items_per_page;
		
		$query = "
				SELECT * FROM  ".$wpdb_dtc->prefix . "dtc_players
				WHERE id !=0 
				".$where."
				".$sort."
				";
													
		$total = $wpdb_dtc->get_var( $query );
		
		$limit_query = ! empty( $dtc_filter ) && $dtc_filter !== 'ALL' ? '' : " LIMIT {$offset}, $items_per_page ";
		
		$query = "
				SELECT * FROM  ".$wpdb_dtc->prefix . "dtc_players
				WHERE id !=0 
				".$where."
				".$sort."
				$limit_query
				";

		$players = $wpdb_dtc->get_results($query, ARRAY_A);
		$total = ! empty( $dtc_filter ) && $dtc_filter !== 'ALL' ? count( $players ) : $total;

		$query_value_tier_qb = "
				SELECT * FROM  ".$wpdb_dtc->prefix . "dtc_players
				WHERE id !=0 
				AND position = 'QB'
				ORDER by rtc_value desc
				LIMIT 1
				";
		
		$query_value_tier_rb = "
				SELECT * FROM  ".$wpdb_dtc->prefix . "dtc_players
				WHERE id !=0 
				AND position = 'RB'
				ORDER by rtc_value desc
				LIMIT 1
				";
		
		$query_value_tier_wr = "
				SELECT * FROM  ".$wpdb_dtc->prefix . "dtc_players
				WHERE id !=0 
				AND position = 'WR'
				ORDER by rtc_value desc
				LIMIT 1
				";
		
		$query_value_tier_te = "
				SELECT * FROM  ".$wpdb_dtc->prefix . "dtc_players
				WHERE id !=0 
				AND position = 'TE'
				ORDER by rtc_value desc
				LIMIT 1
				";
		
		$query_value_tier_k = "
				SELECT * FROM  ".$wpdb_dtc->prefix . "dtc_players
				WHERE id !=0 
				AND position = 'K'
				ORDER by rtc_value desc
				LIMIT 1
				";

		$query_value_tier_def = "
				SELECT * FROM  ".$wpdb_dtc->prefix . "dtc_players
				WHERE id !=0 
				AND position = 'DEF'
				ORDER by rtc_value desc
				LIMIT 1
				";

		$players_value_tier_qb_results = $wpdb_dtc->get_results($query_value_tier_qb, ARRAY_A);
		$players_value_tier_rb_results = $wpdb_dtc->get_results($query_value_tier_rb, ARRAY_A);
		$players_value_tier_wr_results = $wpdb_dtc->get_results($query_value_tier_wr, ARRAY_A);
		$players_value_tier_te_results = $wpdb_dtc->get_results($query_value_tier_te, ARRAY_A);
		$players_value_tier_k_results = $wpdb_dtc->get_results($query_value_tier_k, ARRAY_A);
		$players_value_tier_def_results = $wpdb_dtc->get_results($query_value_tier_def, ARRAY_A);
		
		print_r('Top Players<br>');
		print_r( "QB: " . $players_value_tier_qb_results[0]['name'] . ' (' . $players_value_tier_qb_results[0]['rtc_value'] . ')<br>' );
		print_r( "RB: " . $players_value_tier_rb_results[0]['name'] . ' (' . $players_value_tier_rb_results[0]['rtc_value'] . ')<br>' );
		print_r( "WR: " . $players_value_tier_wr_results[0]['name'] . ' (' . $players_value_tier_wr_results[0]['rtc_value'] . ')<br>' );
		print_r( "TE: " . $players_value_tier_te_results[0]['name'] . ' (' . $players_value_tier_te_results[0]['rtc_value'] . ')<br>' );
		print_r( "K: " . $players_value_tier_k_results[0]['name'] . ' (' . $players_value_tier_k_results[0]['rtc_value'] . ')<br>' );
		print_r( "DEF: " . $players_value_tier_def_results[0]['name'] . ' (' . $players_value_tier_def_results[0]['rtc_value'] . ')<br>' );

		$dtc_filter_all = $dtc_filter === 'ALL' ? '' : '';
		$dtc_filter_qb 	= $dtc_filter === 'QB' 	? ' active ' : '';
		$dtc_filter_rb 	= $dtc_filter === 'RB' 	? ' active ' : '';
		$dtc_filter_wr 	= $dtc_filter === 'WR' 	? ' active ' : '';
		$dtc_filter_te 	= $dtc_filter === 'TE' 	? ' active ' : '';
		$dtc_filter_k 	= $dtc_filter === 'K' 	? ' active ' : '';
		$dtc_filter_def = $dtc_filter === 'DEF' ? ' active ' : '';

	 	echo '
		 <div class="dtc-filter-menu">
	 		<p><strong>Filter</strong></p>
			<a href="#" class="dtc-filter-position ' . $dtc_filter_all . ' " data-id="ALL" data-sort="'.$selected_sort.'" data-sort-type="'.$selected_sort_type .'">Show All</a>
			<a href="#" class="dtc-filter-position ' . $dtc_filter_qb . ' " data-id="QB" data-sort="'.$selected_sort.'" data-sort-type="'.$selected_sort_type .'">QB</a>
			<a href="#" class="dtc-filter-position ' . $dtc_filter_rb . ' " data-id="RB" data-sort="'.$selected_sort.'" data-sort-type="'.$selected_sort_type .'">RB</a>
			<a href="#" class="dtc-filter-position ' . $dtc_filter_wr . ' " data-id="WR" data-sort="'.$selected_sort.'" data-sort-type="'.$selected_sort_type .'">WR</a>
			<a href="#" class="dtc-filter-position ' . $dtc_filter_te . ' " data-id="TE" data-sort="'.$selected_sort.'" data-sort-type="'.$selected_sort_type .'">TE</a>		
			<a href="#" class="dtc-filter-position ' . $dtc_filter_k . ' " data-id="K" data-sort="'.$selected_sort.'" data-sort-type="'.$selected_sort_type .'">K</a>		
			<a href="#" class="dtc-filter-position ' . $dtc_filter_def . ' " data-id="DEF" data-sort="'.$selected_sort.'" data-sort-type="'.$selected_sort_type .'">DEF</a>		

			<div style="clear:both"></div>
		</div>
	 	';

	 	echo '
	  	<div class="mobile-table-sort">
			<p><strong>Sort</strong></p>
			
			<a href="#" class="dtc-sort-item" data-id="average" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">Rank</a>
			<a href="#" class="dtc-sort-item" data-id="name" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">Name</a>
			<a href="#" class="dtc-sort-item" data-id="position" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">Position</a>
			<a href="#" class="dtc-sort-item" data-id="adp_dtc" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">ADP</a>
			<a href="#" class="dtc-sort-item" data-id="previous_rtc_value" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">LAST</a>
			<a href="#" class="dtc-sort-item" data-id="rtc_value" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">CURRENT</a>
			<div style="clear:both"></div>
		</div>
		';

		if ( ! empty( $_POST['show_edit'] ) && $_POST['show_edit'] == 1 ) {
			$months = array(
				1=>'January',
				2=>'Feburary',
				3=>'March',
				4=>'April',
				5=>'May',
				6=>'June',
				7=>'July',
				8=>'August',
				9=>'September',
				10=>'October',
				11=>'November',
				12=>'December'
			);

			echo ' 
	  		<form class="dtc-data-form">
	  			<input type="hidden" name="action" value="dtc_save_history">
				<input type="hidden" name="year" value="'.$_REQUEST['show_year'].'" id="show_year">
			
			 	<table class="wp-list-table widefat fixed posts cell-border compact stripe" cellspacing="0" id="dtc-main-table">
	  				<thead>
						<tr class="dtc-table-header" cope="col">
							<th style="display:none" cope="col" >ID</th>
							<th cope="col" style="width:210px" >Name</th>
							<th cope="col" style="width:50px" >Type</th>
			';
			
			foreach($months as $k=>$month) {
				echo '<th cope="col" >'.$month.'</th>';	
			}

			echo'</tr></thead>';
			
			if ( $players ) {
		  		$rank = 1;
			 	for ($i = 0; $i < count($players); $i++) {
					unset( $player);
					$player_stats = dtc_get_player_stats($players[$i]['id'],$_REQUEST['show_year'],$players[$i]['type']);
				
				 	echo '
					<tr class="row-'.$players[$i]['position'].'">
				 		<td style="display:none">'. $players[$i]['id'].'<input type="hidden" name="update_history['. $players[$i]['id'].'][name]" value="'.$players[$i]['name'].'"></td>
						<td  data-label="Name" style="width:210px"  ><strong>'.stripslashes($players[$i]['name']).'</strong></td><td>Rank</td>
					';
					
					foreach($months as $k=>$month) {
						if ( !isset($player_stats[$k]) ) {
							$player_stats[$k] = 0;	
						}

						echo '<td data-label="'.$month.'"  ><input type="text" style="width:100%;max-width:none" name="update_history['. $players[$i]['id'].']['.$k.']" value="'. $player_stats[$k].'"><span style="display:none">'. $player_stats[$k].'</span></td>';	
					}

					echo '
					</tr>
					';
				  
					echo '
					<tr class="row-'.$players[$i]['position'].'">
				 		<td style="display:none">'. $players[$i]['id'].'<input type="hidden" name="update_history['. $players[$i]['id'].'][name]" value="'.$players[$i]['name'].'"></td>
						<td  data-label="Name" style="width:210px" > '.$players[$i]['position'].' - '.$players[$i]['team'].'</td><td>Value</td>
					';
					
					foreach($months as $k=>$month) {
						if (!isset($player_stats[$k])) {
							$player_stats[$k] = 0;	
						}

						$player_stats['v'.$k] = $player_stats['v'.$k] ?? '';
						echo '		
						<td  data-label="'.$month.'"  ><input type="text" style="width:100%;max-width:none" name="update_history['. $players[$i]['id'].'][v'.$k.']" value="'. $player_stats['v'.$k].'"><span style="display:none">'. $player_stats['v'.$k].'</span></td>
						';	
					}
			
					echo '
					</tr>
					';
				}
			 
			}
			 
			echo '
			 	</table>
			</form>
			';	

		} else {
			$pagination_showing_text = '<p>Showing '.$offset.'-'.$offset_end.' out of '.$total. ' Players</p>';

			if ( ! empty( $dtc_filter ) && $dtc_filter !== 'ALL' ) {
				$pagination_showing_text = "<p>Showing 1 - $total out of $total Players</p>";
			}

			echo $pagination_showing_text;

			echo '<div class="pagination">';
			
			$pagination = paginate_links( array(
				'base' => add_query_arg( 'cpage', '%#%' ),
				'format' => '',

				'total' => ceil($total / $items_per_page),
				'current' => $page,
				'show_all' => true,
				'type' => 'list',
				'prev_next'=>false,
			));

			$pagination = str_replace("<ul class='page-numbers'>", '<ul class="pagination-ul">',$pagination);	
			$pagination = str_replace('page-numbers', 'page-numbers get-table-page',$pagination);
			$request_dtc_sort = ! empty( $_REQUEST['dtc_sort'] ) ? $_REQUEST['dtc_sort'] : '';
			$request_dtc_filter = ! empty( $_REQUEST['dtc_filter'] ) ? $_REQUEST['dtc_filter'] : '';
			$request_dtc_sort_type = ! empty( $_REQUEST['dtc_sort_type'] ) ? $_REQUEST['dtc_sort_type'] : '';

			echo $pagination;
			echo '<div style="clear:both"></div></div>';
			
			echo ' 
			<script type="text/javascript">
				jQuery(document).ready( function ($) {
				//  $(".wp-list-table").DataTable();
				} );
			</script>

			<form class="dtc-data-form">
				<input type="hidden" name="action" value="dtc_save_table">
				<input type="hidden" name="dtc_sort" id="dtc_sort" value="' . $request_dtc_sort . '">
				<input type="hidden" name="dtc_filter" id="dtc_filter" value="' . $request_dtc_filter . '">
				<input type="hidden" name="dtc_sort_type" id="dtc_sort_type" value="' . $request_dtc_sort_type . '">
				<table class="wp-list-table widefat posts cell-border compact stripe " style="table-layout:auto" cellspacing="0" id="dtc-main-table">
					<thead>
						<tr class="dtc-table-header" cope="col">
							<th style="display:none" cope="col" >ID</th>
						
							<th cope="col" style="width:auto; padding:.25em;"><a href="#" class="dtc-sort-item" data-id="average" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">Rank</a></th>
							<th cope="col" style="width:auto; padding:.25em;">Non-PPR</th>
							<th cope="col" style="width:auto; padding:.25em;">0.5 PPR</th>
							<th cope="col" style="width:auto; padding:.25em;">RB PPC</th>
							<th cope="col" style="width:auto; padding:.25em;">Value Tier</th>
							<th cope="col" style="width:auto; padding:.25em;">Tier</th>
							<th cope="col" style="width:auto; padding:.25em;"><a href="#" class="dtc-sort-item" data-id="name" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">Name</a></th>
							
							<th cope="col" style="width:auto; padding:.25em;"><a href="#" class="dtc-sort-item" data-id="position" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">Position</a></th>
							<th cope="col" style="width:auto; padding:.25em;"><a href="#" class="dtc-sort-item" data-id="team" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">Team</a></th>
							<th cope="col" style="width:auto; padding:.25em;">Birthdate</th>
							
							<th cope="col" style="width:auto; padding:.25em;"><a href="#" class="dtc-sort-item" data-id="previous_rtc_value" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">LAST</a></th>
							<th cope="col" style="width:auto; padding:.25em;"><a href="#" class="dtc-sort-item" data-id="rtc_value" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">CURRENT</a></th>
							<th style="width:auto;"></th>
						</tr>
					</thead>
			';

			if ( $players ) {
				$rank = 1;
			
				for ($i = 0; $i < count($players); $i++) {
					$checked =  ($players[$i]['nonppr']== '1' ? 'checked="checked"' : '');
					$halfpprchecked =  ($players[$i]['half_ppr']== '1' ? 'checked="checked"' : '');
					$rbchecked =  ($players[$i]['rbppc']== '1' ? 'checked="checked"' : '');
					
					if ($players[$i]['birthdate'] == '0000-00-00') {
						$date = '';	
					} else {
						$date = $players[$i]['birthdate'];	
					}
			
					$player_rank = $offset + $rank;
			
					$players_value_tier_results = $players_value_tier_qb_results;
			
					switch( $players[$i]['position'] ) {
						case 'QB':
							$players_value_tier_results = $players_value_tier_qb_results;
							break;

						case 'RB':
							$players_value_tier_results = $players_value_tier_rb_results;
							break;

						case 'WR':
							$players_value_tier_results = $players_value_tier_wr_results;
							break;

						case 'TE':
							$players_value_tier_results = $players_value_tier_te_results;
							break;

						case 'K':
							$players_value_tier_results = $players_value_tier_k_results;
							break;

						case 'DEF':
							$players_value_tier_results = $players_value_tier_def_results;
							break;

						default:
							break;
					}
			
					$player_value_tier = dtc_get_player_value_tier( $players[$i], $players_value_tier_results[0]);
					echo '
					<tr class="row-'.$players[$i]['position'].'">
						<td style="display:none">'. $players[$i]['id'].'<input type="hidden" name="update_stats['. $players[$i]['id'].'][name]" value="'.$players[$i]['name'].'"></td>
						<td style="padding:.25em;" data-label="Rank">'. esc_html( $player_rank ) /* . "(" . $players[$i]['rank'] . ")" */ .' '.dtc_show_rank($players[$i]['rank_trend']).'</td>
						<td style="padding:.25em;" data-label="NonPPR"><input type="checkbox" value="1" name="update_stats['. $players[$i]['id'].'][nonppr]" '. $checked.'></td>
						<td style="padding:.25em;" data-label="HalfPPR"><input type="checkbox" value="1" name="update_stats['. $players[$i]['id'].'][half_ppr]" '. $halfpprchecked.'></td>
						<td style="padding:.25em;" data-label="RBPPC">
					';

					if($players[$i]['position'] == 'RB'){
						echo '<input type="checkbox" value="1" name="update_stats['. $players[$i]['id'].'][rbppc]" '.  $rbchecked.'>';	
					}

					echo'
						</td>
						<td data-label="Value Tier">' . $player_value_tier . '</td>
						<td style="padding:.25em;" data-label="NonPPR">'.$this->get_tier_dropdown( $players[$i]['tier'], $players[$i]['id']).''
					;

					if ($players[$i]['position'] == 'QB') {
						echo '<br>'.$this->get_qb_tier_dropdown( $players[$i]['qbtier'], $players[$i]['id']).'';
					}

					echo '<br>';
					
					if ( $players[$i]['badge_id'] == '' ) {
						$badges  = array();	
					}else{
						$badges = unserialize($players[$i]['badge_id']);	
					}
					
					echo '
						</td>
						<td style="padding:.25em; text-align:left;" data-label="Name">
							<input style="max-width:none;width:100% !important; margin: 2px 0 .8em;" type="text" name="update_stats['. $players[$i]['id'].'][name]" value="'.stripslashes($players[$i]['name']).'">
							<a href="#" data-id="'. $players[$i]['id'].'" class="update-player-photo button"  data-type="">Edit Photo</a>
							'.$this->get_badge_dropdown($badges, $players[$i]['id']).'
						</td>
		
						<td style="padding:.25em;" data-label="Position" ><span class="position-'.$players[$i]['position'].'"><input  style="width:50px"    type="text" name="update_stats['. $players[$i]['id'].'][position]" value="'.$players[$i]['position'].'"></span></td>
						<td style="padding:.25em;" data-label="Team"><span class="position-'.$players[$i]['team'].'"><input  style="width:50px" type="text" name="update_stats['. $players[$i]['id'].'][team]" value="'.$players[$i]['team'].'"></span></td>
						<td style="padding:.25em;" data-label="Birthdate"><input class="dtc_datepicker" type="text" name="update_stats['. $players[$i]['id'].'][birthdate]" value="'.$date .'" style="max-width:100px;width:100px;"></td>
						
						<td data-label="AVG_LAST">'.$players[$i]['previous_rtc_value'].'</td>
						<td data-label="AVG_CURRENT">'.$players[$i]['rtc_value'].'</td>
						<td style="padding:.25em;"><a href="#" class="dtc-delete-player" data-id="'.$players[$i]['id'].'" class="button"><span class="dashicons dashicons-dismiss"></span></a></td>
					</tr>
					';

					$rank += 1;
				}
			}
			
			echo '
				</table>
			</form>
			';
		}
		?>
     
		<script type="text/javascript">
			jQuery(document).ready(function($) {
				/*
				$('#dtc-main-table').Tabledit({
				url:ajaxurl,
				restoreButton: false,
				editButton: false,
			
				hideIdentifier: true,
				onSuccess: function(data, textStatus, jqXHR) {
				
				$.post(ajaxurl, {'action':'dtc_get_table'}, function(response) {
						$(".dtc-player-table").html(response);
					});
				
				},
			
				buttons: {
					edit: {
						class: 'btn btn-sm btn-default',
						html: '<span class="dashicons dashicons-welcome-write-blog dtc-check-lock"></span>',
						action: 'dtc_edit_stats'
					},
					delete: {
						class: 'btn btn-sm btn-default',
						html: '<span class="dashicons dashicons-trash"></span>',
						action: 'dtc_confirm_player'
					},
					confirm: {
						class: 'btn btn-sm btn-default',
						html: 'Confirm Delete',
						action: 'dtc_confirm_player'
					},
					save: {
						class: 'button',
						html: 'Save'
					},
				
				},
				columns: {
					identifier: [0, 'id'],
					editable: [ [5, 'yse'], [6, 'nyh'], [7, 'nrm']]
				}
			});
			*/
			});
		</script>     
		<?php
	}
	
	public function import_players() {
		if ( ! empty( $_POST['dtc-import-players'] ) && $_POST['dtc-import-players'] != '' ) {
			global $wpdb;

			$table = $_POST['table'];
			require_once(''.DTC_DIR.'classes/PHPExcel.php');
			$worksheets = array();
			$filename = $_FILES['players']['tmp_name'];
			$type = PHPExcel_IOFactory::identify($filename);
			$objReader = PHPExcel_IOFactory::createReader($type);
			$objPHPExcel = $objReader->load($filename);
			$dtc = get_option('dtc_array');
			
			foreach ($objPHPExcel->getWorksheetIterator() as $worksheet) {
				$worksheets[$worksheet->getTitle()] = $worksheet->toArray();
			}

			if ( count($worksheets)>0 ) {
				$inserted= 0;
				$updated = 0;
				
				foreach($worksheets as $r) {
					if ( count($r)>0 ) {
						$dtc_count = 0;
						
						for ($i = 0; $i < count($r); $i++) {
							$headers = $r[0];
							
							foreach($headers as $key=>$header) {
								$insert[$header] = 	$r[$i][$key];
							}
							
							if ($insert['name'] != 'name' && $insert['dtc'] != 'dtc') {
								if ($table == "".$wpdb->prefix . "dtc_players") {
									$insert['adp_dtc'] = $dtc[$dtc_count];	
								}

								$dtc_count++;
							
								if (dtc_player_exists($insert['name'],$insert['position'],$table) == false) {
									$wpdb->insert($table, $insert);
									$inserted++;
								} else {
									$where['name'] = $insert['name'];
									$where['position'] = $insert['position'];
									$wpdb->update($table, $insert,$where);	
									$updated++;
								}

								unset($where);
								unset($insert);
							}
						}
					}
				}
			}

			// $this->update_averages();
			$this->update_ranks();

			echo '
			<div class="notice notice-success is-dismissible">
        		<p>'.__( 'Inserted '.$inserted.' Players and Updated '.$updated.' Players', 'dtc' ).'</p>
    		</div>
			';
		}
		
		if ( ! empty( $_POST['dtc-import-dtc'] ) && $_POST['dtc-import-dtc'] != '' ) {
			$dtc = $_POST['dtc_data'];
			$dtc_array = array();
			
			if( $dtc ) {
				$dtc_array = explode(PHP_EOL, $dtc );
			}

			update_option('dtc_array',array_filter($dtc_array));
		}
	}
	
	public function view() {
		global $wpdb;
		$this->import_players();

		add_thickbox();
	 
		if ( ! empty( $_POST['dtc-save-image'] ) && $_POST['dtc-save-image'] != '') {	
			if( $_FILES["upload-photo"]["name"] != '' ){
       			$upload_path = wp_upload_dir();
				
				$main_image = $upload_path['basedir'].'/headshots_new/players/'.$_POST['update-player-photo-field-id'].'.png';
				$thumb_image = $upload_path['basedir'].'/headshots_new/players/thumbs/'.$_POST['update-player-photo-field-id'].'.png';
				
				move_uploaded_file($_FILES["upload-photo"]["tmp_name"], $main_image);
				copy($main_image, $thumb_image);
				
				echo $upload_path['basedir'].'/headshots_new/players/'.$_POST['update-player-photo-field-id'].'.png';
				echo '<div class="dtc-success">Updated Photo for '.$_POST['update-player-photo-field-id'].'</div>';
			}
		}

		$dtc = array();
		$dtc = get_option('dtc_array');
		$dtc_data = implode("\n",$dtc);
		?>
        
        <style type="text/css">
		</style>

		<script type="text/javascript">
			jQuery(document).ready(function($) {
			//change the integers below to match the height of your upper div, which I called
			//banner.  Just add a 1 to the last number.  console.log($(window).scrollTop())
			//to figure out what the scroll position is when exactly you want to fix the nav
			//bar or div or whatever.  I stuck in the console.log for you.  Just remove when
			//you know the position.
			$(window).scroll(function () { 

				console.log($(window).scrollTop());

				if ($(window).scrollTop() > 350) {
				$('.dtc-top-menu').addClass('navbar-fixed-top');
				}

				if ($(window).scrollTop() < 351) {
				$('.dtc-top-menu').removeClass('navbar-fixed-top');
				}
			});
			});
	 	</script>

        <?php
		echo '	
		<div id="dtc-import-players" style="display:none;">
			<h2>Import Players</h2>
			<form action="" method="post" enctype="multipart/form-data">
				<input type="hidden" name="table" value="'.$wpdb->prefix . 'dtc_players">
				Upload Players Excel Document 
				<input type="file" name="players"> <input name="dtc-import-players" type="submit" value="Import">
			</form>
		</div>

		<div id="dtc-import-adp" style="display:none;">
     		<h2>Import ADP</h2>
	 		<form action="" method="post" enctype="multipart/form-data">
				<input type="hidden" name="table" value="'.$wpdb->prefix . 'dtc_players">
	 			Upload Players Excel Document <input type="file" name="players"> <input name="dtc-import-players" type="submit" value="Import">
	 		</form>
		</div>

		<div id="dtc-edit-dtc" style="display:none;">
			<h2>Modify DTC</h2>
			<form action="" method="post" enctype="multipart/form-data">
				<textarea name="dtc_data" value="" style="width:100%;height:500px" class="dtc-editor">'.$dtc_data.'</textarea><br>
				<input name="dtc-import-dtc" type="submit" value="Save">
			</form>
		</div>

		<div id="dtc-add-players" style="display:none;">
     		<h2>Add Players</h2>
	 		
			<form action="" method="post" enctype="multipart/form-data" class="dtc-add-players-form">
	 			<input type="hidden" name="action" value="dtc_add_players">
	 			
				<table class="wp-list-table widefat fixed posts" cellspacing="0" id="dtc-add-main-table">
	 				<thead>
	 					<tr>
	  						<th>Name</th>
	  						<th>Position</th>
							<th>Team</th>
							<th>Birth Date</th>	  
							<th>ADP</th>
							<th>YSE</th>
							<th>NYH</th>
							<th>NRM</th>
	 					</tr>
	 				</thead>
	 				
					<tbody>
	 	';

		for ($i = 1; $i <= 10; $i++) {
			echo '
						<tr>
							<th><input type="text" name="player['.$i.'][name]" value=""></th>
							<th><input type="text" name="player['.$i.'][position]" value=""></th>
							<th><input type="text" name="player['.$i.'][team]" value=""></th>
							<th><input type="text" name="player['.$i.'][birthdate]" value="" class="dtc_datepicker"></th>
							<th><input type="text" name="player['.$i.'][adp]" value=""></th>
							<th><input type="text" name="player['.$i.'][yse]" value=""></th>
							<th><input type="text" name="player['.$i.'][nyh]" value=""></th>
							<th><input type="text" name="player['.$i.'][nrm]" value=""></th>
						</tr>
			';	
		}
	 
	 	echo '
					</tbody>
	 			</table>
	 			
				<input name="dtc-import-dtc" type="submit" value="Save">
			</form>
		</div>

		<div id="update-player-photo-modal" style="display:none;">
			<div class="update-player-photo-modal-inner">
				<form class="update-player-photo-form" enctype="multipart/form-data" method="post" action="">
					<input type="hidden" class="update-player-photo-field-id" name="update-player-photo-field-id" value="">
					
					<div class="update-player-photo-form-image">
					</div>
					
					<input type="hidden" name="update-player-photo-field"  class="update-player-photo-field">
					<p><input type="file" name="upload-photo"> <input type="submit" name="dtc-save-image" value="Upload Photo"></p>
				</form>

				<form class="update-player-photo-xy">
					<input type="hidden" name="action" value="dtc_update_player_axis">
					<input type="hidden" name="type" class="update-player-photo-field-type" >
					<input type="hidden" class="update-player-photo-field-id" name="player_id" value="">
					<p>Modify the position below</p>
					<strong>X:</strong> <input type="text" name="image_x" value="" class="update-player-photo-x" style="width:60px"> 
					<strong>Y:</strong><input type="text" name="image_y" value="" class="update-player-photo-y" style="width:60px"> 
					<strong>Scale:</strong> <input type="text" name="image_scale" value="" class="update-player-photo-scale" style="width:60px">% 
					<strong>Rotation:</strong><input type="text" name="image_rotation" value="" class="update-player-photo-rotation" style="width:60px">Deg
					<input type="submit" name="save_image_axis" value="Save Axis">
				</form>
			</div>
		</div>

	 	<div class="dtc-admin-wrapper">
		';
	 	
		dtc_admin_header('Calculator Values');
	 
	 	echo '
			<div class="dtc-top-menu">
	 			<a href="#" class="search-player button" style="float:right">Search</a>
				<input class="dtc-admin-search" type="text" name="search" value="" style="float:right"> 
				
				<div style="clear:both"></div>
				
				<a href="#" class="dtc-save-table" style="background-color:#f2a535">Save Values</a>
				<a href="#" class="dtc-recalculate-values" style="background-color:#77bc07; display: none;">Recalculate Values</a>
				<a href="#TB_inline?width=600&height=150&inlineId=dtc-import-adp" class="thickbox advanced-button">Import ADP</a> 
				
				<a href="#TB_inline?width=600&height=650&inlineId=dtc-edit-dtc" class="thickbox advanced-button">Modify DTC</a>
				<a  href="#TB_inline?width=600&height=650&inlineId=dtc-add-players" class="thickbox advanced-button">Add Players</a> 
				<a  href="'. admin_url( 'admin-ajax.php' ).'?action=dtc_export_table" class="advanced-button" >Export Players</a> 
				
				<div style="clear:both"></div>
			</div>
	 	';

		$showedit = '';
	  
		echo '
			<script type="text/javascript">
				jQuery(document).ready(function($) {
					dtc_preloader("Loading");;
					dtc_get_table();;
				});
			</script>

			<div style="padding:5px;background-color;#EFEFEF">
				<input type="checkbox" class="show-historical-data" value="1" '.$showedit.' > 
				Edit historical data 
				
				<select class="show-historical-data-year" style="display:none">
					<option value="2022">2022</option>
					<option value="2021">2021</option>
					<option value="2020">2020</option>
					<option value="2019">2019</option>
					<option value="2018">2018</option>
					<option value="2017">2017</option>
				</select> 
				
				<a href="#" data-table="#dtc-main-table" class="dtc-export-excel" style="text-decoration:none"><span class="dashicons dashicons-media-spreadsheet"></span></a>
			</div>

			<div class="dtc-player-table">';
				//dtc_admin_table::get_table();
		echo '
			</div>
		';

	   	echo '
		</div>
		';
	}
}