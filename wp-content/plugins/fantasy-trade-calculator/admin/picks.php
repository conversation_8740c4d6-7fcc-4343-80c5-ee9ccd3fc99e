<?php

new dtc_admin_picks;

class dtc_admin_picks{
	
	public function __construct(){

		add_action( 'wp_ajax_dtc_add_pick', array('dtc_admin_picks','add'));
		add_action( 'wp_ajax_dtc_save_picks', array('dtc_admin_picks','save'));
		add_action( 'wp_ajax_dtc_delete_pick', array('dtc_admin_picks','delete'));
		add_action( 'wp_ajax_dtc_get_picks_table', array('dtc_admin_picks','ajax_get_table'));

	}
	function enqueue_scripts(){
		

		
		
	}
	function delete(){
		
		global $wpdb;
		
		if($_POST['pick_id'] != ''){
			$wpdb->query($wpdb->prepare("DELETE FROM ".$wpdb->prefix . "dtc_draft_picks WHERE id = %d",$_POST['pick_id']));		
			
		}
		die();
	}
	function save(){
		
		if(count($_POST['picks'])>0){
		global $wpdb;
		
			foreach($_POST['picks'] as $pick_id=>$stats){
			
					$update['pick'] = $stats['pick'];
				
				
					$update['ten'] =  $stats['ten'];
					$update['twelve'] = $stats['twelve'];
					$update['fourteen'] = $stats['fourteen'];
					$update['sixteen'] = $stats['sixteen'];
				
					$update['tensf'] =  $stats['tensf'];
					$update['twelvesf'] = $stats['twelvesf'];
					$update['fourteensf'] = $stats['fourteensf'];
					$update['sixteensf'] = $stats['sixteensf'];
                    $update['dropdown_active'] = $_POST['enabled'][$pick_id];
					$where['id'] = $pick_id;
					
					
			
			
					$wpdb->update("".$wpdb->prefix . "dtc_draft_picks",$update,$where);
					
					   if($wpdb->last_error !== '') :

        $str   = htmlspecialchars( $wpdb->last_result, ENT_QUOTES );
        $query = htmlspecialchars( $wpdb->last_query, ENT_QUOTES );

        print "<div id='error'>
        <p class='wpdberror'><strong>WordPress database error:</strong> [$str]<br />
        <code>$query</code></p>
        </div>";

    endif;
					unset($update);unset($where);
			}
	}
	
	die();
		
	}
	function add(){
		global $wpdb;
		
		$insert['pick'] = $_POST['pick'];
		
		$insert['ten'] =  $_POST['ten'] ;
		$insert['twelve'] =  $_POST['twelve'] ;
		$insert['fourteen'] =  $_POST['fourteen'] ;
		$insert['sixteen'] =  $_POST['sixteen'] ;
		
	
		$insert['tensf'] =  $_POST['tensf'] ;
		$insert['twelvesf'] =  $_POST['twelvesf'] ;
		$insert['fourteensf'] =  $_POST['fourteensf'] ;
		$insert['sixteensf'] =  $_POST['sixteensf'] ;
        
	
		$insert = $wpdb->insert("".$wpdb->prefix . "dtc_draft_picks",$insert);
	
		   if($wpdb->last_error !== '') :

        $str   = htmlspecialchars( $wpdb->last_result, ENT_QUOTES );
        $query = htmlspecialchars( $wpdb->last_query, ENT_QUOTES );

        print "<div id='error'>
        <p class='wpdberror'><strong>WordPress database error:</strong> [$str]<br />
        <code>$query</code></p>
        </div>";

    endif;

	die();
		
	}
	function ajax_get_table(){

		$this->get_table();
		die();
	}
	public function get_table(){
		global $wpdb;
		$r = $wpdb->get_results("SELECT * FROM  ".$wpdb->prefix . "dtc_draft_picks order by dropdown_active,pick", ARRAY_A);
	
	
	if($r){
		?>
        
        		<script>
 
 function round(value, precision) {
    var multiplier = Math.pow(10, precision || 0);
    return Math.round(value * multiplier) / multiplier;
}
  jQuery( function($) {

    $( "#slider" ).slider({
      min: -500,
      max: 500,
      slide: function( event, ui ) {
       $(".slider-percentage-amount").text( ui.value );
	   
	   
	   
	   $( ".slider-calculate" ).each(function() {
		
	
			var val =  parseInt($(this).attr('data-value'));
					val = val + (val * (parseInt(ui.value) / 100));
						
					$(this).val(round(val,1));	
			
		
			
		});
	   
      }
    });
  } );
  </script>
  <div style="max-width:500px;clear:both;padding:20px">
	 <label for="amount" style="margin:10px 0px">Bulk Pick Adjustments <strong><span class="slider-percentage-amount">0</span>%</strong></label>
    <div id="slider">

</div>
</div>
<div style="clear:both"></div>
        
        <?php
		echo '

		
		<form class="dtc-picks-form"> 
		<input type="hidden" name="action" value="dtc_save_picks">
		<table class="wp-list-table widefat fixed posts" cellspacing="0" id="dtc-main-table">
	  		<thead>
			<tr class="dtc-table-header" cope="col">
			<th cope="col" >Pick</th>
			
			<th cope="col" >10 Team</th>
			<th cope="col" >12 Team</th>
			<th cope="col" >14 Team</th>
			<th cope="col" >16 Team</th>
			
			
			<th cope="col" >10 Team SF</th>
			<th cope="col" >12 Team  SF</th>
			<th cope="col" >14 Team  SF</th>
			<th cope="col" >16 Team  SF</th>
			<th cope="col" >Disable Dropdown</th>
			<th cope="col" >Action</th>
			</tr></thead>';
			for ($i = 0; $i < count($r); $i++) {
				
				$tenchecked = ($r[$i]['ten'] == '1' ? 'checked="checked"' : "");
				$twelvechecked = ($r[$i]['twelve'] == '1' ? 'checked="checked"' : "");
				$fourchecked = ($r[$i]['fourteen'] == '1' ? 'checked="checked"' : "");
				$sixchecked = ($r[$i]['sixteen'] == '1' ? 'checked="checked"' : "");
				
            if($r[$i]['dropdown_active'] == 1){
             
                $checked = 'checked="checked"';
                
            }else{
                $checked = '';   
            }
                echo '<tr>
						<td><input type="text" name="picks['.$r[$i]['id'].'][pick]" value="'.$r[$i]['pick'].'"></td>
					
						
						
						<td><input class="slider-calculate" type="text" name="picks['.$r[$i]['id'].'][ten]" data-value="'.$r[$i]['ten'].'" value="'.$r[$i]['ten'].'"></td>
						<td><input class="slider-calculate" type="text" name="picks['.$r[$i]['id'].'][twelve]" data-value="'.$r[$i]['twelve'].'" value="'.$r[$i]['twelve'].'"></td>
						<td><input class="slider-calculate" type="text" name="picks['.$r[$i]['id'].'][fourteen]" data-value="'.$r[$i]['fourteen'].'" value="'.$r[$i]['fourteen'].'"></td>
						<td><input class="slider-calculate" type="text" name="picks['.$r[$i]['id'].'][sixteen]" data-value="'.$r[$i]['sixteen'].'" value="'.$r[$i]['sixteen'].'"></td>
						
						<td><input class="slider-calculate"  type="text" name="picks['.$r[$i]['id'].'][tensf]" data-value="'.$r[$i]['tensf'].'" value="'.$r[$i]['tensf'].'"></td>
						<td><input class="slider-calculate" type="text" name="picks['.$r[$i]['id'].'][twelvesf]" data-value="'.$r[$i]['twelvesf'].'" value="'.$r[$i]['twelvesf'].'"></td>
						<td><input class="slider-calculate" type="text" name="picks['.$r[$i]['id'].'][fourteensf]" data-value="'.$r[$i]['fourteensf'].'" value="'.$r[$i]['fourteensf'].'"></td>
						<td><input class="slider-calculate" type="text" name="picks['.$r[$i]['id'].'][sixteensf]" data-value="'.$r[$i]['sixteensf'].'" value="'.$r[$i]['sixteensf'].'"></td>
						<td><input type="checkbox" name="enabled['.$r[$i]['id'].']" value="1" '.$checked.'></td>
						
						<td><a href="#" class="dtc-delete-pick" data-id="'.$r[$i]['id'].'" class="button"><span class="dashicons dashicons-dismiss"></span></a></td>
					</tr>';
					
					
						
			}
		echo '</table></form>';
	}
	
	}
	public function view(){
	global $wpdb;
		add_thickbox();
	
	echo '	 <div id="dtc-add-pick" style="display:none;">
     <h2>Add Pick</h2>
	 <form action="" method="post" enctype="multipart/form-data" class="dtc-add-pick-form">
	
	 <input type="hidden" name="action" value="dtc_add_pick">
	 Pick <input type="text" name="pick"><br>
	 
	 <input type="text" name="ten"> 10 Person <br>
	 <input type="text" name="twelve"> 12 Person <br>
	 <input type="text" name="fourteen"> 14 Person <br>
	 <input type="text" name="sixteen"> 16 Person <br><br>
	  <input type="text" name="tensf"> 10 Person SF <br>
	 <input type="text" name="twelvesf"> 12 Person SF <br>
	 <input type="text" name="fourteensf"> 14 Person SF<br>
	 <input type="text" name="sixteensf"> 16 Person SF<br>
	 <br><input name="dtc-add-pick" type="submit" value="Add">
	 </form>
</div>
	 ';
	
	
	echo ' <div class="dtc-admin-wrapper">';
	 dtc_admin_header('Pick Values');
		 
	 echo '<div class="dtc-top-menu">
	 		<a href="#" class="dtc-save-picks" style="background-color:#f2a535">Save</a>
	 		<a href="#TB_inline?width=600&height=550&inlineId=dtc-add-pick" class="thickbox">Add Pick</a>
			</div>
	 ';
	 
	

		echo '<div class="dtc-picks-table">';

		$this->get_table();

		echo'</div>';
	
		
		echo '</div>';
	}
	
	
}