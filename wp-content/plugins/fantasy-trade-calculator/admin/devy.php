<?php 
new dtc_admin_devy_table;


class dtc_admin_devy_table{
	
	public function __construct(){

		add_action( 'wp_ajax_dtc_devy_get_table', array('dtc_admin_devy_table','ajax_get_table'));
		add_action( 'wp_ajax_dtc_devy_edit_stats', array('dtc_admin_devy_table','ajax_edit_stats'));
		add_action( 'wp_ajax_dtc_devy_confirm_player', array('dtc_admin_devy_table','ajax_delete_player'));
		add_action( 'wp_ajax_dtc_devy_save_table', array('dtc_admin_devy_table','ajax_save_table'));
		add_action( 'wp_ajax_dtc_devy_add_players', array('dtc_admin_devy_table','ajax_add_players'));
		add_action( 'wp_ajax_dtc_devy_delete_player', array('dtc_admin_devy_table','ajax_delete_players'));
		add_action( 'wp_ajax_dtc_devy_get_player_photo', array('dtc_admin_devy_table','ajax_get_player_photo'));


	}
	
	function ajax_get_player_photo(){
		global $wpdb;
		
		$j = array();
		$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_players_devy where id = %d", $_POST['player_id']), ARRAY_A);
		
		$j['image']= dtc_get_player_image($r[0]['id'],$r[0]['list']);
		$j['name']= $r[0]['name'];
		
		echo json_encode($j);
		die();
	}
	
	function ajax_delete_players(){
		global $wpdb;
		
	if($_POST['player_id'] != ''){
	
	

	$wpdb->query($wpdb->prepare("DELETE FROM ".$wpdb->prefix . "dtc_players_devy WHERE id = %d",$_POST['player_id']));



	$this->update_averages();
	$this->update_ranks();
		}
			
	
	
	die();	
	}
	
	function ajax_add_players(){
		global $wpdb;
		
		if(count($_POST['player'])>0){
			
			foreach($_POST['player'] as $player){
				
				if($player['name'] != ''){
							
						$wpdb->insert("".$wpdb->prefix . "dtc_players_devy",$player);	
					
				}
				
			$this->update_averages();
			$this->update_ranks();
			}
			
	
		}
		
	die();	
	}
	public function update_averages(){
		global $wpdb;
	$r = $wpdb->get_results("SELECT * FROM  ".$wpdb->prefix . "dtc_players_devy order by adp asc", ARRAY_A);

		$dtc = array();
		$dtc = get_option('dtc_array');
		if($r){
			$dtc_count = 0;
			for ($i = 0; $i < count($r); $i++) {
						
						$update['adp_dtc'] = $dtc[$dtc_count];
						
						$update['average'] = dtc_get_average(array($r[$i]['yse'],$r[$i]['nyh'],$r[$i]['nrm']));
						$where['id'] = $r[$i]['id'];
						$wpdb->update("".$wpdb->prefix . "dtc_players_devy",$update,$where);
						$dtc_count++;	
			}
		}
		
		
		unset($r);
		unset($where);
		unset($update);
		
		$r = $wpdb->get_results("SELECT * FROM  ".$wpdb->prefix . "dtc_players_devy order by average desc", ARRAY_A);

	
		if($r){
			$dtc_count = 0;
			for ($i = 0; $i < count($r); $i++) {
						
						$update['adp_dtc'] = $dtc[$dtc_count];
						
						$update['average'] = dtc_get_average(array($r[$i]['yse'],$r[$i]['nyh'],$r[$i]['nrm']));
						$where['id'] = $r[$i]['id'];
						$wpdb->update("".$wpdb->prefix . "dtc_players_devy",$update,$where);
						$dtc_count++;	
			}
		}
		
		
		
		
		
		
	}	
		public function update_player_rank($player_id,$rank,$old_rank,$sub= false){
			global $wpdb;
			if($sub == true){
			$update['sub_rank'] = $rank;
				
				if($old_rank == 0){
				$update['sub_rank_trend'] = 0;	
				}else{
					if($old_rank <$rank){	
					$trend = $rank - $old_rank;
					$trend = -1 * abs($trend);										
					if($trend != 0){	
					$update['sub_rank_trend'] = $trend;		
					}
					}elseif($old_rank >$rank){
					if($trend != 0){	
					$trend = $old_rank - $rank;;	
					}
					$update['sub_rank_trend'] = $trend;		
					}elseif($old_rank == $rank){
					#$update['sub_rank_trend'] = 0;	
					}
				
				}
			
			}else{
			$update['rank'] = $rank;
			
				if($old_rank == 0){
				$update['rank_trend'] = 0;	
				}else{
					if($old_rank <$rank){	
					$trend = $rank - $old_rank;										
					$trend = -1 * abs($trend);
					if($trend != 0){
					$update['rank_trend'] = $trend;	
					}
					}elseif($old_rank >$rank){
					$trend = $old_rank - $rank;;	
					if($trend != 0){	
					$update['rank_trend'] = $trend;		
					}
					}elseif($old_rank == $rank){
					#$update['rank_trend'] = 0;	
					}
				
				}
				
			}
			$where['id'] = $player_id;
			$wpdb->update("".$wpdb->prefix . "dtc_players_devy", $update,$where);	
		}
		



	public function update_ranks(){
		global $wpdb;

		$r = $wpdb->get_results("SELECT * FROM  ".$wpdb->prefix . "dtc_players_devy order by average desc", ARRAY_A);
	
		if($r){
			$dtc_count = 0;
			$rank = 1;
			for ($i = 0; $i < count($r); $i++) {

				$this->update_player_rank($r[$i]['id'],$rank,$r[$i]['rank'],false);
				$rank++;
			}
		}
		unset($rank);
		unset($r);
		
		
		$positions = array("QB","RB","WR","TE");
		
		foreach($positions as $position){
			
			$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_players_devy where position = %s order by average desc", $position), ARRAY_A);	
			if($r){
			$dtc_count = 0;
			$rank = 1;
			for ($i = 0; $i < count($r); $i++) {

				$this->update_player_rank($r[$i]['id'],$rank,$r[$i]['sub_rank'],true);
				$rank++;
			}
		}
				unset($rank);
				unset($r);
		}
		
		
	}
		
		
	
	function ajax_save_table(){
		global $wpdb;	
	 ini_set('memory_limit', '1024M');
	 print_r($_POST['update_stats'] );
	if(count($_POST['update_stats'])>0){
			foreach($_POST['update_stats'] as $player_id=>$stats){
				
					$update['yse'] = $stats['yse'];
					$update['nyh'] = $stats['nyh'];
					$update['nrm'] = $stats['nrm'];
					$update['tier'] = $stats['tier'];
					$update['qbtier'] = $stats['qbtier'];
					$update['birthdate'] = $stats['birthdate'];
					$update['name'] = $stats['name'];
					$update['team'] = $stats['team'];
					$update['position'] = $stats['position'];
					$update['nonppr'] =  ($stats['nonppr'] == '1' ? '1' : '0');
				$update['rbppc'] =  ($stats['rbppc'] == '1' ? '1' : '0');
					
					if(is_array($stats['badges'])){
					$update['badge_id'] = serialize($stats['badges']);
					}
					$where['id'] = $player_id;
					
					
					
				
					
					$wpdb->update("".$wpdb->prefix . "dtc_players_devy",$update,$where);
					
					
    if($wpdb->last_error !== '') :

        $str   = htmlspecialchars( $wpdb->last_result, ENT_QUOTES );
        $query = htmlspecialchars( $wpdb->last_query, ENT_QUOTES );

      echo "<div id='error'>
        <p class='wpdberror'><strong>WordPress database error:</strong> [$str]<br />
        <code>$query</code></p>
        </div>";

    endif;
					
					unset($update);unset($where);
			}
	}
	$this->update_averages();
	$this->update_ranks();
	die();
		
	}
	function ajax_check_lock(){
	global $wpdb;	

	if($_POST['id'] != ''){
		echo $wpdb->prepare("DELETE FROM ".$wpdb->prefix . "dtc_players_devy WHERE id =%d",$_POST['id']);
	$wpdb->query($wpdb->prepare("DELETE FROM ".$wpdb->prefix . "dtc_players_devy WHERE id =%d",$_POST['id']));
	$this->update_averages();
	
	}
	die();	
	}
	function ajax_delete_player(){
	global $wpdb;	

	if($_POST['id'] != ''){
		echo $wpdb->prepare("DELETE FROM ".$wpdb->prefix . "dtc_players_devy WHERE id =%d",$_POST['id']);
	$wpdb->query($wpdb->prepare("DELETE FROM ".$wpdb->prefix . "dtc_players_devy WHERE id =%d",$_POST['id']));
	$this->update_averages();
	
	}
	die();	
	}
	function ajax_edit_stats(){
	global $wpdb;	
	
	
	$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  ".$wpdb->prefix . "dtc_players_devy where id = %d",$_POST['id']), ARRAY_A);		
	
	
	$update['yse'] = $_POST['yse'];
	$update['nyh'] = $_POST['nyh'];
	$update['nrm'] = $_POST['nrm'];
	$update['average'] = dtc_get_average(array($update['yse'],$update['nyh'],$update['nrm']));
	$where['id'] = $_POST['id'];
	$wpdb->update("".$wpdb->prefix . "dtc_players_devy",$update,$where);
	die();	
	}
	function ajax_get_table(){
		 $this->get_table();
		 die();
	}
	
	function get_tier_dropdown($tier_id,$id){
		
		$tiers = array(1,2,3);
		$tier = '<select name="update_stats['.$id.'][tier]"><option value="0">Tier</option>';
		
				foreach($tiers as $tierd){
					
					if($tier_id == $tierd){
					$selected = 'selected="selected"';	
					}else{
					$selected = '';	
					}
				$tier.= '<option value="'.$tierd.'" '.$selected.'>Tier '.$tierd.'</option>';	
					
				}
		$tier .='</select>';
	return $tier;	
	}
	function get_qb_tier_dropdown($tier_id,$id){
		
		$tiers = array(1,2,3,4,5);
		$tier = '<select name="update_stats['.$id.'][qbtier]"><option value="0">QB Tier</option>';
		
				foreach($tiers as $tierd){
					
					if($tier_id == $tierd){
					$selected = 'selected="selected"';	
					}else{
					$selected = '';	
					}
				$tier.= '<option value="'.$tierd.'" '.$selected.'>QB Tier '.$tierd.'</option>';	
					
				}
		$tier .='</select>';
	return $tier;	
	}
	public function get_table(){
		global $wpdb;

		$where = '';
		if($_POST['action'] == 'dtc_get_table'){
			
		#print_r($_POST);
		$sort = 'ORDER by '.$_POST['dtc_sort'].' '.$_POST['dtc_sort_type'].'';	
		if($_POST['dtc_filter'] != ''){
			if($_POST['dtc_filter'] != 'ALL'){
			$where = ' AND position = "'.$_POST['dtc_filter'] .'"';	
			}
		}
		
			
			if($_POST['dtc_sort_type'] == 'desc'){
			$sort_type = 'asc';	
			}else{
			$sort_type = 'desc';		
			}
			
			if($_POST['dtc_sort'] == ''){
			$sort = 'ORDER by average desc';	
			}
		}else{
		
		$sort = 'ORDER by average desc';	
			
		}
		
		if($_POST['dtc_filter'] != ''){
			
		$selected_filter = $_POST['dtc_filter'];	
		}else{
		$selected_filter = 'ALL';	
		}
		
		if($_POST['dtc_sort'] != ''){
		$selected_sort = $_POST['dtc_sort'];
		}else{
		$selected_sort = 'average';	
		}
		
		if($_POST['dtc_sort_type'] != ''){
		$selected_sort_type = $_POST['dtc_sort_type'];
		}else{
		$selected_sort_type = 'desc';	
		}
		
		
		
		  $query = "SELECT
					  												*
					  												FROM  ".$wpdb->prefix . "dtc_players_devy
	 															
	 																WHERE id !=0 
																	".$where."
																	".$sort."
																	limit 1000";
										#echo $query;							
														
		  $players = $wpdb->get_results($query, ARRAY_A);		
	 
	 	 echo '

		 <div class="dtc-filter-menu">
	 		<p><strong>Filter</strong></p>
			<a href="#" class="dtc-devy-filter-position" data-id="ALL" data-sort="'.$selected_sort.'" data-sort-type="'.$selected_sort_type .'">Show All</a>
			<a href="#" class="dtc-devy-filter-position" data-id="QB" data-sort="'.$selected_sort.'" data-sort-type="'.$selected_sort_type .'">QB</a>
			<a href="#" class="dtc-devy-filter-position" data-id="RB" data-sort="'.$selected_sort.'" data-sort-type="'.$selected_sort_type .'">RB</a>
			<a href="#" class="dtc-devy-filter-position" data-id="WR" data-sort="'.$selected_sort.'" data-sort-type="'.$selected_sort_type .'">WR</a>
			<a href="#" class="dtc-devy-filter-position" data-id="TE" data-sort="'.$selected_sort.'" data-sort-type="'.$selected_sort_type .'">TE</a>		
			
			<div style="clear:both"></div>
			</div>
	 ';
	 
	 
	 echo '
	  		<div class="mobile-table-sort">
			<p><strong>Sort</strong></p>
			
			<a href="#" class="dtc-devy-sort-item" data-id="average" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">Rank</a>
			<a href="#" class="dtc-devy-sort-item" data-id="name" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">Name</a>
			<a href="#" class="dtc-devy-sort-item" data-id="position" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">Position</a>
			<a href="#" class="dtc-devy-sort-item" data-id="adp_dtc" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">ADP</a>
			<a href="#" class="dtc-devy-sort-item" data-id="yse" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">YSE</a>
			<a href="#" class="dtc-devy-sort-item" data-id="nyh" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">NYH</a>
			<a href="#" class="dtc-devy-sort-item" data-id="nrm" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">NRM</a>
			<a href="#" class="dtc-devy-sort-item" data-id="average" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">AVG</a>
			<div style="clear:both"></div>
			</div>';
			
			
			
	  echo ' <form class="dtc-data-form">
	  		<input type="hidden" name="action" value="dtc_devy_save_table">
			 <table class="wp-list-table widefat fixed posts" cellspacing="0" id="dtc-main-table">
	  		<thead>
			<tr class="dtc-table-header" cope="col">
			<th style="display:none" cope="col">ID</th>
		
			<th cope="col"  style="width:50px"><a href="#" class="dtc-devy-sort-item" data-id="average" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">Rank</a></th>
			<th cope="col" style="width:50px" >Non-PPR</th>
			<th cope="col"  style="width:50px" >RB PPC</th>
			<th cope="col"  style="width:50px" >Tier</th>
			<th cope="col" ><a href="#" class="dtc-devy-sort-item" data-id="name" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">Name</a></th>
			
			<th cope="col" style="width:75px"><a href="#" class="dtc-devy-sort-item" data-id="position" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">Position</a></th>
			<th cope="col" style="width:75px"><a href="#" class="dtc-devy-sort-item" data-id="team" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">Team</a></th>
			<th cope="col" >Birthdate</th>
			
			<th cope="col" style="width:75px"><a href="#" class="dtc-devy-sort-item" data-id="yse" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">YSE</a></th>
			<th cope="col" style="width:75px"><a href="#" class="dtc-devy-sort-item" data-id="nyh" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">NYH</a></th>
			<th cope="col" style="width:75px"><a href="#" class="dtc-devy-sort-item" data-id="nrm" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">NRM</a></th>
			<th cope="col" style="width:75px"><a href="#" class="dtc-devy-sort-item" data-id="average" data-sort="'.$sort_type.'" data-filter="'.$selected_filter.'">AVG</a></th>
			<th></th>
			</tr>
			</thead>
	  ';
	  $offset = 0;
	  
	  if($players){
		  	$rank = 1;
			 for ($i = 0; $i < count($players); $i++) {
				 $checked =  ($players[$i]['nonppr']== '1' ? 'checked="checked"' : '');
				 $rbchecked =  ($players[$i]['rbppc']== '1' ? 'checked="checked"' : '');
				
				
				if($players[$i]['birthdate'] == '0000-00-00'){
				$date = '';	
				}else{
				$date = $players[$i]['birthdate'];	
				}
				
				$player_rank = $offset + $rank;
				
				 echo '<tr class="row-'.$players[$i]['position'].'">
				 	   <td style="display:none">'. $players[$i]['id'].'<input type="hidden" name="update_stats['. $players[$i]['id'].'][name]" value="'.$players[$i]['name'].'"></td>
					   <td  data-label="Rank">'. esc_html( $player_rank )  /* . $players[$i]['rank'] */ .' '.dtc_show_rank($players[$i]['rank_trend']).'</td>
					   <td  data-label="NonPPR"><input type="checkbox" value="1" name="update_stats['. $players[$i]['id'].'][nonppr]" '. $checked.'></td>
					       <td  data-label="RBPPC">';
						if($players[$i]['position'] == 'RB'){
						echo '<input type="checkbox" value="1" name="update_stats['. $players[$i]['id'].'][rbppc]" '.  $rbchecked.'>';	
						}
						echo'</td>
				 		<td  data-label="NonPPR">'.$this->get_tier_dropdown( $players[$i]['tier'], $players[$i]['id']).'';
						if($players[$i]['position'] == 'QB'){
						echo '<br>'.$this->get_qb_tier_dropdown( $players[$i]['qbtier'], $players[$i]['id']).'';
						}
						echo'</td>';
						
						if($players[$i]['badge_id'] == ''){
						$badges  = array();	
						}else{
						$badges = unserialize($players[$i]['badge_id']);	
						}
						
						echo'</td>
						
						<td  data-label="Name" style=";text-align:left;" ><input style="max-width:none;width:200px !important;margin-top:1px" type="text" name="update_stats['. $players[$i]['id'].'][name]" value="'.stripslashes($players[$i]['name']).'"> <a href="#" data-id="'. $players[$i]['id'].'" class="update-player-photo button" data-type="devy" >Edit Photo</a>
						'.dtc_admin_table::get_badge_dropdown($badges, $players[$i]['id']).'

						</td>';
			
			
						echo '<td  data-label="Position" style="max-width:50px"><span class="position-'.$players[$i]['position'].'"><input  style="width:50px"    type="text" name="update_stats['. $players[$i]['id'].'][position]" value="'.$players[$i]['position'].'"></span></td>
						<td  data-label="Team"  style="max-width:50px" ><span class="position-'.$players[$i]['team'].'"><input  style="width:50px" type="text" name="update_stats['. $players[$i]['id'].'][team]" value="'.$players[$i]['team'].'"></span></td>
						<td  data-label="Birthdate"><input class="dtc_datepicker" type="text" name="update_stats['. $players[$i]['id'].'][birthdate]" value="'.$date .'"></td>
						
						<td  data-label="YSE"><input type="number" name="update_stats['. $players[$i]['id'].'][yse]" value="'.$players[$i]['yse'].'"></td>
						<td  data-label="NYH"><input type="number" name="update_stats['. $players[$i]['id'].'][nyh]" value="'.$players[$i]['nyh'].'"></td>
						<td  data-label="NRM"><input type="number" name="update_stats['. $players[$i]['id'].'][nrm]" value="'.$players[$i]['nrm'].'"></td>
						<td  data-label="AVG">'.$players[$i]['average'].'</td>
						<td><a href="#" class="dtc-devy-delete-player" data-id="'.$players[$i]['id'].'" class="button"><span class="dashicons dashicons-dismiss"></span></a></td>
					  </tr>';
					  $rank += 1;
				 
			 }
	  }
	 echo '</table></form>';
	 
	 ?>
     

     <script type="text/javascript">
	
jQuery(document).ready(function($) {
	

	      /*
	$('#dtc-main-table').Tabledit({
    url:ajaxurl,
	 restoreButton: false,
	  editButton: false,
  
    hideIdentifier: true,
     onSuccess: function(data, textStatus, jqXHR) {
       
	   $.post(ajaxurl, {'action':'dtc_get_table'}, function(response) {
			$(".dtc-player-table").html(response);
		});
	   
    },
   
   buttons: {
    edit: {
        class: 'btn btn-sm btn-default',
        html: '<span class="dashicons dashicons-welcome-write-blog dtc-check-lock"></span>',
        action: 'dtc_edit_stats'
    },
    delete: {
        class: 'btn btn-sm btn-default',
        html: '<span class="dashicons dashicons-trash"></span>',
        action: 'dtc_confirm_player'
    },
	 confirm: {
        class: 'btn btn-sm btn-default',
        html: 'Confirm Delete',
        action: 'dtc_confirm_player'
    },
    save: {
        class: 'button',
        html: 'Save'
    },
  
},
    columns: {
        identifier: [0, 'id'],
        editable: [ [5, 'yse'], [6, 'nyh'], [7, 'nrm']]
    }
});
*/
});


	 </script>
     
     <?php
	
		
	}
	public function import_players(){




		if($_POST['dtc-import-players'] != ''){
		global $wpdb;
		$table = $_POST['table'];
		require_once(''.DTC_DIR.'classes/PHPExcel.php');
		$worksheets = array();
		$filename = $_FILES['players']['tmp_name'];
		$type = PHPExcel_IOFactory::identify($filename);
		$objReader = PHPExcel_IOFactory::createReader($type);
		$objPHPExcel = $objReader->load($filename);
		$dtc = get_option('dtc_array');
		foreach ($objPHPExcel->getWorksheetIterator() as $worksheet) {
			$worksheets[$worksheet->getTitle()] = $worksheet->toArray();
		}
		
	
			if(count($worksheets)>0){
				
				$inserted= 0;
				$updated = 0;
				foreach($worksheets as $r){
							
							if(count($r)>0){
								$dtc_count = 0;
								 for ($i = 0; $i < count($r); $i++) {
									
										$headers = $r[0];
										
										foreach($headers as $key=>$header){
										$insert[$header] = 	$r[$i][$key];
										}
										
										if($insert['name'] != 'name' && $insert['dtc'] != 'dtc'){
											
											if($table == "".$wpdb->prefix . "dtc_players_devy"){
											$insert['adp_dtc'] = $dtc[$dtc_count];	
											}
											$dtc_count++;
										
											if(dtc_player_exists($insert['name'],$insert['position'],$table) == false){
											$wpdb->insert($table, $insert);
											$inserted++;
											}else{
											$where['name'] = $insert['name'];
											$where['position'] = $insert['position'];
											$wpdb->update($table, $insert,$where);	
											$updated++;
											}
											unset($where);
											unset($insert);
										}
								
								 }
							}
					
				}
				
			}
			$this->update_averages();
			$this->update_ranks();
		
			echo '   <div class="notice notice-success is-dismissible">
        <p>'.__( 'Inserted '.$inserted.' Players and Updated '.$updated.' Players', 'dtc' ).'</p>
    </div>';
		}
		
		
		if($_POST['dtc-import-dtc'] != ''){
			
			
		$dtc = $_POST['dtc_data'];
		$dtc_array = array();
		if($dtc){
		
		$dtc_array = explode(PHP_EOL, $dtc );
		
		}
		update_option('dtc_array',array_filter($dtc_array));
		}
		
		
	}
	
	public function view(){
		global $wpdb;
		$this->import_players();

		add_thickbox();
	 
		if($_POST['dtc-save-image'] != ''){
			if($_FILES["upload-photo"]["name"] != ''){
       			$upload_path = wp_upload_dir();
				move_uploaded_file($_FILES["upload-photo"]["tmp_name"], $upload_path['basedir'].'/headshots_new/players/'.$_POST['update-player-photo-field-id'].'.png');
            	echo '<div class="dtc-success">Updated Photo for '.$_POST['update-player-photo-field-id'].'</div>';
			}
		}

		$dtc = array();
		$dtc = get_option('dtc_array');
		$dtc_data = implode("\n",$dtc);

		echo '	
	 	<style type="text/css">

	 	</style>

		<div id="dtc-import-players" style="display:none;">
			<h2>Import Players</h2>
			<form action="" method="post" enctype="multipart/form-data">
				<input type="hidden" name="table" value="'.$wpdb->prefix . 'dtc_players">
				Upload Players Excel Document <input type="file" name="players"> <input name="dtc-import-players" type="submit" value="Import">
			</form>
		</div>

		<div id="dtc-import-adp" style="display:none;">
     		<h2>Import ADP</h2>
	 		
			<form action="" method="post" enctype="multipart/form-data">
				<input type="hidden" name="table" value="'.$wpdb->prefix . 'dtc_players">
	 			Upload Players Excel Document <input type="file" name="players"> <input name="dtc-import-players" type="submit" value="Import">
	 		</form>
		</div>

	  	<div id="dtc-edit-dtc" style="display:none;">
     		<h2>Modify DTC</h2>
	 		
			<form action="" method="post" enctype="multipart/form-data">
	 			<textarea name="dtc_data" value="" style="width:100%;height:500px" class="dtc-editor">'.$dtc_data.'</textarea><br>
	 			<input name="dtc-import-dtc" type="submit" value="Save">
			</form>
		</div>

		<div id="dtc-add-players" style="display:none;">
     		<h2>Add Players</h2>
	 		
			<form action="" method="post" enctype="multipart/form-data" class="dtc-devy-add-players-form">
	 			<input type="hidden" name="action" value="dtc_devy_add_players">
	 			
				<table class="wp-list-table widefat fixed posts" cellspacing="0" id="dtc-main-table">
	 				<thead>
						<tr>
							<th>Name</th>
							<th>Position</th>
							<th>Team</th>
							<th>Birth Date</th>	  
							<th>ADP</th>
							<th>YSE</th>
							<th>NYH</th>
							<th>NRM</th>
						</tr>
					</thead>
					<tbody>
		';
	
		for ($i = 1; $i <= 10; $i++) {
			echo '
						<tr>
							<th>
								<input type="text" name="player['.$i.'][name]" value="">
							</th>
							
							<th>
								<input type="text" name="player['.$i.'][position]" value="">
							</th>
							
							<th>
								<input type="text" name="player['.$i.'][team]" value="">
							</th>
							
							<th>
								<input type="text" name="player['.$i.'][birthdate]" value="" class="dtc_datepicker">
							</th>
							
							<th>
								<input type="text" name="player['.$i.'][adp]" value="">
							</th>
							
							<th>
								<input type="text" name="player['.$i.'][yse]" value="">
							</th>
							
							<th>
								<input type="text" name="player['.$i.'][nyh]" value="">
							</th>
							
							<th>
								<input type="text" name="player['.$i.'][nrm]" value="">
							</th>
						</tr>
			';	
		}
	 
	 	echo '
					</tbody>
	 			</table>
	 			
				<input name="dtc-import-dtc" type="submit" value="Save">
			</form>
		</div>

		<div id="update-player-photo-modal" style="display:none;">
			<div class="update-player-photo-modal-inner">
				<form class="update-player-photo-form" enctype="multipart/form-data" method="post" action="">
					<input type="hidden" class="update-player-photo-field-id" name="update-player-photo-field-id" value="">
					
					<div class="update-player-photo-form-image">
					</div>
					
					<input type="hidden" name="update-player-photo-field"  class="update-player-photo-field">
					<p><input type="file" name="upload-photo"> <input type="submit" name="dtc-save-image" value="Upload Photo"></p>
				</form>

				<form class="update-player-photo-xy">
					<input type="hidden" name="action" value="dtc_update_player_axis">
					<input type="hidden" name="type" class="update-player-photo-field-type" >
					<input type="hidden" class="update-player-photo-field-id" name="player_id" value="">
					
					<p>Modify the position below</p>
					<strong>X:</strong> <input type="text" name="image_x" value="" class="update-player-photo-x" style="width:60px"> 
					<strong>Y:</strong><input type="text" name="image_y" value="" class="update-player-photo-y" style="width:60px"> 
					<strong>Scale:</strong> <input type="text" name="image_scale" value="" class="update-player-photo-scale" style="width:60px">% 
					<strong>Rotation:</strong><input type="text" name="image_rotation" value="" class="update-player-photo-rotation" style="width:60px">Deg
					<input type="submit" name="save_image_axis" value="Save Axis">
				</form>
			</div>
		</div>

		<div class="dtc-admin-wrapper">';
	 		dtc_admin_header('Calculator Values');
			
			echo '
			<div class="dtc-top-menu">
	 			<a href="#" class="dtc-devy-save-table" style="background-color:#f2a535">Save</a>
				<a  href="#TB_inline?width=600&height=650&inlineId=dtc-add-players" class="thickbox">Add Players</a> 
				<div style="clear:both"></div>
			</div>
	 		';

			echo '
			<div class="dtc-player-table">
			';
				$this->get_table();
			echo '
			</div>
			';

	   	echo '
	   	</div>
	   	';
	}
}