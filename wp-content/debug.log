[06-Jun-2025 13:12:32 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:32 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:32 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:32 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rocket</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '1876', `object_type` = 'post', `object_sub_type` = 'avada_faq', `permalink` = 'http://fantasytradecalculator.test/faq-items/class-aptent-taciti-sociosqu-ad-litora-torquent-per-conubia-nostra-pers/', `primary_focus_keyword_score` = NULL, `readability_score` = '0', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = NULL, `open_graph_image_id` = NULL, `open_graph_image_source` = NULL, `open_graph_image_meta` = NULL, `twitter_image` = NULL, `twitter_image_id` = NULL, `twitter_image_source` = NULL, `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Class aptent taciti sociosqu ad litora torquent per conubia nostra pers.', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2012-11-27 14:42:55', `object_published_at` = '2012-11-27 14:42:55', `version` = '2', `permalink_hash` = '117:b521d05641d0721fb1f29b7059f92b49', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '627' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Indexable_Post_Indexation_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '1877', `object_type` = 'post', `object_sub_type` = 'avada_faq', `permalink` = 'http://fantasytradecalculator.test/faq-items/fusce-nisi-augue-malesuada-in-commodo-quis-euismod-quis-orci-integer-vitae-nisl-non/', `primary_focus_keyword_score` = NULL, `readability_score` = '0', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = 'https://avada.theme-fusion.com/agency/wp-content/uploads/sites/12/2012/07/blog_5-300x171.jpg', `open_graph_image_id` = NULL, `open_graph_image_source` = 'first-content-image', `open_graph_image_meta` = NULL, `twitter_image` = 'https://avada.theme-fusion.com/agency/wp-content/uploads/sites/12/2012/07/blog_5-300x171.jpg', `twitter_image_id` = NULL, `twitter_image_source` = 'first-content-image', `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Fusce nisi augue, malesuada in commodo quis, euismod quis orci integer vitae nisl non.', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2012-11-27 14:43:24', `object_published_at` = '2012-11-27 14:43:24', `version` = '2', `permalink_hash` = '129:c695d5fbfb01aaefa84bde5f6b3c29e0', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '327' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Indexable_Post_Indexation_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '1878', `object_type` = 'post', `object_sub_type` = 'avada_faq', `permalink` = 'http://fantasytradecalculator.test/faq-items/integer-vitae-nisl-non-augue-ullamcorper-blandit-donec-vitae-nibh-ipsums/', `primary_focus_keyword_score` = NULL, `readability_score` = '0', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = NULL, `open_graph_image_id` = NULL, `open_graph_image_source` = NULL, `open_graph_image_meta` = NULL, `twitter_image` = NULL, `twitter_image_id` = NULL, `twitter_image_source` = NULL, `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Integer vitae nisl non augue ullamcorper blandit donec vitae nibh ipsums.', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2012-11-27 14:43:43', `object_published_at` = '2012-11-27 14:43:43', `version` = '2', `permalink_hash` = '118:681ede29ab9a47020a05c6c1a68679ab', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '720' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Indexable_Post_Indexation_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '1879', `object_type` = 'post', `object_sub_type` = 'avada_faq', `permalink` = 'http://fantasytradecalculator.test/faq-items/vivamus-ullamcorper-nim-sit-amet-consequat-laoreet-tortor-tortor-dictum-egestas-urna/', `primary_focus_keyword_score` = NULL, `readability_score` = '0', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = 'https://avada.theme-fusion.com/agency/wp-content/uploads/sites/12/2012/07/blog_5-300x171.jpg', `open_graph_image_id` = NULL, `open_graph_image_source` = 'first-content-image', `open_graph_image_meta` = NULL, `twitter_image` = 'https://avada.theme-fusion.com/agency/wp-content/uploads/sites/12/2012/07/blog_5-300x171.jpg', `twitter_image_id` = NULL, `twitter_image_source` = 'first-content-image', `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Vivamus ullamcorper nim sit amet consequat laoreet tortor tortor dictum egestas urna.', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2012-11-27 14:44:05', `object_published_at` = '2012-11-27 14:44:05', `version` = '2', `permalink_hash` = '130:386a4c51de855d388994513b287bb6b1', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '2376' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Indexable_Post_Indexation_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '1880', `object_type` = 'post', `object_sub_type` = 'avada_faq', `permalink` = 'http://fantasytradecalculator.test/faq-items/fusce-nisi-malesuada-in-commodo-quis-euismod-quis-orci-onteger-vitae-nisl-non-augue-ullamcorpers/', `primary_focus_keyword_score` = NULL, `readability_score` = '0', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = NULL, `open_graph_image_id` = NULL, `open_graph_image_source` = NULL, `open_graph_image_meta` = NULL, `twitter_image` = NULL, `twitter_image_id` = NULL, `twitter_image_source` = NULL, `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Fusce nisi malesuada in commodo quis, euismod quis orci on augue ullamcorpers.', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2012-11-27 14:44:31', `object_published_at` = '2012-11-27 14:44:31', `version` = '2', `permalink_hash` = '142:3e72d1bbca963c1dd78735606cc5e666', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '897' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Indexable_Post_Indexation_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '1881', `object_type` = 'post', `object_sub_type` = 'avada_faq', `permalink` = 'http://fantasytradecalculator.test/faq-items/curabitur-eget-leo-at-velit-imperdiet-varius-eu-ipsum-vitae-velit-congue-iaculis-vitaes/', `primary_focus_keyword_score` = NULL, `readability_score` = '0', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = 'https://avada.theme-fusion.com/agency/wp-content/uploads/sites/12/2012/07/blog_5-300x171.jpg', `open_graph_image_id` = NULL, `open_graph_image_source` = 'first-content-image', `open_graph_image_meta` = NULL, `twitter_image` = 'https://avada.theme-fusion.com/agency/wp-content/uploads/sites/12/2012/07/blog_5-300x171.jpg', `twitter_image_id` = NULL, `twitter_image_source` = 'first-content-image', `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Curabitur eget leo at velit imperdiet varius eu ipsum vitae velit congue iaculis vitaes.', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2012-11-27 14:44:54', `object_published_at` = '2012-11-27 14:44:54', `version` = '2', `permalink_hash` = '133:c66f036aa34f362ccbbfb61a69db0155', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '641' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Indexable_Post_Indexation_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '20523', `object_type` = 'post', `object_sub_type` = 'avada_portfolio', `permalink` = 'http://fantasytradecalculator.test/?post_type=avada_portfolio&p=20523', `primary_focus_keyword_score` = NULL, `readability_score` = '30', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = NULL, `open_graph_image_id` = NULL, `open_graph_image_source` = NULL, `open_graph_image_meta` = NULL, `twitter_image` = NULL, `twitter_image_id` = NULL, `twitter_image_source` = NULL, `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Dynasty Rankings &#8211; July', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'draft', `is_protected` = '0', `is_public` = '0', `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2018-08-02 03:50:18', `object_published_at` = '0000-00-00 00:00:00', `version` = '2', `permalink_hash` = '69:e21e3c36b8fcac21ac6a54429a9572a3', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '3295' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Indexable_Post_Indexation_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '192', `object_type` = 'post', `object_sub_type` = 'avada_portfolio', `permalink` = 'http://fantasytradecalculator.test/portfolio-items/suspendisse-pharetra-urna/', `primary_focus_keyword_score` = NULL, `readability_score` = '0', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = 'http://fantasytradecalculator.test/wp-content/uploads/2012/07/portfolio_5.jpg', `open_graph_image_id` = '3895', `open_graph_image_source` = 'featured-image', `open_graph_image_meta` = '{\"width\":940,\"height\":600,\"url\":\"http://fantasytradecalculator.test/wp-content/uploads/2012/07/portfolio_5.jpg\",\"path\":\"/Users/<USER>/Sites/fantasytradecalculator/wp-content/uploads/2012/07/portfolio_5.jpg\",\"size\":\"full\",\"id\":3895,\"alt\":\"\",\"pixels\":564000,\"type\":\"image/jpeg\"}', `twitter_image` = 'http://fantasytradecalculator.test/wp-content/uploads/2012/07/portfolio_5.jpg', `twitter_image_id` = '3895', `twitter_image_source` = 'featured-image', `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Suspende Phara Urna', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2012-07-31 16:43:55', `object_published_at` = '2012-07-31 16:43:55', `version` = '2', `permalink_hash` = '77:0d88be3319238340267a73d24947b0dd', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '642' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Indexable_Post_Indexation_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '193', `object_type` = 'post', `object_sub_type` = 'avada_portfolio', `permalink` = 'http://fantasytradecalculator.test/portfolio-items/curabitur-malesuada-lorem/', `primary_focus_keyword_score` = NULL, `readability_score` = '0', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = 'http://fantasytradecalculator.test/wp-content/uploads/2012/07/portfolio_4.jpg', `open_graph_image_id` = '3894', `open_graph_image_source` = 'featured-image', `open_graph_image_meta` = '{\"width\":940,\"height\":600,\"url\":\"http://fantasytradecalculator.test/wp-content/uploads/2012/07/portfolio_4.jpg\",\"path\":\"/Users/<USER>/Sites/fantasytradecalculator/wp-content/uploads/2012/07/portfolio_4.jpg\",\"size\":\"full\",\"id\":3894,\"alt\":\"\",\"pixels\":564000,\"type\":\"image/jpeg\"}', `twitter_image` = 'http://fantasytradecalculator.test/wp-content/uploads/2012/07/portfolio_4.jpg', `twitter_image_id` = '3894', `twitter_image_source` = 'featured-image', `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Curabitur Malada Lorem', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2012-07-31 16:44:55', `object_published_at` = '2012-07-31 16:44:55', `version` = '2', `permalink_hash` = '77:63f69da6f20ee25734ac58fab849f90f', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '238' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Indexable_Post_Indexation_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '196', `object_type` = 'post', `object_sub_type` = 'avada_portfolio', `permalink` = 'http://fantasytradecalculator.test/portfolio-items/nam-viverra-euismod/', `primary_focus_keyword_score` = NULL, `readability_score` = '0', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = 'http://fantasytradecalculator.test/wp-content/uploads/2012/07/portfolio_3.jpg', `open_graph_image_id` = '3893', `open_graph_image_source` = 'featured-image', `open_graph_image_meta` = '{\"width\":940,\"height\":600,\"url\":\"http://fantasytradecalculator.test/wp-content/uploads/2012/07/portfolio_3.jpg\",\"path\":\"/Users/<USER>/Sites/fantasytradecalculator/wp-content/uploads/2012/07/portfolio_3.jpg\",\"size\":\"full\",\"id\":3893,\"alt\":\"\",\"pixels\":564000,\"type\":\"image/jpeg\"}', `twitter_image` = 'http://fantasytradecalculator.test/wp-content/uploads/2012/07/portfolio_3.jpg', `twitter_image_id` = '3893', `twitter_image_source` = 'featured-image', `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Nam Viverra Euismod', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2012-07-31 16:53:09', `object_published_at` = '2012-07-31 16:53:09', `version` = '2', `permalink_hash` = '71:908ccde2be65f127650885840f9c0f75', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '493' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Indexable_Post_Indexation_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '195', `object_type` = 'post', `object_sub_type` = 'avada_portfolio', `permalink` = 'http://fantasytradecalculator.test/portfolio-items/proin-sodales-quam/', `primary_focus_keyword_score` = NULL, `readability_score` = '0', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = 'http://fantasytradecalculator.test/wp-content/uploads/2012/07/portfolio_2.jpg', `open_graph_image_id` = '3892', `open_graph_image_source` = 'featured-image', `open_graph_image_meta` = '{\"width\":940,\"height\":600,\"url\":\"http://fantasytradecalculator.test/wp-content/uploads/2012/07/portfolio_2.jpg\",\"path\":\"/Users/<USER>/Sites/fantasytradecalculator/wp-content/uploads/2012/07/portfolio_2.jpg\",\"size\":\"full\",\"id\":3892,\"alt\":\"\",\"pixels\":564000,\"type\":\"image/jpeg\"}', `twitter_image` = 'http://fantasytradecalculator.test/wp-content/uploads/2012/07/portfolio_2.jpg', `twitter_image_id` = '3892', `twitter_image_source` = 'featured-image', `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Proin Sodales Quam', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2012-07-31 16:53:51', `object_published_at` = '2012-07-31 16:53:51', `version` = '2', `permalink_hash` = '70:20117ee747505f9677c97b8aa3920fab', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '120' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Indexable_Post_Indexation_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '191', `object_type` = 'post', `object_sub_type` = 'avada_portfolio', `permalink` = 'http://fantasytradecalculator.test/portfolio-items/mauris-fringilla-voluts/', `primary_focus_keyword_score` = NULL, `readability_score` = '0', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = 'http://fantasytradecalculator.test/wp-content/uploads/2013/10/portfolio_6.jpg', `open_graph_image_id` = '3936', `open_graph_image_source` = 'featured-image', `open_graph_image_meta` = '{\"width\":940,\"height\":600,\"url\":\"http://fantasytradecalculator.test/wp-content/uploads/2013/10/portfolio_6.jpg\",\"path\":\"/Users/<USER>/Sites/fantasytradecalculator/wp-content/uploads/2013/10/portfolio_6.jpg\",\"size\":\"full\",\"id\":3936,\"alt\":\"\",\"pixels\":564000,\"type\":\"image/jpeg\"}', `twitter_image` = 'http://fantasytradecalculator.test/wp-content/uploads/2013/10/portfolio_6.jpg', `twitter_image_id` = '3936', `twitter_image_source` = 'featured-image', `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Mauris Fringilla Voluts', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2012-07-31 16:54:02', `object_published_at` = '2012-07-31 16:54:02', `version` = '2', `permalink_hash` = '75:d72a5ca658afb1b7db5c8068f3bfc9aa', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '651' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Indexable_Post_Indexation_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '198', `object_type` = 'post', `object_sub_type` = 'avada_portfolio', `permalink` = 'http://fantasytradecalculator.test/portfolio-items/donec-ornare-turpis-eget/', `primary_focus_keyword_score` = NULL, `readability_score` = '0', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = 'http://fantasytradecalculator.test/wp-content/uploads/2012/07/portfolio_1.jpg', `open_graph_image_id` = '3891', `open_graph_image_source` = 'featured-image', `open_graph_image_meta` = '{\"width\":940,\"height\":600,\"url\":\"http://fantasytradecalculator.test/wp-content/uploads/2012/07/portfolio_1.jpg\",\"path\":\"/Users/<USER>/Sites/fantasytradecalculator/wp-content/uploads/2012/07/portfolio_1.jpg\",\"size\":\"full\",\"id\":3891,\"alt\":\"\",\"pixels\":564000,\"type\":\"image/jpeg\"}', `twitter_image` = 'http://fantasytradecalculator.test/wp-content/uploads/2012/07/portfolio_1.jpg', `twitter_image_id` = '3891', `twitter_image_source` = 'featured-image', `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Donec Ore Turis Eget', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2012-07-31 17:00:15', `object_published_at` = '2012-07-31 17:00:15', `version` = '2', `permalink_hash` = '76:f4d1c5582534aeb9de5c2ed38b76039c', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '804' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Indexable_Post_Indexation_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '18268', `object_type` = 'post', `object_sub_type` = 'page', `permalink` = 'http://fantasytradecalculator.test/?page_id=18268', `primary_focus_keyword_score` = NULL, `readability_score` = '0', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = NULL, `open_graph_image_id` = NULL, `open_graph_image_source` = NULL, `open_graph_image_meta` = NULL, `twitter_image` = NULL, `twitter_image_id` = NULL, `twitter_image_source` = NULL, `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Logout', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'draft', `is_protected` = '0', `is_public` = '0', `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2018-05-07 18:49:52', `object_published_at` = '0000-00-00 00:00:00', `version` = '2', `permalink_hash` = '49:35f479c224773b45f84c79177bf6bcf1', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '751' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Indexable_Post_Indexation_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '18479', `object_type` = 'post', `object_sub_type` = 'page', `permalink` = 'http://fantasytradecalculator.test/?page_id=18479', `primary_focus_keyword_score` = NULL, `readability_score` = '0', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = NULL, `open_graph_image_id` = NULL, `open_graph_image_source` = NULL, `open_graph_image_meta` = NULL, `twitter_image` = NULL, `twitter_image_id` = NULL, `twitter_image_source` = NULL, `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Rankings Test', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'draft', `is_protected` = '0', `is_public` = '0', `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2018-05-13 22:36:35', `object_published_at` = '0000-00-00 00:00:00', `version` = '2', `permalink_hash` = '49:d7e112a8ea99bd9da33574855c99b9f9', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '755' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Indexable_Post_Indexation_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query INSERT INTO `wp_yoast_indexable` (`object_type`, `object_id`, `object_sub_type`, `permalink`, `primary_focus_keyword_score`, `readability_score`, `inclusive_language_score`, `is_cornerstone`, `is_robots_noindex`, `is_robots_nofollow`, `is_robots_noimageindex`, `is_robots_noarchive`, `is_robots_nosnippet`, `open_graph_image`, `open_graph_image_id`, `open_graph_image_source`, `open_graph_image_meta`, `twitter_image`, `twitter_image_id`, `twitter_image_source`, `primary_focus_keyword`, `canonical`, `title`, `description`, `breadcrumb_title`, `open_graph_title`, `open_graph_description`, `twitter_title`, `twitter_description`, `estimated_reading_time_minutes`, `author_id`, `post_parent`, `number_of_pages`, `post_status`, `is_protected`, `is_public`, `has_public_posts`, `blog_id`, `schema_page_type`, `schema_article_type`, `object_last_modified`, `object_published_at`, `version`, `permalink_hash`, `created_at`, `updated_at`) VALUES ('post', '39112', 'page', 'http://fantasytradecalculator.test/draft-caddy/', NULL, '0', '0', '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Draft Caddy', NULL, NULL, NULL, NULL, '0', '4523', '0', NULL, 'publish', '0', NULL, NULL, '1', NULL, NULL, '2021-11-16 00:52:59', '2021-11-01 19:59:18', '2', '47:a73b8eefd2b0d69e418a77e1f38879f6', '2025-06-06 13:12:35', '2025-06-06 13:12:35') made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Abstract_Link_Indexing_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Builders\Indexable_Builder->build_for_id_and_type, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query INSERT INTO `wp_yoast_indexable` (`object_type`, `object_id`, `object_sub_type`, `permalink`, `primary_focus_keyword_score`, `readability_score`, `inclusive_language_score`, `is_cornerstone`, `is_robots_noindex`, `is_robots_nofollow`, `is_robots_noimageindex`, `is_robots_noarchive`, `is_robots_nosnippet`, `open_graph_image`, `open_graph_image_id`, `open_graph_image_source`, `open_graph_image_meta`, `twitter_image`, `twitter_image_id`, `twitter_image_source`, `primary_focus_keyword`, `canonical`, `title`, `description`, `breadcrumb_title`, `open_graph_title`, `open_graph_description`, `twitter_title`, `twitter_description`, `estimated_reading_time_minutes`, `author_id`, `post_parent`, `number_of_pages`, `post_status`, `is_protected`, `is_public`, `has_public_posts`, `blog_id`, `schema_page_type`, `schema_article_type`, `object_last_modified`, `object_published_at`, `version`, `permalink_hash`, `created_at`, `updated_at`) VALUES ('post', '44860', 'page', 'http://fantasytradecalculator.test/terms-of-service/', NULL, '0', '0', '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Terms of Service', NULL, NULL, NULL, NULL, NULL, '6', '0', NULL, 'publish', '0', NULL, NULL, '1', NULL, NULL, '2024-10-02 19:27:25', '2024-02-16 19:10:27', '2', '52:8c556f671d7a6d1ecddf792d35d025b4', '2025-06-06 13:12:35', '2025-06-06 13:12:35') made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Abstract_Link_Indexing_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Builders\Indexable_Builder->build_for_id_and_type, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '11922', `object_type` = 'post', `object_sub_type` = 'page', `permalink` = 'http://fantasytradecalculator.test/contact-us/', `primary_focus_keyword_score` = NULL, `readability_score` = '90', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = NULL, `open_graph_image_id` = NULL, `open_graph_image_source` = NULL, `open_graph_image_meta` = NULL, `twitter_image` = NULL, `twitter_image_id` = NULL, `twitter_image_source` = NULL, `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = NULL, `breadcrumb_title` = 'Contact Us', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2024-09-25 20:33:45', `object_published_at` = '2017-08-13 00:58:44', `version` = '2', `permalink_hash` = '46:c2a720cf56d96c22b271504146d2dbdc', `updated_at` = '2025-06-06 13:12:35' WHERE `id` = '78' made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Abstract_Link_Indexing_Action->index, Yoast\WP\SEO\Builders\Indexable_Link_Builder->build, Yoast\WP\SEO\Builders\Indexable_Link_Builder->create_links, array_map, Yoast\WP\SEO\Builders\Indexable_Link_Builder->Yoast\WP\SEO\Builders\{closure}, Yoast\WP\SEO\Builders\Indexable_Link_Builder->create_internal_link, Yoast\WP\SEO\Builders\Indexable_Link_Builder->enhance_link_from_indexable, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:35 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query INSERT INTO `wp_yoast_indexable` (`object_type`, `object_id`, `object_sub_type`, `permalink`, `primary_focus_keyword_score`, `readability_score`, `inclusive_language_score`, `is_cornerstone`, `is_robots_noindex`, `is_robots_nofollow`, `is_robots_noimageindex`, `is_robots_noarchive`, `is_robots_nosnippet`, `open_graph_image`, `open_graph_image_id`, `open_graph_image_source`, `open_graph_image_meta`, `twitter_image`, `twitter_image_id`, `twitter_image_source`, `primary_focus_keyword`, `canonical`, `title`, `description`, `breadcrumb_title`, `open_graph_title`, `open_graph_description`, `twitter_title`, `twitter_description`, `estimated_reading_time_minutes`, `author_id`, `post_parent`, `number_of_pages`, `post_status`, `is_protected`, `is_public`, `has_public_posts`, `blog_id`, `schema_page_type`, `schema_article_type`, `object_last_modified`, `object_published_at`, `version`, `permalink_hash`, `created_at`, `updated_at`) VALUES ('post', '45318', 'popup', 'http://fantasytradecalculator.test/?post_type=popup&p=45318', NULL, '0', '0', '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Example: Auto-opening announcement popup', NULL, NULL, NULL, NULL, NULL, '25056', '0', NULL, 'publish', '0', NULL, NULL, '1', NULL, NULL, '2024-05-23 17:42:51', '2024-05-23 17:42:51', '2', '59:8aa70fbd4bafe8f9cd08e27b29123c33', '2025-06-06 13:12:35', '2025-06-06 13:12:35') made by require('wp-cron.php'), do_action_ref_array('wpseo_indexable_index_batch'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Admin\Background_Indexing_Integration->index, Yoast\WP\SEO\Actions\Indexing\Abstract_Link_Indexing_Action->index, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Builders\Indexable_Builder->build_for_id_and_type, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute
[06-Jun-2025 13:12:37 UTC] PHP Fatal error:  Uncaught Error: Undefined constant "DB_USER_DTC" in /Users/<USER>/Sites/fantasytradecalculator/wp-content/plugins/fantasy-trade-calculator/admin/database-dtc.php:9
Stack trace:
#0 /Users/<USER>/Sites/fantasytradecalculator/wp-content/plugins/fantasy-trade-calculator/admin/cron-tools.php(97): Database_DTC::connection()
#1 /Users/<USER>/Sites/fantasytradecalculator/wp-includes/class-wp-hook.php(324): dtc_cron_tools->fetch_and_update_data()
#2 /Users/<USER>/Sites/fantasytradecalculator/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#3 /Users/<USER>/Sites/fantasytradecalculator/wp-includes/plugin.php(565): WP_Hook->do_action(Array)
#4 /Users/<USER>/Sites/fantasytradecalculator/wp-cron.php(191): do_action_ref_array('fetch_update_da...', Array)
#5 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>/Si...')
#6 {main}
  thrown in /Users/<USER>/Sites/fantasytradecalculator/wp-content/plugins/fantasy-trade-calculator/admin/database-dtc.php on line 9
[06-Jun-2025 13:12:57 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:57 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:57 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:12:57 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:42 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '11896', `object_type` = 'post', `object_sub_type` = 'page', `permalink` = 'http://fantasytradecalculator.test/', `primary_focus_keyword_score` = '51', `readability_score` = '60', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = NULL, `open_graph_image_id` = NULL, `open_graph_image_source` = NULL, `open_graph_image_meta` = NULL, `twitter_image` = NULL, `twitter_image_id` = NULL, `twitter_image_source` = NULL, `primary_focus_keyword` = 'Dynasty Trade Calculator', `canonical` = NULL, `title` = NULL, `description` = 'Fantasy Football Trade Calculator & Rankings | AI-Powered League-Integrated Trade Analysis. Use our suite of tools to get personalized advice, expert rankings, and make smarter decisions in your fantasy football league.', `breadcrumb_title` = 'Home', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = '8', `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2024-11-08 19:24:32', `object_published_at` = '2017-08-13 00:59:31', `version` = '2', `permalink_hash` = '35:821c0470657449b9a28298410c4a300e', `updated_at` = '2025-06-06 13:14:42' WHERE `id` = '3' made by require('index.php'), require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('/themes/Avada/page.php'), get_header, locate_template, load_template, require_once('/themes/Avada/header.php'), wp_head, do_action('wp_head'), WP_Hook->do_action, WP_Hook->apply_filters, wp_robots, apply_filters('wp_robots'), WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Front_End\WP_Robots_Integration->add_robots, Yoast\WP\SEO\Integrations\Front_End\WP_Robots_Integration->get_robots_value, Yoast\WP\SEO\Memoizers\Meta_Tags_Context_Memoizer->for_current_page, Yoast\WP\SEO\Repositories\Indexable_Repository->for_current_page, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute, QM_DB->query
[06-Jun-2025 13:14:42 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/fantasytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[06-Jun-2025 13:14:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rocket</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:42 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/fantasytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[06-Jun-2025 13:14:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:14:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:16 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '11916', `object_type` = 'post', `object_sub_type` = 'page', `permalink` = 'http://fantasytradecalculator.test/calculator/', `primary_focus_keyword_score` = NULL, `readability_score` = '30', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = NULL, `open_graph_image_id` = NULL, `open_graph_image_source` = NULL, `open_graph_image_meta` = NULL, `twitter_image` = NULL, `twitter_image_id` = NULL, `twitter_image_source` = NULL, `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = 'The Dynasty Trade Calculator is one of the most innovative tools in all of Dynasty Fantasy Football.', `breadcrumb_title` = 'Calculator', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2024-09-24 18:30:02', `object_published_at` = '2017-08-13 00:59:08', `version` = '2', `permalink_hash` = '46:d0bbec2dffd8504e3928b19acbb362e7', `updated_at` = '2025-06-06 13:15:16' WHERE `id` = '2' made by require('index.php'), require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('/themes/Avada/page.php'), get_header, locate_template, load_template, require_once('/themes/Avada/header.php'), wp_head, do_action('wp_head'), WP_Hook->do_action, WP_Hook->apply_filters, wp_robots, apply_filters('wp_robots'), WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Front_End\WP_Robots_Integration->add_robots, Yoast\WP\SEO\Integrations\Front_End\WP_Robots_Integration->get_robots_value, Yoast\WP\SEO\Memoizers\Meta_Tags_Context_Memoizer->for_current_page, Yoast\WP\SEO\Repositories\Indexable_Repository->for_current_page, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute, QM_DB->query
[06-Jun-2025 13:15:16 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '11896', `object_type` = 'post', `object_sub_type` = 'page', `permalink` = 'http://fantasytradecalculator.test/', `primary_focus_keyword_score` = '51', `readability_score` = '60', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = NULL, `open_graph_image_id` = NULL, `open_graph_image_source` = NULL, `open_graph_image_meta` = NULL, `twitter_image` = NULL, `twitter_image_id` = NULL, `twitter_image_source` = NULL, `primary_focus_keyword` = 'Dynasty Trade Calculator', `canonical` = NULL, `title` = NULL, `description` = 'Fantasy Football Trade Calculator & Rankings | AI-Powered League-Integrated Trade Analysis. Use our suite of tools to get personalized advice, expert rankings, and make smarter decisions in your fantasy football league.', `breadcrumb_title` = 'Home', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = '8', `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2024-11-08 19:24:32', `object_published_at` = '2017-08-13 00:59:31', `version` = '2', `permalink_hash` = '35:821c0470657449b9a28298410c4a300e', `updated_at` = '2025-06-06 13:15:16' WHERE `id` = '3' made by require('index.php'), require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('/themes/Avada/page.php'), get_header, locate_template, load_template, require_once('/themes/Avada/header.php'), wp_head, do_action('wp_head'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Front_End_Integration->call_wpseo_head, do_action('wpseo_head'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Front_End_Integration->present_head, Yoast\WP\SEO\Presenters\Schema_Presenter->present, Yoast\WP\SEO\Presenters\Schema_Presenter->get, Yoast\WP\SEO\Presentations\Abstract_Presentation->__get, Yoast\WP\SEO\Presentations\Indexable_Presentation->generate_schema, Yoast\WP\SEO\Generators\Schema_Generator->generate, Yoast\WP\SEO\Generators\Schema_Generator->generate_graph, Yoast\WP\SEO\Generators\Schema\Breadcrumb->generate, Yoast\WP\SEO\Presentations\Abstract_Presentation->__get, Yoast\WP\SEO\Presentations\Indexable_Presentation->generate_breadcrumbs, Yoast\WP\SEO\Generators\Breadcrumbs_Generator->generate, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute, QM_DB->query
[06-Jun-2025 13:15:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rocket</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:17 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '11916', `object_type` = 'post', `object_sub_type` = 'page', `permalink` = 'http://fantasytradecalculator.test/calculator/', `primary_focus_keyword_score` = NULL, `readability_score` = '30', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = NULL, `open_graph_image_id` = NULL, `open_graph_image_source` = NULL, `open_graph_image_meta` = NULL, `twitter_image` = NULL, `twitter_image_id` = NULL, `twitter_image_source` = NULL, `primary_focus_keyword` = NULL, `canonical` = NULL, `title` = NULL, `description` = 'The Dynasty Trade Calculator is one of the most innovative tools in all of Dynasty Fantasy Football.', `breadcrumb_title` = 'Calculator', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = NULL, `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2024-09-24 18:30:02', `object_published_at` = '2017-08-13 00:59:08', `version` = '2', `permalink_hash` = '46:d0bbec2dffd8504e3928b19acbb362e7', `updated_at` = '2025-06-06 13:15:17' WHERE `id` = '2' made by require('index.php'), require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('/themes/Avada/page.php'), get_header, locate_template, load_template, require_once('/themes/Avada/header.php'), wp_head, do_action('wp_head'), WP_Hook->do_action, WP_Hook->apply_filters, wp_robots, apply_filters('wp_robots'), WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Front_End\WP_Robots_Integration->add_robots, Yoast\WP\SEO\Integrations\Front_End\WP_Robots_Integration->get_robots_value, Yoast\WP\SEO\Memoizers\Meta_Tags_Context_Memoizer->for_current_page, Yoast\WP\SEO\Repositories\Indexable_Repository->for_current_page, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute, QM_DB->query
[06-Jun-2025 13:15:17 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '11896', `object_type` = 'post', `object_sub_type` = 'page', `permalink` = 'http://fantasytradecalculator.test/', `primary_focus_keyword_score` = '51', `readability_score` = '60', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = NULL, `open_graph_image_id` = NULL, `open_graph_image_source` = NULL, `open_graph_image_meta` = NULL, `twitter_image` = NULL, `twitter_image_id` = NULL, `twitter_image_source` = NULL, `primary_focus_keyword` = 'Dynasty Trade Calculator', `canonical` = NULL, `title` = NULL, `description` = 'Fantasy Football Trade Calculator & Rankings | AI-Powered League-Integrated Trade Analysis. Use our suite of tools to get personalized advice, expert rankings, and make smarter decisions in your fantasy football league.', `breadcrumb_title` = 'Home', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = '8', `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2024-11-08 19:24:32', `object_published_at` = '2017-08-13 00:59:31', `version` = '2', `permalink_hash` = '35:821c0470657449b9a28298410c4a300e', `updated_at` = '2025-06-06 13:15:17' WHERE `id` = '3' made by require('index.php'), require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('/themes/Avada/page.php'), get_header, locate_template, load_template, require_once('/themes/Avada/header.php'), wp_head, do_action('wp_head'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Front_End_Integration->call_wpseo_head, do_action('wpseo_head'), WP_Hook->do_action, WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Front_End_Integration->present_head, Yoast\WP\SEO\Presenters\Schema_Presenter->present, Yoast\WP\SEO\Presenters\Schema_Presenter->get, Yoast\WP\SEO\Presentations\Abstract_Presentation->__get, Yoast\WP\SEO\Presentations\Indexable_Presentation->generate_schema, Yoast\WP\SEO\Generators\Schema_Generator->generate, Yoast\WP\SEO\Generators\Schema_Generator->generate_graph, Yoast\WP\SEO\Generators\Schema\Breadcrumb->generate, Yoast\WP\SEO\Presentations\Abstract_Presentation->__get, Yoast\WP\SEO\Presentations\Indexable_Presentation->generate_breadcrumbs, Yoast\WP\SEO\Generators\Breadcrumbs_Generator->generate, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute, QM_DB->query
[06-Jun-2025 13:15:17 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/fantasytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[06-Jun-2025 13:15:17 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/fantasytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[06-Jun-2025 13:15:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:15:20 UTC] WordPress database error Unknown column 'estimated_reading_time_minutes' in 'field list' for query UPDATE `wp_yoast_indexable` SET `object_id` = '11896', `object_type` = 'post', `object_sub_type` = 'page', `permalink` = 'http://fantasytradecalculator.test/', `primary_focus_keyword_score` = '51', `readability_score` = '60', `inclusive_language_score` = '0', `is_cornerstone` = '0', `is_robots_noindex` = NULL, `is_robots_nofollow` = '0', `is_robots_noimageindex` = NULL, `is_robots_noarchive` = NULL, `is_robots_nosnippet` = NULL, `open_graph_image` = NULL, `open_graph_image_id` = NULL, `open_graph_image_source` = NULL, `open_graph_image_meta` = NULL, `twitter_image` = NULL, `twitter_image_id` = NULL, `twitter_image_source` = NULL, `primary_focus_keyword` = 'Dynasty Trade Calculator', `canonical` = NULL, `title` = NULL, `description` = 'Fantasy Football Trade Calculator & Rankings | AI-Powered League-Integrated Trade Analysis. Use our suite of tools to get personalized advice, expert rankings, and make smarter decisions in your fantasy football league.', `breadcrumb_title` = 'Home', `open_graph_title` = NULL, `open_graph_description` = NULL, `twitter_title` = NULL, `twitter_description` = NULL, `estimated_reading_time_minutes` = '8', `author_id` = '3', `post_parent` = '0', `number_of_pages` = NULL, `post_status` = 'publish', `is_protected` = '0', `is_public` = NULL, `has_public_posts` = NULL, `blog_id` = '1', `schema_page_type` = NULL, `schema_article_type` = NULL, `object_last_modified` = '2024-11-08 19:24:32', `object_published_at` = '2017-08-13 00:59:31', `version` = '2', `permalink_hash` = '35:821c0470657449b9a28298410c4a300e', `updated_at` = '2025-06-06 13:15:20' WHERE `id` = '3' made by require('index.php'), require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('/themes/Avada/page.php'), get_header, locate_template, load_template, require_once('/themes/Avada/header.php'), wp_head, do_action('wp_head'), WP_Hook->do_action, WP_Hook->apply_filters, wp_robots, apply_filters('wp_robots'), WP_Hook->apply_filters, Yoast\WP\SEO\Integrations\Front_End\WP_Robots_Integration->add_robots, Yoast\WP\SEO\Integrations\Front_End\WP_Robots_Integration->get_robots_value, Yoast\WP\SEO\Memoizers\Meta_Tags_Context_Memoizer->for_current_page, Yoast\WP\SEO\Repositories\Indexable_Repository->for_current_page, Yoast\WP\SEO\Repositories\Indexable_Repository->find_by_id_and_type, Yoast\WP\SEO\Repositories\Indexable_Repository->upgrade_indexable, Yoast\WP\SEO\Builders\Indexable_Builder->build, Yoast\WP\SEO\Helpers\Indexable_Helper->save_indexable, Yoast\WP\SEO\Models\Indexable->save, Yoast\WP\Lib\Model->save, Yoast\WP\Lib\ORM->save, Yoast\WP\Lib\ORM::execute, QM_DB->query
[06-Jun-2025 13:15:20 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/fantasytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[06-Jun-2025 13:15:20 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/fantasytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[06-Jun-2025 13:16:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rocket</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:16:49 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:17:04 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:17:04 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>easy-pricing-tables</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:17:05 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
[06-Jun-2025 13:17:05 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>popup-maker</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/fantasytradecalculator/wp-includes/functions.php on line 6121
