2025-4-21 15:55:55 - Event ID: evt_1RGRNeDrMO37mudtyM4VJYjh; Event Type: invoice.created
2025-4-21 15:55:55 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-21 16:00:50 - Starting to process Stripe webhook.
2025-4-21 16:00:50 - Event ID: evt_1RGRSPDrMO37mudtKqOTYr12; Event Type: customer.subscription.trial_will_end
2025-4-21 16:00:50 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.trial_will_end" is not currently handled.
2025-4-21 16:22:21 - Starting to process Stripe webhook.
2025-4-21 16:22:21 - Event ID: evt_1RGRnDDrMO37mudtgCTjak6j; Event Type: invoice.created
2025-4-21 16:22:21 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-21 16:22:21 - Starting to process Stripe webhook.
2025-4-21 16:22:21 - Event ID: evt_1RGRnEDrMO37mudtsCcxTXym; Event Type: customer.subscription.updated
2025-4-21 16:22:21 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-21 16:28:29 - Starting to process Stripe webhook.
2025-4-21 16:28:29 - Event ID: evt_1RGRtADrMO37mudt0wovcF3W; Event Type: invoice.upcoming
2025-4-21 16:28:29 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-21 16:35:55 - Starting to process Stripe webhook.
2025-4-21 16:35:55 - Event ID: evt_1RGS0NDrMO37mudtzFyGFe8z; Event Type: customer.subscription.trial_will_end
2025-4-21 16:35:55 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.trial_will_end" is not currently handled.
2025-4-21 16:36:17 - Starting to process Stripe webhook.
2025-4-21 16:36:18 - Event ID: evt_1RGS0jDrMO37mudtxFmMsiXX; Event Type: invoice.upcoming
2025-4-21 16:36:18 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-21 16:47:42 - Starting to process Stripe webhook.
2025-4-21 16:47:42 - Starting to process Stripe webhook.
2025-4-21 16:47:42 - Event ID: evt_1RGSBlDrMO37mudt0Xodb1FW; Event Type: customer.subscription.updated
2025-4-21 16:47:42 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-21 16:47:42 - Event ID: evt_1RGSBlDrMO37mudt4AeXxLiD; Event Type: invoice.created
2025-4-21 16:47:42 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-21 16:49:19 - Starting to process Stripe webhook.
2025-4-21 16:49:19 - Event ID: evt_3RGSDJDrMO37mudt3SwSqv94; Event Type: payment_intent.created
2025-4-21 16:49:19 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 16:49:21 - Starting to process Stripe webhook.
2025-4-21 16:49:21 - Starting to process Stripe webhook.
2025-4-21 16:49:21 - Event ID: evt_3RGSDJDrMO37mudt3xbrBONO; Event Type: charge.succeeded
2025-4-21 16:49:21 - Stripe Webhook: Getting membership by subscription key: 96c3b3f329e24eae0243a65603932dbe
2025-4-21 16:49:21 - Event ID: evt_3RGSDJDrMO37mudt3Y8GClHX; Event Type: payment_intent.succeeded
2025-4-21 16:49:21 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 16:49:21 - Exiting Stripe webhook - membership not found. Customer ID: cus_Fl0ujDK2DTz6vF.
2025-4-21 16:52:58 - Starting to process Stripe webhook.
2025-4-21 16:52:58 - Event ID: evt_1RGSGrDrMO37mudtiHOhF4R5; Event Type: invoice.upcoming
2025-4-21 16:52:58 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-21 16:56:11 - Starting to process Stripe webhook.
2025-4-21 16:56:11 - Starting to process Stripe webhook.
2025-4-21 16:56:11 - Event ID: evt_3RGSJwDrMO37mudt3sVR4npz; Event Type: charge.succeeded
2025-4-21 16:56:11 - Event ID: evt_3RGSJwDrMO37mudt3Aw20GmB; Event Type: payment_intent.succeeded
2025-4-21 16:56:11 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 16:56:12 - Exiting Stripe webhook - membership not found. Customer ID: cus_LC3GGTJJ24Kepw.
2025-4-21 16:56:12 - Starting to process Stripe webhook.
2025-4-21 16:56:12 - Event ID: evt_3RGSJwDrMO37mudt3SZ0zZF2; Event Type: payment_intent.created
2025-4-21 16:56:12 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 16:56:12 - Starting to process Stripe webhook.
2025-4-21 16:56:13 - Event ID: evt_1RGSJzDrMO37mudti6HeZbTn; Event Type: invoice.updated
2025-4-21 16:56:13 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 16:56:13 - Starting to process Stripe webhook.
2025-4-21 16:56:13 - Event ID: evt_1RGSK0DrMO37mudtcLeDR4B4; Event Type: invoice.finalized
2025-4-21 16:56:13 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-21 16:56:14 - Starting to process Stripe webhook.
2025-4-21 16:56:14 - Event ID: evt_1RGSK0DrMO37mudt6DYBmfgJ; Event Type: invoice.paid
2025-4-21 16:56:14 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-21 16:56:14 - Starting to process Stripe webhook.
2025-4-21 16:56:14 - Event ID: evt_1RGSK0DrMO37mudtrLlfHEIa; Event Type: invoice.payment_succeeded
2025-4-21 16:56:14 - Exiting Stripe webhook - membership not found. Customer ID: cus_LC3GGTJJ24Kepw.
2025-4-21 16:56:45 - Starting to process Stripe webhook.
2025-4-21 16:56:45 - Starting to process Stripe webhook.
2025-4-21 16:56:45 - Event ID: evt_3RGSKUDrMO37mudt064HvJvO; Event Type: payment_intent.succeeded
2025-4-21 16:56:45 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 16:56:45 - Event ID: evt_3RGSKUDrMO37mudt0gbp80z2; Event Type: charge.succeeded
2025-4-21 16:56:45 - Exiting Stripe webhook - membership not found. Customer ID: cus_IFJ4SWssdeo1Px.
2025-4-21 16:56:45 - Starting to process Stripe webhook.
2025-4-21 16:56:46 - Event ID: evt_3RGSKUDrMO37mudt0z8oD2Ep; Event Type: payment_intent.created
2025-4-21 16:56:46 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 16:56:46 - Starting to process Stripe webhook.
2025-4-21 16:56:46 - Event ID: evt_1RGSKXDrMO37mudtO8dddBLH; Event Type: invoice.updated
2025-4-21 16:56:46 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 16:56:46 - Starting to process Stripe webhook.
2025-4-21 16:56:46 - Starting to process Stripe webhook.
2025-4-21 16:56:47 - Event ID: evt_1RGSKXDrMO37mudtlOgRuW7i; Event Type: invoice.finalized
2025-4-21 16:56:47 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-21 16:56:47 - Event ID: evt_1RGSKXDrMO37mudt8LkLcCKr; Event Type: invoice.paid
2025-4-21 16:56:47 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-21 16:56:47 - Starting to process Stripe webhook.
2025-4-21 16:56:47 - Event ID: evt_1RGSKYDrMO37mudt8JR4f8xQ; Event Type: invoice.payment_succeeded
2025-4-21 16:56:47 - Exiting Stripe webhook - membership not found. Customer ID: cus_IFJ4SWssdeo1Px.
2025-4-21 17:21:22 - Starting to process Stripe webhook.
2025-4-21 17:21:22 - Starting to process Stripe webhook.
2025-4-21 17:21:22 - Event ID: evt_1RGSiLDrMO37mudtdDPjpO5X; Event Type: customer.created
2025-4-21 17:21:22 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.created" is not currently handled.
2025-4-21 17:21:22 - Event ID: evt_3RGSiMDrMO37mudt4E0zYwGG; Event Type: payment_intent.created
2025-4-21 17:21:22 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 17:21:24 - Starting to process Stripe webhook.
2025-4-21 17:21:24 - Starting to process Stripe webhook.
2025-4-21 17:21:24 - Event ID: evt_3RGSiMDrMO37mudt40yfo3kE; Event Type: payment_intent.succeeded
2025-4-21 17:21:24 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 17:21:24 - Event ID: evt_3RGSiMDrMO37mudt4dCnVh6U; Event Type: charge.succeeded
2025-4-21 17:21:24 - Stripe Webhook: Getting membership by subscription key: ********************************
2025-4-21 17:21:25 - Exiting Stripe webhook - membership not found. Customer ID: cus_SAoLs9B7qPcwWv.
2025-4-21 17:21:26 - Starting to process Stripe webhook.
2025-4-21 17:21:26 - Event ID: evt_1RGSiODrMO37mudtOJruLnw5; Event Type: payment_method.attached
2025-4-21 17:21:26 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_method.attached" is not currently handled.
2025-4-21 17:21:27 - Starting to process Stripe webhook.
2025-4-21 17:21:27 - Event ID: evt_1RGSiQDrMO37mudtnuEQp9j1; Event Type: customer.updated
2025-4-21 17:21:27 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.updated" is not currently handled.
2025-4-21 17:21:27 - Starting to process Stripe webhook.
2025-4-21 17:21:28 - Event ID: evt_1RGSiPDrMO37mudtdiVjW32g; Event Type: customer.updated
2025-4-21 17:21:28 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.updated" is not currently handled.
2025-4-21 17:21:29 - Starting to process Stripe webhook.
2025-4-21 17:21:29 - Starting to process Stripe webhook.
2025-4-21 17:21:29 - Starting to process Stripe webhook.
2025-4-21 17:21:29 - Starting to process Stripe webhook.
2025-4-21 17:21:29 - Event ID: evt_1RGSiSDrMO37mudtJgXzo6hp; Event Type: setup_intent.succeeded
2025-4-21 17:21:29 - Starting to process Stripe webhook.
 Stripe webhook. The webhook "setup_intent.succeeded" is not currently handled.
2025-4-21 17:21:29 - Event ID: evt_1RGSiSDrMO37mudtDnfcwSjt; Event Type: customer.updated
2025-4-21 17:21:29 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.updated" is not currently handled.
2025-4-21 17:21:29 - Event ID: evt_1RGSiSDrMO37mudtOc09RMaz; Event Type: customer.subscription.created
2025-4-21 17:21:29 - Event ID: evt_1RGSiSDrMO37mudtGplkKmSS; Event Type: invoice.created
2025-4-21 17:21:29 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-21 17:21:29 - Event ID: evt_1RGSiSDrMO37mudt176fRNFr; Event Type: setup_intent.created
2025-4-21 17:21:29 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "setup_intent.created" is not currently handled.
2025-4-21 17:21:29 - Exiting Stripe webhook - membership not found. Customer ID: cus_SAoLs9B7qPcwWv.
2025-4-21 17:21:30 - Starting to process Stripe webhook.
2025-4-21 17:21:30 - Starting to process Stripe webhook.
2025-4-21 17:21:30 - Starting to process Stripe webhook.
2025-4-21 17:21:30 - Event ID: evt_1RGSiSDrMO37mudtQVpxVjfE; Event Type: invoice.finalized
2025-4-21 17:21:30 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-21 17:21:30 - Event ID: evt_1RGSiSDrMO37mudtpWZQcmjF; Event Type: invoice.payment_succeeded
2025-4-21 17:21:30 - Event ID: evt_1RGSiSDrMO37mudtihEYR81D; Event Type: invoice.paid
2025-4-21 17:21:30 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-21 17:21:30 - Exiting Stripe webhook - membership not found. Customer ID: cus_SAoLs9B7qPcwWv.
2025-4-21 17:22:46 - Starting to process Stripe webhook.
2025-4-21 17:22:46 - Starting to process Stripe webhook.
2025-4-21 17:22:46 - Event ID: evt_3RGSjeDrMO37mudt3LHMnb2S; Event Type: payment_intent.succeeded
2025-4-21 17:22:46 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 17:22:46 - Event ID: evt_3RGSjeDrMO37mudt3Mqh3Hih; Event Type: charge.succeeded
2025-4-21 17:22:46 - Exiting Stripe webhook - membership not found. Customer ID: cus_Py05ZGCTOLazFZ.
2025-4-21 17:22:46 - Starting to process Stripe webhook.
2025-4-21 17:22:47 - Event ID: evt_3RGSjeDrMO37mudt3mN0TeDc; Event Type: payment_intent.created
2025-4-21 17:22:47 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 17:22:47 - Starting to process Stripe webhook.
2025-4-21 17:22:47 - Starting to process Stripe webhook.
2025-4-21 17:22:47 - Event ID: evt_1RGSjiDrMO37mudtQKnsV6GA; Event Type: invoice.updated
2025-4-21 17:22:47 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 17:22:47 - Starting to process Stripe webhook.
2025-4-21 17:22:47 - Event ID: evt_1RGSjjDrMO37mudtodoVAjGo; Event Type: invoice.paid
2025-4-21 17:22:47 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-21 17:22:47 - Event ID: evt_1RGSjiDrMO37mudtFJTsnzK8; Event Type: invoice.finalized
2025-4-21 17:22:47 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-21 17:22:48 - Starting to process Stripe webhook.
2025-4-21 17:22:48 - Event ID: evt_1RGSjjDrMO37mudtrVtnAkQa; Event Type: invoice.payment_succeeded
2025-4-21 17:22:48 - Exiting Stripe webhook - membership not found. Customer ID: cus_Py05ZGCTOLazFZ.
2025-4-21 17:23:15 - Starting to process Stripe webhook.
2025-4-21 17:23:15 - Event ID: evt_3RGSkADrMO37mudt2tiu0UoU; Event Type: payment_intent.created
2025-4-21 17:23:15 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 17:23:16 - Starting to process Stripe webhook.
2025-4-21 17:23:17 - Event ID: evt_3RGSkADrMO37mudt2WYuZZWu; Event Type: payment_intent.succeeded
2025-4-21 17:23:17 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 17:23:17 - Starting to process Stripe webhook.
2025-4-21 17:23:17 - Event ID: evt_3RGSkADrMO37mudt2R1vDawD; Event Type: charge.succeeded
2025-4-21 17:23:17 - Stripe Webhook: Getting membership by subscription key: bf44e8ba6d61be3d5abe02a261df5067
2025-4-21 17:23:17 - Exiting Stripe webhook - membership not found. Customer ID: cus_JFE9MgUCgZop8T.
2025-4-21 17:23:17 - Starting to process Stripe webhook.
2025-4-21 17:23:18 - Event ID: evt_1RGSkDDrMO37mudtWmXDaY3a; Event Type: customer.updated
2025-4-21 17:23:18 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.updated" is not currently handled.
2025-4-21 17:23:20 - Starting to process Stripe webhook.
2025-4-21 17:23:20 - Starting to process Stripe webhook.
2025-4-21 17:23:20 - Starting to process Stripe webhook.
2025-4-21 17:23:20 - Event ID: evt_1RGSkFDrMO37mudt6hsr5Dmx; Event Type: setup_intent.created
2025-4-21 17:23:20 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "setup_intent.created" is not currently handled.
2025-4-21 17:23:20 - Event ID: evt_1RGSkFDrMO37mudtJOotW1F6; Event Type: customer.subscription.created
2025-4-21 17:23:20 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "setup_intent.succeeded" is not currently handled.
2025-4-21 17:23:20 - Starting to process Stripe webhook.
2025-4-21 17:23:20 - Starting to process Stripe webhook.
2025-4-21 17:23:21 - Exiting Stripe webhook - membership not found. Customer ID: cus_JFE9MgUCgZop8T.
2025-4-21 17:23:21 - Event ID: evt_1RGSkGDrMO37mudtUF2a9h4G; Event Type: invoice.finalized
2025-4-21 17:23:21 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-21 17:23:21 - Event ID: evt_1RGSkGDrMO37mudt8nkOipfj; Event Type: invoice.created
2025-4-21 17:23:21 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-21 17:23:21 - Starting to process Stripe webhook.
2025-4-21 17:23:21 - Starting to process Stripe webhook.
2025-4-21 17:23:21 - Event ID: evt_1RGSkGDrMO37mudtLI4fK8bF; Event Type: invoice.paid
2025-4-21 17:23:21 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-21 17:23:21 - Event ID: evt_1RGSkGDrMO37mudtI3ev31nI; Event Type: invoice.payment_succeeded
2025-4-21 17:23:22 - Exiting Stripe webhook - membership not found. Customer ID: cus_JFE9MgUCgZop8T.
2025-4-21 17:37:49 - Starting to process Stripe webhook.
2025-4-21 17:37:49 - Event ID: evt_1RGSyHDrMO37mudtgl06tyH0; Event Type: invoice.upcoming
2025-4-21 17:37:49 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-21 17:48:16 - Starting to process Stripe webhook.
2025-4-21 17:48:16 - Event ID: evt_3RGT8LDrMO37mudt2Sp1vzpP; Event Type: payment_intent.succeeded
2025-4-21 17:48:16 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 17:48:16 - Event ID: evt_3RGT8LDrMO37mudt2M8rBidy; Event Type: charge.succeeded
2025-4-21 17:48:17 - Exiting Stripe webhook - membership not found. Customer ID: cus_KxQnNSvJCdvPTc.
2025-4-21 17:48:17 - Starting to process Stripe webhook.
2025-4-21 17:48:17 - Event ID: evt_3RGT8LDrMO37mudt2esQ7j4a; Event Type: payment_intent.created
2025-4-21 17:48:17 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 17:48:17 - Starting to process Stripe webhook.
2025-4-21 17:48:17 - Starting to process Stripe webhook.
2025-4-21 17:48:17 - Event ID: evt_1RGT8PDrMO37mudtvDuasby9; Event Type: invoice.updated
2025-4-21 17:48:17 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 17:48:17 - Starting to process Stripe webhook.
2025-4-21 17:48:17 - Event ID: evt_1RGT8PDrMO37mudt15NuI9Of; Event Type: invoice.finalized
2025-4-21 17:48:17 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-21 17:48:17 - Event ID: evt_1RGT8PDrMO37mudtKpRIAy5R; Event Type: invoice.paid
2025-4-21 17:48:17 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-21 17:48:18 - Starting to process Stripe webhook.
2025-4-21 17:48:18 - Event ID: evt_1RGT8PDrMO37mudtNlqjnQc7; Event Type: invoice.payment_succeeded
2025-4-21 17:48:18 - Exiting Stripe webhook - membership not found. Customer ID: cus_KxQnNSvJCdvPTc.
2025-4-21 17:50:19 - Starting to process Stripe webhook.
2025-4-21 17:50:20 - Event ID: evt_1RGTANDrMO37mudtRFz8r7ky; Event Type: customer.subscription.trial_will_end
2025-4-21 17:50:20 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.trial_will_end" is not currently handled.
2025-4-21 18:02:28 - Starting to process Stripe webhook.
2025-4-21 18:02:28 - Event ID: evt_1RGTM8DrMO37mudt0GyNz7tC; Event Type: invoice.upcoming
2025-4-21 18:02:28 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-21 18:23:40 - Starting to process Stripe webhook.
2025-4-21 18:23:40 - Starting to process Stripe webhook.
2025-4-21 18:23:40 - Event ID: evt_3RB0IhDrMO37mudt3z9WWjI2; Event Type: charge.failed
2025-4-21 18:23:40 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "charge.failed" is not currently handled.
2025-4-21 18:23:40 - Event ID: evt_3RB0IhDrMO37mudt3gHlSwPD; Event Type: payment_intent.payment_failed
2025-4-21 18:23:40 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.payment_failed" is not currently handled.
2025-4-21 18:23:42 - Starting to process Stripe webhook.
2025-4-21 18:23:42 - Starting to process Stripe webhook.
2025-4-21 18:23:42 - Event ID: evt_1RGTgfDrMO37mudtnvmMBIea; Event Type: invoice.payment_failed
2025-4-21 18:23:42 - Event ID: evt_1RGTgfDrMO37mudtWbndcnZh; Event Type: customer.subscription.deleted
2025-4-21 18:23:42 - Starting to process Stripe webhook.
2025-4-21 18:23:42 - Exiting Stripe webhook - membership not found. Customer ID: cus_OMl6IlT47gdG0Q.
2025-4-21 18:23:42 - Exiting Stripe webhook - membership not found. Customer ID: cus_OMl6IlT47gdG0Q.
2025-4-21 18:23:42 - Starting to process Stripe webhook.
2025-4-21 18:23:42 - Event ID: evt_1RGTgfDrMO37mudtcDr1cKgQ; Event Type: invoice.updated
2025-4-21 18:23:42 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 18:23:42 - Event ID: evt_1RGTggDrMO37mudtmHLl6ud0; Event Type: invoice.updated
2025-4-21 18:23:42 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 18:30:26 - Starting to process Stripe webhook.
2025-4-21 18:30:26 - Starting to process Stripe webhook.
2025-4-21 18:30:26 - Event ID: evt_1RGTnCDrMO37mudtIgdIPZfo; Event Type: invoice.created
2025-4-21 18:30:26 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-21 18:30:27 - Event ID: evt_1RGTnCDrMO37mudtHILuDl3e; Event Type: customer.subscription.updated
2025-4-21 18:30:27 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-21 19:26:11 - Starting to process Stripe webhook.
2025-4-21 19:26:11 - Starting to process Stripe webhook.
2025-4-21 19:26:12 - Event ID: evt_1RGUf9DrMO37mudt4tgw7mNK; Event Type: customer.source.updated
2025-4-21 19:26:12 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.source.updated" is not currently handled.
2025-4-21 19:26:12 - Event ID: evt_1RGUf9DrMO37mudtmaBJDYF5; Event Type: payment_method.automatically_updated
2025-4-21 19:26:12 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_method.automatically_updated" is not currently handled.
2025-4-21 19:31:36 - Starting to process Stripe webhook.
2025-4-21 19:31:36 - Starting to process Stripe webhook.
2025-4-21 19:31:36 - Event ID: evt_3RGUkLDrMO37mudt1qq9M9jU; Event Type: payment_intent.succeeded
2025-4-21 19:31:36 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 19:31:36 - Event ID: evt_3RGUkLDrMO37mudt1Sc931fE; Event Type: charge.succeeded
2025-4-21 19:31:37 - Exiting Stripe webhook - membership not found. Customer ID: cus_KGmCfvTlDTIQ0B.
2025-4-21 19:31:37 - Starting to process Stripe webhook.
2025-4-21 19:31:37 - Event ID: evt_3RGUkLDrMO37mudt1eJC20ek; Event Type: payment_intent.created
2025-4-21 19:31:37 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 19:31:37 - Starting to process Stripe webhook.
2025-4-21 19:31:38 - Event ID: evt_1RGUkPDrMO37mudtMFHWJ78K; Event Type: invoice.updated
2025-4-21 19:31:38 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 19:31:38 - Starting to process Stripe webhook.
2025-4-21 19:31:38 - Starting to process Stripe webhook.
2025-4-21 19:31:38 - Event ID: evt_1RGUkPDrMO37mudt0WFQ0hWq; Event Type: invoice.finalized
2025-4-21 19:31:38 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-21 19:31:38 - Event ID: evt_1RGUkPDrMO37mudtMYSDe5gK; Event Type: invoice.paid
2025-4-21 19:31:38 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-21 19:31:38 - Starting to process Stripe webhook.
2025-4-21 19:31:38 - Event ID: evt_1RGUkPDrMO37mudtTCREWzHq; Event Type: invoice.payment_succeeded
2025-4-21 19:31:39 - Exiting Stripe webhook - membership not found. Customer ID: cus_KGmCfvTlDTIQ0B.
2025-4-21 19:41:45 - Starting to process Stripe webhook.
2025-4-21 19:41:45 - Starting to process Stripe webhook.
2025-4-21 19:41:46 - Event ID: evt_1RGUuDDrMO37mudtMfEuMoK2; Event Type: invoice.created
2025-4-21 19:41:46 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-21 19:41:46 - Event ID: evt_1RGUuDDrMO37mudt9FsOXnDR; Event Type: customer.subscription.updated
2025-4-21 19:41:46 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-21 19:43:29 - Starting to process Stripe webhook.
2025-4-21 19:43:29 - Event ID: evt_1RGUvsDrMO37mudtcPBRxD1v; Event Type: balance.available
2025-4-21 19:43:29 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "balance.available" is not currently handled.
2025-4-21 19:50:05 - Starting to process Stripe webhook.
2025-4-21 19:50:05 - Event ID: evt_1RGV2GDrMO37mudt0qPxXYrk; Event Type: invoice.upcoming
2025-4-21 19:50:05 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-21 19:52:25 - Starting to process Stripe webhook.
2025-4-21 19:52:25 - Event ID: evt_1RGV4XDrMO37mudtTwaUmmcJ; Event Type: payment_method.automatically_updated
2025-4-21 19:52:25 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_method.automatically_updated" is not currently handled.
2025-4-21 19:57:56 - Starting to process Stripe webhook.
2025-4-21 19:57:57 - Event ID: evt_1RGV9sDrMO37mudtM6J4Xyau; Event Type: customer.subscription.trial_will_end
2025-4-21 19:57:57 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.trial_will_end" is not currently handled.
2025-4-21 19:59:55 - Starting to process Stripe webhook.
2025-4-21 19:59:55 - Starting to process Stripe webhook.
2025-4-21 19:59:55 - Event ID: evt_1RGVBmDrMO37mudtDavSeoLH; Event Type: invoice.created
2025-4-21 19:59:55 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-21 19:59:55 - Event ID: evt_1RGVBnDrMO37mudtTvuesdER; Event Type: customer.subscription.updated
2025-4-21 19:59:55 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-21 20:05:45 - Starting to process Stripe webhook.
2025-4-21 20:05:45 - Event ID: evt_1RGVHRDrMO37mudtgwrUlmza; Event Type: customer.subscription.trial_will_end
2025-4-21 20:05:45 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.trial_will_end" is not currently handled.
2025-4-21 20:17:07 - Starting to process Stripe webhook.
2025-4-21 20:17:07 - Starting to process Stripe webhook.
2025-4-21 20:17:07 - Event ID: evt_1RGVSQDrMO37mudtmxOUzrUQ; Event Type: customer.subscription.updated
2025-4-21 20:17:07 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-21 20:17:07 - Event ID: evt_1RGVSQDrMO37mudtbFcbUqqy; Event Type: invoice.created
2025-4-21 20:17:07 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-21 20:23:36 - Starting to process Stripe webhook.
2025-4-21 20:23:36 - Event ID: evt_1RGVYhDrMO37mudtfr58Cq18; Event Type: invoice.upcoming
2025-4-21 20:23:36 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-21 20:25:29 - Starting to process Stripe webhook.
2025-4-21 20:25:29 - Event ID: evt_1RGVaXDrMO37mudtiqbZzaTL; Event Type: invoice.upcoming
2025-4-21 20:25:29 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-21 20:35:08 - Starting to process Stripe webhook.
2025-4-21 20:35:08 - Event ID: evt_1RGVjrDrMO37mudtoXz9CiAL; Event Type: invoice.upcoming
2025-4-21 20:35:08 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-21 20:35:14 - Starting to process Stripe webhook.
2025-4-21 20:35:14 - Event ID: evt_1RGVjyDrMO37mudtGaQMXJeM; Event Type: invoice.upcoming
2025-4-21 20:35:14 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-21 20:42:12 - Starting to process Stripe webhook.
2025-4-21 20:42:12 - Event ID: evt_3RGVqfDrMO37mudt0TQz1TqN; Event Type: payment_intent.succeeded
2025-4-21 20:42:12 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 20:42:12 - Starting to process Stripe webhook.
2025-4-21 20:42:13 - Event ID: evt_3RGVqfDrMO37mudt0vJ5q71i; Event Type: charge.succeeded
2025-4-21 20:42:13 - Exiting Stripe webhook - membership not found. Customer ID: cus_CYoB3rTyq0WSTU.
2025-4-21 20:42:13 - Starting to process Stripe webhook.
2025-4-21 20:42:14 - Event ID: evt_3RGVqfDrMO37mudt0k7H3JNq; Event Type: payment_intent.created
2025-4-21 20:42:14 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 20:42:14 - Starting to process Stripe webhook.
2025-4-21 20:42:14 - Starting to process Stripe webhook.
2025-4-21 20:42:14 - Event ID: evt_1RGVqiDrMO37mudtvQwW7kAA; Event Type: invoice.updated
2025-4-21 20:42:14 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 20:42:14 - Event ID: evt_1RGVqjDrMO37mudtaIpWgsA3; Event Type: invoice.finalized
2025-4-21 20:42:14 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-21 20:42:14 - Starting to process Stripe webhook.
2025-4-21 20:42:15 - Event ID: evt_1RGVqjDrMO37mudtiHhuXafB; Event Type: invoice.paid
2025-4-21 20:42:15 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-21 20:42:15 - Starting to process Stripe webhook.
2025-4-21 20:42:15 - Event ID: evt_1RGVqjDrMO37mudtDpPxd8Im; Event Type: invoice.payment_succeeded
2025-4-21 20:42:15 - Exiting Stripe webhook - membership not found. Customer ID: cus_CYoB3rTyq0WSTU.
2025-4-21 20:51:22 - Starting to process Stripe webhook.
2025-4-21 20:51:22 - Event ID: evt_1RGVzZDrMO37mudtjPqmqLuz; Event Type: invoice.upcoming
2025-4-21 20:51:23 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-21 20:55:07 - Starting to process Stripe webhook.
2025-4-21 20:55:07 - Starting to process Stripe webhook.
2025-4-21 20:55:08 - Event ID: evt_1RGW3DDrMO37mudtVkcZEVyX; Event Type: invoice.created
2025-4-21 20:55:08 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-21 20:55:08 - Event ID: evt_1RGW3DDrMO37mudtxcYcmwoD; Event Type: customer.subscription.updated
2025-4-21 20:55:08 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-21 21:00:10 - Starting to process Stripe webhook.
2025-4-21 21:00:10 - Event ID: evt_3RGW83DrMO37mudt0MCqRemD; Event Type: payment_intent.succeeded
2025-4-21 21:00:10 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 21:00:10 - Starting to process Stripe webhook.
2025-4-21 21:00:10 - Event ID: evt_3RGW83DrMO37mudt0NdjDQdL; Event Type: charge.succeeded
2025-4-21 21:00:11 - Exiting Stripe webhook - membership not found. Customer ID: cus_Py3a02v3yOFMDV.
2025-4-21 21:00:11 - Starting to process Stripe webhook.
2025-4-21 21:00:11 - Event ID: evt_3RGW83DrMO37mudt0oL6caAp; Event Type: payment_intent.created
2025-4-21 21:00:11 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 21:00:12 - Starting to process Stripe webhook.
2025-4-21 21:00:12 - Starting to process Stripe webhook.
2025-4-21 21:00:12 - Starting to process Stripe webhook.
2025-4-21 21:00:12 - Event ID: evt_1RGW87DrMO37mudt3zEF8y7Q; Event Type: invoice.updated
2025-4-21 21:00:12 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 21:00:12 - Event ID: evt_1RGW87DrMO37mudtvSta5b8K; Event Type: invoice.paid
2025-4-21 21:00:12 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-21 21:00:12 - Event ID: evt_1RGW87DrMO37mudtojfuE0ph; Event Type: invoice.finalized
2025-4-21 21:00:12 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-21 21:00:12 - Starting to process Stripe webhook.
2025-4-21 21:00:12 - Event ID: evt_1RGW87DrMO37mudtnKcPe88q; Event Type: invoice.payment_succeeded
2025-4-21 21:00:13 - Exiting Stripe webhook - membership not found. Customer ID: cus_Py3a02v3yOFMDV.
2025-4-21 21:03:26 - Starting to process Stripe webhook.
2025-4-21 21:03:26 - Starting to process Stripe webhook.
2025-4-21 21:03:26 - Event ID: evt_1RGWBGDrMO37mudt4dEGFbsM; Event Type: invoice.created
2025-4-21 21:03:26 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-21 21:03:27 - Event ID: evt_1RGWBGDrMO37mudtznTR6Dlg; Event Type: customer.subscription.updated
2025-4-21 21:03:27 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-21 21:18:06 - Starting to process Stripe webhook.
2025-4-21 21:18:06 - Starting to process Stripe webhook.
2025-4-21 21:18:06 - Event ID: evt_1RGWPRDrMO37mudteeNHwgzk; Event Type: invoice.created
2025-4-21 21:18:06 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-21 21:18:06 - Event ID: evt_1RGWPSDrMO37mudt2sFecwkd; Event Type: customer.subscription.updated
2025-4-21 21:18:06 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-21 21:18:09 - Starting to process Stripe webhook.
2025-4-21 21:18:09 - Starting to process Stripe webhook.
2025-4-21 21:18:09 - Event ID: evt_3RGWPTDrMO37mudt3PNI8h9K; Event Type: charge.succeeded
2025-4-21 21:18:09 - Event ID: evt_3RGWPTDrMO37mudt3uQBJQ39; Event Type: payment_intent.succeeded
2025-4-21 21:18:09 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 21:18:10 - Exiting Stripe webhook - membership not found. Customer ID: cus_GuocbJyhmSmlNE.
2025-4-21 21:18:10 - Starting to process Stripe webhook.
2025-4-21 21:18:10 - Event ID: evt_3RGWPTDrMO37mudt35Og9KID; Event Type: payment_intent.created
2025-4-21 21:18:10 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 21:18:10 - Starting to process Stripe webhook.
2025-4-21 21:18:10 - Starting to process Stripe webhook.
2025-4-21 21:18:10 - Starting to process Stripe webhook.
2025-4-21 21:18:11 - Event ID: evt_1RGWPWDrMO37mudtTJgHShyf; Event Type: invoice.updated
2025-4-21 21:18:11 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 21:18:11 - Event ID: evt_1RGWPWDrMO37mudtNDqkFn4Z; Event Type: invoice.paid
2025-4-21 21:18:11 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-21 21:18:11 - Event ID: evt_1RGWPWDrMO37mudtiODqyMjb; Event Type: invoice.finalized
2025-4-21 21:18:11 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-21 21:18:11 - Starting to process Stripe webhook.
2025-4-21 21:18:11 - Event ID: evt_1RGWPWDrMO37mudtnmj63Mwo; Event Type: invoice.payment_succeeded
2025-4-21 21:18:11 - Exiting Stripe webhook - membership not found. Customer ID: cus_GuocbJyhmSmlNE.
2025-4-21 21:20:14 - Starting to process Stripe webhook.
2025-4-21 21:20:14 - Event ID: evt_1RGWRVDrMO37mudthVvm4atu; Event Type: invoice.upcoming
2025-4-21 21:20:14 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-21 21:41:25 - Starting to process Stripe webhook.
2025-4-21 21:41:25 - Starting to process Stripe webhook.
2025-4-21 21:41:26 - Event ID: evt_1RGWm1DrMO37mudtVQnmURXv; Event Type: invoice.created
2025-4-21 21:41:26 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-21 21:41:26 - Event ID: evt_1RGWm1DrMO37mudthoYgU3zh; Event Type: customer.subscription.updated
2025-4-21 21:41:26 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-21 21:55:51 - Starting to process Stripe webhook.
2025-4-21 21:55:51 - Starting to process Stripe webhook.
2025-4-21 21:55:51 - Event ID: evt_3RGWzwDrMO37mudt2Zj2M2mk; Event Type: payment_intent.succeeded
2025-4-21 21:55:51 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 21:55:51 - Event ID: evt_3RGWzwDrMO37mudt2YqwTfcf; Event Type: charge.succeeded
2025-4-21 21:55:51 - Starting to process Stripe webhook.
2025-4-21 21:55:52 - Exiting Stripe webhook - membership not found. Customer ID: cus_Hioo2xQXRtBdLh.
2025-4-21 21:55:52 - Event ID: evt_3RGWzwDrMO37mudt2sLLIMZQ; Event Type: payment_intent.created
2025-4-21 21:55:52 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 21:55:52 - Starting to process Stripe webhook.
2025-4-21 21:55:52 - Starting to process Stripe webhook.
2025-4-21 21:55:52 - Event ID: evt_1RGWzzDrMO37mudtnd1BSMy2; Event Type: invoice.updated
2025-4-21 21:55:52 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 21:55:52 - Starting to process Stripe webhook.
2025-4-21 21:55:52 - Event ID: evt_1RGX00DrMO37mudtGS1Zi35c; Event Type: invoice.finalized
2025-4-21 21:55:52 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-21 21:55:52 - Event ID: evt_1RGX00DrMO37mudt4xUYPQ6H; Event Type: invoice.paid
2025-4-21 21:55:52 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-21 21:55:52 - Starting to process Stripe webhook.
2025-4-21 21:55:53 - Event ID: evt_1RGX00DrMO37mudtCVnwYKCG; Event Type: invoice.payment_succeeded
2025-4-21 21:55:53 - Exiting Stripe webhook - membership not found. Customer ID: cus_Hioo2xQXRtBdLh.
2025-4-21 21:57:31 - Starting to process Stripe webhook.
2025-4-21 21:57:31 - Event ID: evt_1RGX1aDrMO37mudtdjVboJkj; Event Type: invoice.upcoming
2025-4-21 21:57:31 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-21 22:03:53 - Starting to process Stripe webhook.
2025-4-21 22:03:53 - Starting to process Stripe webhook.
2025-4-21 22:03:54 - Event ID: evt_3RGX7iDrMO37mudt33kXXw9N; Event Type: payment_intent.succeeded
2025-4-21 22:03:54 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 22:03:54 - Event ID: evt_3RGX7iDrMO37mudt3RpO6ZLt; Event Type: charge.succeeded
2025-4-21 22:03:54 - Starting to process Stripe webhook.
2025-4-21 22:03:54 - Exiting Stripe webhook - membership not found. Customer ID: cus_QKvO8HCptuWIAM.
2025-4-21 22:03:54 - Event ID: evt_3RGX7iDrMO37mudt32s56X1j; Event Type: payment_intent.created
2025-4-21 22:03:54 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 22:03:54 - Starting to process Stripe webhook.
2025-4-21 22:03:54 - Starting to process Stripe webhook.
2025-4-21 22:03:54 - Event ID: evt_1RGX7mDrMO37mudtXDpXAbDD; Event Type: invoice.updated
2025-4-21 22:03:54 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 22:03:55 - Starting to process Stripe webhook.
2025-4-21 22:03:55 - Event ID: evt_1RGX7mDrMO37mudtcTM7y0Wv; Event Type: invoice.finalized
2025-4-21 22:03:55 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-21 22:03:55 - Event ID: evt_1RGX7mDrMO37mudtibem8Jda; Event Type: invoice.paid
2025-4-21 22:03:55 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-21 22:03:55 - Starting to process Stripe webhook.
2025-4-21 22:03:55 - Event ID: evt_1RGX7mDrMO37mudtNBld1Gj2; Event Type: invoice.payment_succeeded
2025-4-21 22:03:55 - Exiting Stripe webhook - membership not found. Customer ID: cus_QKvO8HCptuWIAM.
2025-4-21 22:18:45 - Starting to process Stripe webhook.
2025-4-21 22:18:45 - Starting to process Stripe webhook.
2025-4-21 22:18:45 - Event ID: evt_3RGXM6DrMO37mudt3upxarLZ; Event Type: payment_intent.succeeded
2025-4-21 22:18:45 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 22:18:45 - Event ID: evt_3RGXM6DrMO37mudt38Ubw2lg; Event Type: charge.succeeded
2025-4-21 22:18:46 - Exiting Stripe webhook - membership not found. Customer ID: cus_PmSqvVoHkidTlc.
2025-4-21 22:18:46 - Starting to process Stripe webhook.
2025-4-21 22:18:46 - Event ID: evt_3RGXM6DrMO37mudt3CwmhwQ7; Event Type: payment_intent.created
2025-4-21 22:18:46 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 22:18:46 - Starting to process Stripe webhook.
2025-4-21 22:18:47 - Starting to process Stripe webhook.
2025-4-21 22:18:47 - Event ID: evt_1RGXMADrMO37mudtH076iK7x; Event Type: invoice.updated
2025-4-21 22:18:47 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 22:18:47 - Starting to process Stripe webhook.
2025-4-21 22:18:47 - Event ID: evt_1RGXMADrMO37mudtI88XoHb7; Event Type: invoice.finalized
2025-4-21 22:18:47 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-21 22:18:47 - Event ID: evt_1RGXMADrMO37mudtr9HHB35p; Event Type: invoice.paid
2025-4-21 22:18:47 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-21 22:18:47 - Starting to process Stripe webhook.
2025-4-21 22:18:47 - Event ID: evt_1RGXMADrMO37mudtI2wb1atg; Event Type: invoice.payment_succeeded
2025-4-21 22:18:48 - Exiting Stripe webhook - membership not found. Customer ID: cus_PmSqvVoHkidTlc.
2025-4-21 22:19:22 - Starting to process Stripe webhook.
2025-4-21 22:19:22 - Event ID: evt_1RGXMkDrMO37mudt420Sia2E; Event Type: invoice.upcoming
2025-4-21 22:19:22 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-21 22:25:12 - Starting to process Stripe webhook.
2025-4-21 22:25:13 - Starting to process Stripe webhook.
2025-4-21 22:25:13 - Event ID: evt_1RGXSODrMO37mudtypiHcWA8; Event Type: invoice.created
2025-4-21 22:25:13 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-21 22:25:13 - Event ID: evt_1RGXSODrMO37mudtcoziyzKH; Event Type: customer.subscription.updated
2025-4-21 22:25:13 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-21 22:31:10 - Starting to process Stripe webhook.
2025-4-21 22:31:10 - Event ID: evt_1RGXY9DrMO37mudttYX8IKR9; Event Type: invoice.upcoming
2025-4-21 22:31:10 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-21 22:42:31 - Starting to process Stripe webhook.
2025-4-21 22:42:31 - Starting to process Stripe webhook.
2025-4-21 22:42:31 - Event ID: evt_3RGXj6DrMO37mudt4QZ9bCid; Event Type: payment_intent.succeeded
2025-4-21 22:42:31 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 22:42:31 - Event ID: evt_3RGXj6DrMO37mudt4zRS40iI; Event Type: charge.succeeded
2025-4-21 22:42:31 - Exiting Stripe webhook - membership not found. Customer ID: cus_J8j2eY9lchIR1F.
2025-4-21 22:42:31 - Starting to process Stripe webhook.
2025-4-21 22:42:32 - Event ID: evt_3RGXj6DrMO37mudt4seilOct; Event Type: payment_intent.created
2025-4-21 22:42:32 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 22:42:32 - Starting to process Stripe webhook.
2025-4-21 22:42:32 - Starting to process Stripe webhook.
2025-4-21 22:42:32 - Event ID: evt_1RGXj9DrMO37mudtDAoPJsLv; Event Type: invoice.updated
2025-4-21 22:42:32 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 22:42:32 - Starting to process Stripe webhook.
2025-4-21 22:42:32 - Event ID: evt_1RGXj9DrMO37mudtFMNCKMLU; Event Type: invoice.finalized
2025-4-21 22:42:32 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-21 22:42:32 - Event ID: evt_1RGXj9DrMO37mudt3Bv6T7iw; Event Type: invoice.paid
2025-4-21 22:42:32 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-21 22:42:32 - Starting to process Stripe webhook.
2025-4-21 22:42:33 - Event ID: evt_1RGXjADrMO37mudtPTcRS5j1; Event Type: invoice.payment_succeeded
2025-4-21 22:42:33 - Exiting Stripe webhook - membership not found. Customer ID: cus_J8j2eY9lchIR1F.
2025-4-21 23:00:06 - Starting rcp_check_member_counts() cron job.
2025-4-21 23:11:10 - Starting to process Stripe webhook.
2025-4-21 23:11:10 - Starting to process Stripe webhook.
2025-4-21 23:11:10 - Event ID: evt_1RGYArDrMO37mudttROxLq3G; Event Type: customer.subscription.deleted
2025-4-21 23:11:10 - Starting to process Stripe webhook.
2025-4-21 23:11:10 - Event ID: evt_1RGYAsDrMO37mudtBCaq6Rvh; Event Type: invoice.updated
2025-4-21 23:11:10 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 23:11:10 - Starting to process Stripe webhook.
2025-4-21 23:11:10 - Exiting Stripe webhook - membership not found. Customer ID: cus_Nwy5wgpvQb1NWY.
2025-4-21 23:11:11 - Event ID: evt_1RGYAsDrMO37mudtZx7rMaMu; Event Type: invoice.payment_failed
2025-4-21 23:11:11 - Event ID: evt_1RGYAsDrMO37mudtF4xnWoIa; Event Type: invoice.updated
2025-4-21 23:11:11 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 23:11:11 - Exiting Stripe webhook - membership not found. Customer ID: cus_Nwy5wgpvQb1NWY.
2025-4-21 23:11:11 - Starting to process Stripe webhook.
2025-4-21 23:11:11 - Event ID: evt_1RGYAsDrMO37mudtEeEWNRC5; Event Type: invoice.updated
2025-4-21 23:11:11 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 23:25:44 - Starting to process Stripe webhook.
2025-4-21 23:25:44 - Starting to process Stripe webhook.
2025-4-21 23:25:44 - Event ID: evt_3RGYOuDrMO37mudt4J3hcjCp; Event Type: charge.succeeded
2025-4-21 23:25:44 - Event ID: evt_3RGYOuDrMO37mudt4i3t552c; Event Type: payment_intent.succeeded
2025-4-21 23:25:44 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-21 23:25:44 - Exiting Stripe webhook - membership not found. Customer ID: cus_F0uwh4CD0fJLbL.
2025-4-21 23:25:44 - Starting to process Stripe webhook.
2025-4-21 23:25:45 - Starting to process Stripe webhook.
2025-4-21 23:25:45 - Event ID: evt_3RGYOuDrMO37mudt4xmf5H5K; Event Type: payment_intent.created
2025-4-21 23:25:45 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-21 23:25:45 - Starting to process Stripe webhook.
2025-4-21 23:25:45 - Event ID: evt_1RGYOyDrMO37mudtSE43dg0N; Event Type: invoice.updated
2025-4-21 23:25:45 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-21 23:25:45 - Event ID: evt_1RGYOyDrMO37mudtVRP79h9T; Event Type: invoice.finalized
2025-4-21 23:25:45 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-21 23:25:45 - Starting to process Stripe webhook.
2025-4-21 23:25:45 - Event ID: evt_1RGYOzDrMO37mudt8zRvn1Ts; Event Type: invoice.paid
2025-4-21 23:25:45 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-21 23:25:45 - Starting to process Stripe webhook.
2025-4-21 23:25:46 - Event ID: evt_1RGYOzDrMO37mudtIsejA9AP; Event Type: invoice.payment_succeeded
2025-4-21 23:25:46 - Exiting Stripe webhook - membership not found. Customer ID: cus_F0uwh4CD0fJLbL.
2025-4-21 23:30:04 - Starting rcp_mark_abandoned_payments() cron job.
2025-4-21 23:38:35 - Starting to process Stripe webhook.
2025-4-21 23:38:36 - Starting to process Stripe webhook.
2025-4-21 23:38:36 - Event ID: evt_1RGYbPDrMO37mudtyhNP5iAc; Event Type: invoice.created
2025-4-21 23:38:36 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-21 23:38:36 - Event ID: evt_1RGYbPDrMO37mudtZewem3wF; Event Type: customer.subscription.updated
2025-4-21 23:38:36 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-22 00:05:58 - Starting to process Stripe webhook.
2025-4-22 00:05:58 - Starting to process Stripe webhook.
2025-4-22 00:05:58 - Event ID: evt_1RGZ1tDrMO37mudtLuoRvsVp; Event Type: invoice.created
2025-4-22 00:05:58 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-22 00:05:58 - Event ID: evt_1RGZ1tDrMO37mudtcEr3yU3x; Event Type: customer.subscription.updated
2025-4-22 00:05:58 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-22 00:06:16 - Starting to process Stripe webhook.
2025-4-22 00:06:16 - Event ID: evt_1RGZ2CDrMO37mudtFQGSx0au; Event Type: invoice.upcoming
2025-4-22 00:06:16 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-22 00:11:35 - Starting to process Stripe webhook.
2025-4-22 00:11:35 - Starting to process Stripe webhook.
2025-4-22 00:11:36 - Event ID: evt_3RDZtvDrMO37mudt1uWh9Q5n; Event Type: payment_intent.payment_failed
2025-4-22 00:11:36 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.payment_failed" is not currently handled.
2025-4-22 00:11:36 - Event ID: evt_3RDZtvDrMO37mudt1pmwkQcB; Event Type: charge.failed
2025-4-22 00:11:36 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "charge.failed" is not currently handled.
2025-4-22 00:11:36 - Starting to process Stripe webhook.
2025-4-22 00:11:36 - Event ID: evt_1RGZ7LDrMO37mudtEJMJxL2p; Event Type: invoice.updated
2025-4-22 00:11:36 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 00:11:41 - Starting to process Stripe webhook.
2025-4-22 00:11:41 - Starting to process Stripe webhook.
2025-4-22 00:11:41 - Event ID: evt_1RGZ7QDrMO37mudtFRsdtqVu; Event Type: invoice.updated
2025-4-22 00:11:41 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 00:11:41 - Event ID: evt_1RGZ7RDrMO37mudtoRnBNLYJ; Event Type: invoice.payment_failed
2025-4-22 00:11:41 - Exiting Stripe webhook - membership not found. Customer ID: cus_QHs3ieEuFVPrjJ.
2025-4-22 00:16:26 - Starting to process Stripe webhook.
2025-4-22 00:16:26 - Event ID: evt_1RGZC1DrMO37mudtUJckqg0I; Event Type: payment_method.automatically_updated
2025-4-22 00:16:26 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_method.automatically_updated" is not currently handled.
2025-4-22 00:17:12 - Starting to process Stripe webhook.
2025-4-22 00:17:12 - Event ID: evt_1RGZCmDrMO37mudtH4ffCeHt; Event Type: payment_method.automatically_updated
2025-4-22 00:17:12 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_method.automatically_updated" is not currently handled.
2025-4-22 00:17:18 - Starting to process Stripe webhook.
2025-4-22 00:17:18 - Event ID: evt_1RGZCrDrMO37mudtLg3p1qm0; Event Type: payment_method.automatically_updated
2025-4-22 00:17:18 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_method.automatically_updated" is not currently handled.
2025-4-22 00:19:27 - Starting to process Stripe webhook.
2025-4-22 00:19:27 - Event ID: evt_1RGZEwDrMO37mudtjjJ9ygw1; Event Type: payment_method.automatically_updated
2025-4-22 00:19:27 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_method.automatically_updated" is not currently handled.
2025-4-22 00:21:11 - Updating customer #25137. New data: array (
  'ips' => 
  array (
    0 => '**************',
    1 => '***************',
    2 => '**************',
    3 => '**************',
    4 => '*************',
    5 => '**************',
    6 => '***************',
    7 => '**************',
    8 => '**************',
    9 => '**************',
    10 => '*************',
  ),
).
2025-4-22 00:21:11 - Updating customer #25137. New data: array (
  'last_login' => '2025-04-22 00:21:11',
).
2025-4-22 00:39:40 - Starting to process Stripe webhook.
2025-4-22 00:39:40 - Event ID: evt_3RGZYSDrMO37mudt1olOsdTt; Event Type: charge.succeeded
2025-4-22 00:39:40 - Event ID: evt_3RGZYSDrMO37mudt1N2v5CkI; Event Type: payment_intent.succeeded
2025-4-22 00:39:40 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-22 00:39:41 - Starting to process Stripe webhook.
2025-4-22 00:39:41 - Exiting Stripe webhook - membership not found. Customer ID: cus_P1CbxiVQbYXgAz.
2025-4-22 00:39:41 - Starting to process Stripe webhook.
2025-4-22 00:39:41 - Event ID: evt_3RGZYSDrMO37mudt126tB3yH; Event Type: payment_intent.created
2025-4-22 00:39:41 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 00:39:41 - Event ID: evt_1RGZYXDrMO37mudt9gWL7Y0r; Event Type: invoice.updated
2025-4-22 00:39:41 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 00:39:41 - Starting to process Stripe webhook.
2025-4-22 00:39:42 - Starting to process Stripe webhook.
2025-4-22 00:39:42 - Event ID: evt_1RGZYXDrMO37mudt9PJJZ5v8; Event Type: invoice.finalized
2025-4-22 00:39:42 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-22 00:39:42 - Event ID: evt_1RGZYXDrMO37mudtoOfx2vzS; Event Type: invoice.paid
2025-4-22 00:39:42 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-22 00:39:42 - Starting to process Stripe webhook.
2025-4-22 00:39:42 - Event ID: evt_1RGZYXDrMO37mudtdcto9ACl; Event Type: invoice.payment_succeeded
2025-4-22 00:39:42 - Exiting Stripe webhook - membership not found. Customer ID: cus_P1CbxiVQbYXgAz.
2025-4-22 00:40:33 - Starting to process Stripe webhook.
2025-4-22 00:40:34 - Event ID: evt_1RGZZNDrMO37mudt3Ib8cxTX; Event Type: payment_method.automatically_updated
2025-4-22 00:40:34 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_method.automatically_updated" is not currently handled.
2025-4-22 00:41:00 - Starting to process Stripe webhook.
2025-4-22 00:41:00 - Event ID: evt_1RGZZoDrMO37mudtaUDyYQbX; Event Type: payment_method.automatically_updated
2025-4-22 00:41:00 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_method.automatically_updated" is not currently handled.
2025-4-22 01:06:42 - Starting to process Stripe webhook.
2025-4-22 01:06:42 - Starting to process Stripe webhook.
2025-4-22 01:06:42 - Event ID: evt_3RGZyeDrMO37mudt0wpztUdg; Event Type: payment_intent.succeeded
2025-4-22 01:06:42 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-22 01:06:43 - Event ID: evt_3RGZyeDrMO37mudt0Heo6Fnq; Event Type: charge.succeeded
2025-4-22 01:06:43 - Exiting Stripe webhook - membership not found. Customer ID: cus_PQPEWzLK8xiBay.
2025-4-22 01:06:43 - Starting to process Stripe webhook.
2025-4-22 01:06:44 - Starting to process Stripe webhook.
2025-4-22 01:06:44 - Event ID: evt_3RGZyeDrMO37mudt0AJdba2R; Event Type: payment_intent.created
2025-4-22 01:06:44 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 01:06:44 - Starting to process Stripe webhook.
2025-4-22 01:06:44 - Event ID: evt_1RGZyhDrMO37mudt9ooCnsbF; Event Type: invoice.updated
2025-4-22 01:06:44 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 01:06:44 - Event ID: evt_1RGZyhDrMO37mudtas2HjsHo; Event Type: invoice.finalized
2025-4-22 01:06:44 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-22 01:06:44 - Starting to process Stripe webhook.
2025-4-22 01:06:44 - Event ID: evt_1RGZyhDrMO37mudtaZh4tSAX; Event Type: invoice.paid
2025-4-22 01:06:44 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-22 01:06:45 - Starting to process Stripe webhook.
2025-4-22 01:06:45 - Event ID: evt_1RGZyhDrMO37mudtWysEE7HV; Event Type: invoice.payment_succeeded
2025-4-22 01:06:45 - Exiting Stripe webhook - membership not found. Customer ID: cus_PQPEWzLK8xiBay.
2025-4-22 01:42:14 - Starting to process Stripe webhook.
2025-4-22 01:42:14 - Starting to process Stripe webhook.
2025-4-22 01:42:14 - Event ID: evt_1RGaX3DrMO37mudtMBTCmTkm; Event Type: invoice.created
2025-4-22 01:42:14 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-22 01:42:14 - Event ID: evt_1RGaX3DrMO37mudtpbakB2Uv; Event Type: customer.subscription.updated
2025-4-22 01:42:14 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-22 02:00:44 - Starting to process Stripe webhook.
2025-4-22 02:00:44 - Starting to process Stripe webhook.
2025-4-22 02:00:44 - Starting to process Stripe webhook.
2025-4-22 02:00:44 - Event ID: evt_1RGaoxDrMO37mudt8JgK90bY; Event Type: customer.subscription.deleted
2025-4-22 02:00:44 - Event ID: evt_1RGaoxDrMO37mudt7Acuc3eh; Event Type: invoice.payment_failed
2025-4-22 02:00:44 - Starting to process Stripe webhook.
2025-4-22 02:00:44 - Event ID: evt_1RGaoxDrMO37mudtQmTwmrcl; Event Type: invoice.updated
2025-4-22 02:00:44 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 02:00:44 - Starting to process Stripe webhook.
2025-4-22 02:00:44 - Exiting Stripe webhook - membership not found. Customer ID: cus_HxByg67Y0CZ5Kh.
2025-4-22 02:00:44 - Event ID: evt_1RGaoxDrMO37mudtwChHiqdf; Event Type: invoice.updated
2025-4-22 02:00:44 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 02:00:44 - Event ID: evt_1RGaoxDrMO37mudthHHF4PwP; Event Type: invoice.updated
2025-4-22 02:00:44 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 02:17:19 - Starting to process Stripe webhook.
2025-4-22 02:17:20 - Event ID: evt_1RGb51DrMO37mudtavZ4XqkK; Event Type: invoice.updated
2025-4-22 02:17:20 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 02:17:20 - Starting to process Stripe webhook.
2025-4-22 02:17:20 - Starting to process Stripe webhook.
2025-4-22 02:17:21 - Event ID: evt_1RGb52DrMO37mudtCb1BEsfu; Event Type: invoice.updated
2025-4-22 02:17:21 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 02:17:21 - Event ID: evt_1RGb52DrMO37mudt2MZCpqA9; Event Type: invoice.payment_failed
2025-4-22 02:17:21 - Exiting Stripe webhook - membership not found. Customer ID: cus_Glw6qXqfMfWDqt.
2025-4-22 02:38:25 - Starting to process Stripe webhook.
2025-4-22 02:38:25 - Event ID: evt_1RGbPRDrMO37mudthBJG81l9; Event Type: invoice.upcoming
2025-4-22 02:38:25 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-22 02:42:46 - Starting to process Stripe webhook.
2025-4-22 02:42:46 - Starting to process Stripe webhook.
2025-4-22 02:42:46 - Event ID: evt_3RGbTbDrMO37mudt3dNk2FwG; Event Type: payment_intent.succeeded
2025-4-22 02:42:46 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-22 02:42:46 - Event ID: evt_3RGbTbDrMO37mudt3H25WugT; Event Type: charge.succeeded
2025-4-22 02:42:47 - Exiting Stripe webhook - membership not found. Customer ID: cus_PmuLm8C3pnvDIJ.
2025-4-22 02:42:47 - Starting to process Stripe webhook.
2025-4-22 02:42:48 - Starting to process Stripe webhook.
2025-4-22 02:42:48 - Event ID: evt_3RGbTbDrMO37mudt3yzW6Xxz; Event Type: payment_intent.created
2025-4-22 02:42:48 - Starting to process Stripe webhook.
2025-4-22 02:42:48 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 02:42:48 - Event ID: evt_1RGbTfDrMO37mudtb7scTZh7; Event Type: invoice.updated
2025-4-22 02:42:48 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 02:42:48 - Starting to process Stripe webhook.
2025-4-22 02:42:48 - Event ID: evt_1RGbTfDrMO37mudtJ9WtQpmJ; Event Type: invoice.finalized
2025-4-22 02:42:48 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-22 02:42:48 - Event ID: evt_1RGbTfDrMO37mudtR8h9DOQa; Event Type: invoice.paid
2025-4-22 02:42:48 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-22 02:42:48 - Starting to process Stripe webhook.
2025-4-22 02:42:48 - Event ID: evt_1RGbTfDrMO37mudtnCyi9t2F; Event Type: invoice.payment_succeeded
2025-4-22 02:42:49 - Exiting Stripe webhook - membership not found. Customer ID: cus_PmuLm8C3pnvDIJ.
2025-4-22 03:16:13 - Starting to process Stripe webhook.
2025-4-22 03:16:13 - Event ID: evt_1RGc01DrMO37mudtU4nezfI2; Event Type: customer.subscription.deleted
2025-4-22 03:16:14 - Exiting Stripe webhook - membership not found. Customer ID: cus_RurRFUNdY9Cosh.
2025-4-22 03:48:57 - Starting to process Stripe webhook.
2025-4-22 03:48:57 - Event ID: evt_1RGcVhDrMO37mudt83gbUAJw; Event Type: invoice.upcoming
2025-4-22 03:48:57 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-22 04:29:31 - Starting to process Stripe webhook.
2025-4-22 04:29:32 - Event ID: evt_1RGd8xDrMO37mudtJyjMyplc; Event Type: invoice.updated
2025-4-22 04:29:32 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 04:29:35 - Starting to process Stripe webhook.
2025-4-22 04:29:35 - Starting to process Stripe webhook.
2025-4-22 04:29:36 - Event ID: evt_1RGd91DrMO37mudtJd6hKu9c; Event Type: invoice.updated
2025-4-22 04:29:36 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 04:29:36 - Event ID: evt_1RGd91DrMO37mudt1b5Nfjoz; Event Type: invoice.payment_failed
2025-4-22 04:29:36 - Exiting Stripe webhook - membership not found. Customer ID: cus_H84ntlAcgNME2u.
2025-4-22 04:50:40 - Starting to process Stripe webhook.
2025-4-22 04:50:40 - Starting to process Stripe webhook.
2025-4-22 04:50:40 - Event ID: evt_1RGdTQDrMO37mudtfv0Dg6Dl; Event Type: invoice.created
2025-4-22 04:50:40 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-22 04:50:40 - Event ID: evt_1RGdTQDrMO37mudt6J1Cv1Hi; Event Type: customer.subscription.updated
2025-4-22 04:50:40 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-22 05:51:07 - Starting to process Stripe webhook.
2025-4-22 05:51:07 - Event ID: evt_3RGePsDrMO37mudt4s2ITrya; Event Type: charge.succeeded
2025-4-22 05:51:07 - Event ID: evt_3RGePsDrMO37mudt47KDa17w; Event Type: payment_intent.succeeded
2025-4-22 05:51:07 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-22 05:51:08 - Exiting Stripe webhook - membership not found. Customer ID: cus_JLcgGlmARGgH0v.
2025-4-22 05:51:08 - Starting to process Stripe webhook.
2025-4-22 05:51:08 - Event ID: evt_1RGePwDrMO37mudtXNxaqpkF; Event Type: invoice.updated
2025-4-22 05:51:08 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 05:51:08 - Starting to process Stripe webhook.
2025-4-22 05:51:08 - Starting to process Stripe webhook.
2025-4-22 05:51:08 - Starting to process Stripe webhook.
2025-4-22 05:51:08 - Event ID: evt_1RGePwDrMO37mudtxU5Hv95V; Event Type: invoice.finalized
2025-4-22 05:51:08 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-22 05:51:08 - Event ID: evt_1RGePwDrMO37mudtEzEhf8op; Event Type: invoice.paid
2025-4-22 05:51:08 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-22 05:51:08 - Event ID: evt_1RGePwDrMO37mudt5dUuW25W; Event Type: invoice.payment_succeeded
2025-4-22 05:51:09 - Exiting Stripe webhook - membership not found. Customer ID: cus_JLcgGlmARGgH0v.
2025-4-22 05:51:09 - Starting to process Stripe webhook.
2025-4-22 05:51:09 - Event ID: evt_3RGePsDrMO37mudt4ZrLyY5v; Event Type: payment_intent.created
2025-4-22 05:51:09 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 07:42:12 - Starting rcp_check_for_expired_users() cron job.
2025-4-22 07:42:12 - No expired memberships found.
2025-4-22 07:42:12 - Starting rcp_check_for_soon_to_expire_users() cron job.
2025-4-22 07:42:12 - Processing expiration reminder. ID: 0; Period: -2weeks; Levels: all.
2025-4-22 07:42:12 - Reminder is not enabled - exiting.
2025-4-22 07:42:12 - Processing expiration reminder. ID: 1; Period: today; Levels: all.
2025-4-22 07:42:12 - Reminder is not enabled - exiting.
2025-4-22 07:52:43 - Starting to process Stripe webhook.
2025-4-22 07:52:44 - Event ID: evt_1RGgJbDrMO37mudtMfqlhlPK; Event Type: invoice.upcoming
2025-4-22 07:52:44 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-22 08:21:14 - Starting to process Stripe webhook.
2025-4-22 08:21:14 - Event ID: evt_1RGglCDrMO37mudt6E1kr1OP; Event Type: invoice.upcoming
2025-4-22 08:21:14 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-22 09:32:30 - Starting to process Stripe webhook.
2025-4-22 09:32:30 - Event ID: evt_3RGhsADrMO37mudt4SKTnogS; Event Type: payment_intent.created
2025-4-22 09:32:30 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 09:32:32 - Starting to process Stripe webhook.
2025-4-22 09:32:32 - Event ID: evt_3RGhsADrMO37mudt4DPz4dLM; Event Type: payment_intent.succeeded
2025-4-22 09:32:32 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-22 09:32:32 - Starting to process Stripe webhook.
2025-4-22 09:32:32 - Event ID: evt_3RGhsADrMO37mudt4sfV4SAO; Event Type: charge.succeeded
2025-4-22 09:32:32 - Stripe Webhook: Getting membership by subscription key: e8546cc40a6f23d7cb0979544623a00a
2025-4-22 09:32:32 - Exiting Stripe webhook - membership not found. Customer ID: cus_LY9MiwBUBIdC95.
2025-4-22 09:32:33 - Starting to process Stripe webhook.
2025-4-22 09:32:33 - Event ID: evt_1RGhsDDrMO37mudtODRkAzqB; Event Type: customer.updated
2025-4-22 09:32:33 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.updated" is not currently handled.
2025-4-22 09:32:36 - Starting to process Stripe webhook.
2025-4-22 09:32:36 - Event ID: evt_1RGhsFDrMO37mudtnx6fQhiq; Event Type: customer.updated
2025-4-22 09:32:36 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.updated" is not currently handled.
2025-4-22 09:32:36 - Starting to process Stripe webhook.
2025-4-22 09:32:36 - Starting to process Stripe webhook.
2025-4-22 09:32:36 - Event ID: evt_1RGhsFDrMO37mudtj001TBWK; Event Type: customer.subscription.created
2025-4-22 09:32:36 - Event ID: evt_1RGhsFDrMO37mudtMXCzLyCl; Event Type: setup_intent.created
2025-4-22 09:32:36 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "setup_intent.created" is not currently handled.
2025-4-22 09:32:36 - Starting to process Stripe webhook.
2025-4-22 09:32:36 - Starting to process Stripe webhook.
2025-4-22 09:32:36 - Exiting Stripe webhook - membership not found. Customer ID: cus_LY9MiwBUBIdC95.
2025-4-22 09:32:36 - Event ID: evt_1RGhsFDrMO37mudtaUs5vxQP; Event Type: setup_intent.succeeded
2025-4-22 09:32:36 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "setup_intent.succeeded" is not currently handled.
2025-4-22 09:32:36 - Event ID: evt_1RGhsGDrMO37mudtshOVdCrH; Event Type: invoice.created
2025-4-22 09:32:36 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-22 09:32:37 - Starting to process Stripe webhook.
2025-4-22 09:32:37 - Event ID: evt_1RGhsGDrMO37mudt7pi4Jvae; Event Type: invoice.finalized
2025-4-22 09:32:37 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-22 09:32:37 - Starting to process Stripe webhook.
2025-4-22 09:32:37 - Starting to process Stripe webhook.
2025-4-22 09:32:37 - Event ID: evt_1RGhsGDrMO37mudtrVndQ0yA; Event Type: invoice.paid
2025-4-22 09:32:37 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-22 09:32:38 - Event ID: evt_1RGhsGDrMO37mudtubasNTr5; Event Type: invoice.payment_succeeded
2025-4-22 09:32:38 - Exiting Stripe webhook - membership not found. Customer ID: cus_LY9MiwBUBIdC95.
2025-4-22 10:13:01 - "Can cancel" status for membership #31630: false. Reason: membership not recurring.
2025-4-22 10:13:01 - "Can cancel" status for membership #31630: false. Reason: membership not recurring.
2025-4-22 10:23:56 - Starting to process Stripe webhook.
2025-4-22 10:23:56 - Event ID: evt_1RGifuDrMO37mudtQbzVdY1h; Event Type: invoice.payment_failed
2025-4-22 10:23:56 - Starting to process Stripe webhook.
2025-4-22 10:23:56 - Exiting Stripe webhook - membership not found. Customer ID: cus_EpLDtt43jFrZRf.
2025-4-22 10:23:56 - Starting to process Stripe webhook.
2025-4-22 10:23:56 - Event ID: evt_1RGifuDrMO37mudtdvHoEWYJ; Event Type: invoice.updated
2025-4-22 10:23:56 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 10:23:57 - Event ID: evt_1RGifvDrMO37mudt0IYb3TEI; Event Type: invoice.updated
2025-4-22 10:23:57 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 10:24:02 - Starting to process Stripe webhook.
2025-4-22 10:24:02 - Starting to process Stripe webhook.
2025-4-22 10:24:02 - Event ID: evt_1RGifuDrMO37mudtRec4mvnt; Event Type: customer.subscription.deleted
2025-4-22 10:24:02 - Event ID: evt_1RGifuDrMO37mudtdxChAPrT; Event Type: invoice.updated
2025-4-22 10:24:02 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 10:24:03 - Exiting Stripe webhook - membership not found. Customer ID: cus_EpLDtt43jFrZRf.
2025-4-22 10:25:12 - Starting to process Stripe webhook.
2025-4-22 10:25:13 - Event ID: evt_3RGih9DrMO37mudt2mQmZPpi; Event Type: payment_intent.created
2025-4-22 10:25:13 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 10:25:14 - Starting to process Stripe webhook.
2025-4-22 10:25:15 - Event ID: evt_3RGih9DrMO37mudt2MFkeNme; Event Type: payment_intent.succeeded
2025-4-22 10:25:15 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-22 10:25:15 - Starting to process Stripe webhook.
2025-4-22 10:25:15 - Event ID: evt_3RGih9DrMO37mudt2qObOLmy; Event Type: charge.succeeded
2025-4-22 10:25:15 - Stripe Webhook: Getting membership by subscription key: 68c2e2e40b3df5f755e22c3ab7a73113
2025-4-22 10:25:15 - Exiting Stripe webhook - membership not found. Customer ID: cus_L3y4FQbp7jaOuj.
2025-4-22 10:28:24 - Starting to process Stripe webhook.
2025-4-22 10:28:24 - Starting to process Stripe webhook.
2025-4-22 10:28:24 - Event ID: evt_1RGikFDrMO37mudtORoU5QxU; Event Type: customer.subscription.updated
2025-4-22 10:28:24 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-22 10:28:24 - Event ID: evt_1RGikFDrMO37mudtHtjsBMSL; Event Type: invoice.created
2025-4-22 10:28:24 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-22 11:29:05 - Starting to process Stripe webhook.
2025-4-22 11:29:06 - Starting to process Stripe webhook.
2025-4-22 11:29:06 - Event ID: evt_3RGjgvDrMO37mudt0lXe1xiZ; Event Type: payment_intent.succeeded
2025-4-22 11:29:06 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-22 11:29:06 - Event ID: evt_3RGjgvDrMO37mudt0v7oFI0d; Event Type: charge.succeeded
2025-4-22 11:29:06 - Starting to process Stripe webhook.
2025-4-22 11:29:06 - Exiting Stripe webhook - membership not found. Customer ID: cus_F49pQ4YwB9WOUb.
2025-4-22 11:29:06 - Event ID: evt_3RGjgvDrMO37mudt0HS3Wz3O; Event Type: payment_intent.created
2025-4-22 11:29:06 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 11:29:06 - Starting to process Stripe webhook.
2025-4-22 11:29:07 - Starting to process Stripe webhook.
2025-4-22 11:29:07 - Starting to process Stripe webhook.
2025-4-22 11:29:07 - Event ID: evt_1RGjgzDrMO37mudtzKBJya6f; Event Type: invoice.updated
2025-4-22 11:29:07 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 11:29:07 - Event ID: evt_1RGjgzDrMO37mudt8Ci59Mve; Event Type: invoice.finalized
2025-4-22 11:29:07 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-22 11:29:07 - Event ID: evt_1RGjh0DrMO37mudt6PbMEaqR; Event Type: invoice.paid
2025-4-22 11:29:07 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-22 11:29:07 - Starting to process Stripe webhook.
2025-4-22 11:29:07 - Event ID: evt_1RGjh0DrMO37mudtqOqfhC23; Event Type: invoice.payment_succeeded
2025-4-22 11:29:08 - Exiting Stripe webhook - membership not found. Customer ID: cus_F49pQ4YwB9WOUb.
2025-4-22 11:41:15 - Starting to process Stripe webhook.
2025-4-22 11:41:15 - Starting to process Stripe webhook.
2025-4-22 11:41:16 - Event ID: evt_1RGjsjDrMO37mudt1ag080NP; Event Type: customer.subscription.updated
2025-4-22 11:41:16 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-22 11:41:16 - Event ID: evt_1RGjsjDrMO37mudt1mnq1aQ4; Event Type: invoice.created
2025-4-22 11:41:16 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-22 11:42:03 - Starting to process Stripe webhook.
2025-4-22 11:42:03 - Starting to process Stripe webhook.
2025-4-22 11:42:03 - Event ID: evt_1RGjtWDrMO37mudtpKBBTXNh; Event Type: invoice.created
2025-4-22 11:42:03 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-22 11:42:03 - Event ID: evt_1RGjtWDrMO37mudtiqWH3Jkj; Event Type: customer.subscription.updated
2025-4-22 11:42:03 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-22 11:47:14 - Starting to process Stripe webhook.
2025-4-22 11:47:15 - Event ID: evt_1RGjyXDrMO37mudtvnFqkLWq; Event Type: invoice.created
2025-4-22 11:47:15 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-22 11:47:15 - Starting to process Stripe webhook.
2025-4-22 11:47:15 - Event ID: evt_1RGjyXDrMO37mudtMC74dfhN; Event Type: customer.subscription.updated
2025-4-22 11:47:15 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-22 11:50:16 - Starting to process Stripe webhook.
2025-4-22 11:50:17 - Starting to process Stripe webhook.
2025-4-22 11:50:17 - Event ID: evt_1RGk1TDrMO37mudt2m4YJUs4; Event Type: invoice.created
2025-4-22 11:50:17 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-22 11:50:17 - Event ID: evt_1RGk1TDrMO37mudt6lkfGKW8; Event Type: customer.subscription.updated
2025-4-22 11:50:17 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-22 12:16:09 - Starting to process Stripe webhook.
2025-4-22 12:16:09 - Starting to process Stripe webhook.
2025-4-22 12:16:09 - Event ID: evt_1RGkQWDrMO37mudtfQmyhFuF; Event Type: invoice.created
2025-4-22 12:16:09 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-22 12:16:09 - Event ID: evt_1RGkQWDrMO37mudtXPPNnCcJ; Event Type: customer.subscription.updated
2025-4-22 12:16:09 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-22 12:24:52 - Starting to process Stripe webhook.
2025-4-22 12:24:52 - Starting to process Stripe webhook.
2025-4-22 12:24:52 - Event ID: evt_1RGkYxDrMO37mudtH6u6IsPe; Event Type: invoice.created
2025-4-22 12:24:52 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-22 12:24:52 - Event ID: evt_1RGkYxDrMO37mudt4H9ZezCn; Event Type: customer.subscription.updated
2025-4-22 12:24:52 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-22 12:29:22 - Starting to process Stripe webhook.
2025-4-22 12:29:22 - Event ID: evt_1RGkdJDrMO37mudtXylWck5z; Event Type: invoice.upcoming
2025-4-22 12:29:22 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.upcoming" is not currently handled.
2025-4-22 12:36:07 - Starting to process Stripe webhook.
2025-4-22 12:36:07 - Event ID: evt_1RGkjqDrMO37mudtzCfSEr7T; Event Type: customer.subscription.deleted
2025-4-22 12:36:07 - Exiting Stripe webhook - membership not found. Customer ID: cus_H7hsdwWk2yzJUo.
2025-4-22 12:36:22 - Starting to process Stripe webhook.
2025-4-22 12:36:22 - Starting to process Stripe webhook.
2025-4-22 12:36:23 - Event ID: evt_1RGkk5DrMO37mudtC4Sje2UY; Event Type: invoice.created
2025-4-22 12:36:23 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-22 12:36:23 - Event ID: evt_1RGkk5DrMO37mudt1Hg8YFoW; Event Type: customer.subscription.updated
2025-4-22 12:36:23 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-22 12:41:45 - Starting to process Stripe webhook.
2025-4-22 12:41:45 - Starting to process Stripe webhook.
2025-4-22 12:41:45 - Event ID: evt_3RGkpGDrMO37mudt4m0ELXPl; Event Type: payment_intent.succeeded
2025-4-22 12:41:45 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-22 12:41:45 - Event ID: evt_3RGkpGDrMO37mudt4Rklowj2; Event Type: charge.succeeded
2025-4-22 12:41:46 - Exiting Stripe webhook - membership not found. Customer ID: cus_JHh8Ire9qUnrIO.
2025-4-22 12:41:46 - Starting to process Stripe webhook.
2025-4-22 12:41:46 - Event ID: evt_3RGkpGDrMO37mudt4TAifpgD; Event Type: payment_intent.created
2025-4-22 12:41:46 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 12:41:47 - Starting to process Stripe webhook.
2025-4-22 12:41:47 - Starting to process Stripe webhook.
2025-4-22 12:41:47 - Event ID: evt_1RGkpJDrMO37mudtz8OhmaKg; Event Type: invoice.finalized
2025-4-22 12:41:47 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-22 12:41:47 - Event ID: evt_1RGkpJDrMO37mudtAlu2u4G5; Event Type: invoice.updated
2025-4-22 12:41:47 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 12:41:47 - Starting to process Stripe webhook.
2025-4-22 12:41:47 - Starting to process Stripe webhook.
2025-4-22 12:41:47 - Event ID: evt_1RGkpKDrMO37mudtgUVxpzOm; Event Type: invoice.paid
2025-4-22 12:41:47 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-22 12:41:47 - Event ID: evt_1RGkpKDrMO37mudtbFF2XzON; Event Type: invoice.payment_succeeded
2025-4-22 12:41:47 - Exiting Stripe webhook - membership not found. Customer ID: cus_JHh8Ire9qUnrIO.
2025-4-22 12:42:26 - Starting to process Stripe webhook.
2025-4-22 12:42:26 - Starting to process Stripe webhook.
2025-4-22 12:42:26 - Event ID: evt_3RGkpuDrMO37mudt20ZhLqmj; Event Type: payment_intent.succeeded
2025-4-22 12:42:26 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-22 12:42:26 - Event ID: evt_3RGkpuDrMO37mudt2Y7Qh5KI; Event Type: charge.succeeded
2025-4-22 12:42:26 - Starting to process Stripe webhook.
2025-4-22 12:42:27 - Exiting Stripe webhook - membership not found. Customer ID: cus_R4q8Rno1hquSwM.
2025-4-22 12:42:27 - Event ID: evt_3RGkpuDrMO37mudt2vQ1oqHu; Event Type: payment_intent.created
2025-4-22 12:42:27 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 12:42:27 - Starting to process Stripe webhook.
2025-4-22 12:42:27 - Starting to process Stripe webhook.
2025-4-22 12:42:27 - Event ID: evt_1RGkpyDrMO37mudt08ReSE75; Event Type: invoice.updated
2025-4-22 12:42:27 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 12:42:27 - Starting to process Stripe webhook.
2025-4-22 12:42:27 - Event ID: evt_1RGkpyDrMO37mudtckZb3ZCH; Event Type: invoice.finalized
2025-4-22 12:42:27 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-22 12:42:27 - Event ID: evt_1RGkpyDrMO37mudtTIdXsDlb; Event Type: invoice.paid
2025-4-22 12:42:27 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-22 12:42:28 - Starting to process Stripe webhook.
2025-4-22 12:42:28 - Event ID: evt_1RGkpyDrMO37mudtyAHkAh4C; Event Type: invoice.payment_succeeded
2025-4-22 12:42:28 - Exiting Stripe webhook - membership not found. Customer ID: cus_R4q8Rno1hquSwM.
2025-4-22 12:48:15 - Starting to process Stripe webhook.
2025-4-22 12:48:15 - Starting to process Stripe webhook.
2025-4-22 12:48:15 - Event ID: evt_3RGkvXDrMO37mudt0qhHUbjr; Event Type: payment_intent.succeeded
2025-4-22 12:48:15 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-22 12:48:15 - Event ID: evt_3RGkvXDrMO37mudt0H0oOgqc; Event Type: charge.succeeded
2025-4-22 12:48:15 - Starting to process Stripe webhook.
2025-4-22 12:48:15 - Exiting Stripe webhook - membership not found. Customer ID: cus_Pmgred5NRJgrrr.
2025-4-22 12:48:16 - Starting to process Stripe webhook.
2025-4-22 12:48:16 - Event ID: evt_3RGkvXDrMO37mudt0V88SaC0; Event Type: payment_intent.created
2025-4-22 12:48:16 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 12:48:16 - Starting to process Stripe webhook.
2025-4-22 12:48:16 - Event ID: evt_1RGkvbDrMO37mudt4xm9GuXI; Event Type: invoice.updated
2025-4-22 12:48:16 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 12:48:16 - Starting to process Stripe webhook.
2025-4-22 12:48:16 - Event ID: evt_1RGkvbDrMO37mudtsRbH4Jk6; Event Type: invoice.finalized
2025-4-22 12:48:16 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-22 12:48:16 - Event ID: evt_1RGkvbDrMO37mudtV2Sg5pDU; Event Type: invoice.paid
2025-4-22 12:48:16 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-22 12:48:16 - Starting to process Stripe webhook.
2025-4-22 12:48:17 - Event ID: evt_1RGkvbDrMO37mudt6yW29GMw; Event Type: invoice.payment_succeeded
2025-4-22 12:48:17 - Exiting Stripe webhook - membership not found. Customer ID: cus_Pmgred5NRJgrrr.
2025-4-22 12:51:20 - Starting to process Stripe webhook.
2025-4-22 12:51:20 - Starting to process Stripe webhook.
2025-4-22 12:51:20 - Event ID: evt_3RGkyWDrMO37mudt2gPs3NdO; Event Type: payment_intent.payment_failed
2025-4-22 12:51:20 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.payment_failed" is not currently handled.
2025-4-22 12:51:20 - Event ID: evt_3RGkyWDrMO37mudt2aSp1ox3; Event Type: charge.failed
2025-4-22 12:51:20 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "charge.failed" is not currently handled.
2025-4-22 12:51:21 - Starting to process Stripe webhook.
2025-4-22 12:51:21 - Starting to process Stripe webhook.
2025-4-22 12:51:22 - Event ID: evt_3RGkyWDrMO37mudt2sUP76Br; Event Type: payment_intent.created
2025-4-22 12:51:22 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 12:51:22 - Starting to process Stripe webhook.
2025-4-22 12:51:22 - Event ID: evt_1RGkyaDrMO37mudtfsyBvjmI; Event Type: customer.updated
2025-4-22 12:51:22 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.updated" is not currently handled.
2025-4-22 12:51:22 - Event ID: evt_1RGkybDrMO37mudtTQ6ofStx; Event Type: invoice.updated
2025-4-22 12:51:22 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 12:51:22 - Event ID: evt_1RGkybDrMO37mudtNT8TrOZD; Event Type: invoice.finalized
2025-4-22 12:51:22 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-22 12:51:22 - Starting to process Stripe webhook.
2025-4-22 12:51:22 - Event ID: evt_1RGkybDrMO37mudt67Oi1wmf; Event Type: customer.subscription.updated
2025-4-22 12:51:22 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-22 12:51:25 - Starting to process Stripe webhook.
2025-4-22 12:51:26 - Starting to process Stripe webhook.
2025-4-22 12:51:26 - Event ID: evt_1RGkyeDrMO37mudtCiK7D1Ab; Event Type: invoice.updated
2025-4-22 12:51:26 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 12:51:26 - Event ID: evt_1RGkyeDrMO37mudtvCVGh15E; Event Type: invoice.payment_failed
2025-4-22 12:51:26 - Exiting Stripe webhook - membership not found. Customer ID: cus_RzQLDtAdxkXJ5M.
2025-4-22 13:16:36 - Starting to process Stripe webhook.
2025-4-22 13:16:36 - Starting to process Stripe webhook.
2025-4-22 13:16:36 - Event ID: evt_3RGlMxDrMO37mudt2Ev4vqdN; Event Type: payment_intent.succeeded
2025-4-22 13:16:36 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-22 13:16:36 - Event ID: evt_3RGlMxDrMO37mudt2A3G7xoB; Event Type: charge.succeeded
2025-4-22 13:16:37 - Exiting Stripe webhook - membership not found. Customer ID: cus_PEaosEcHOvFgLa.
2025-4-22 13:16:38 - Starting to process Stripe webhook.
2025-4-22 13:16:38 - Starting to process Stripe webhook.
2025-4-22 13:16:38 - Event ID: evt_1RGlN2DrMO37mudtl6e5XykH; Event Type: invoice.updated
2025-4-22 13:16:38 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 13:16:38 - Event ID: evt_3RGlMxDrMO37mudt23zLZNtO; Event Type: payment_intent.created
2025-4-22 13:16:38 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 13:16:38 - Starting to process Stripe webhook.
2025-4-22 13:16:38 - Starting to process Stripe webhook.
2025-4-22 13:16:38 - Starting to process Stripe webhook.
2025-4-22 13:16:38 - Event ID: evt_1RGlN2DrMO37mudtlCY2g9bJ; Event Type: invoice.finalized
2025-4-22 13:16:38 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-22 13:16:39 - Event ID: evt_1RGlN3DrMO37mudtetwLR4hy; Event Type: invoice.paid
2025-4-22 13:16:39 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-22 13:16:39 - Event ID: evt_1RGlN3DrMO37mudtFSsnelLc; Event Type: invoice.payment_succeeded
2025-4-22 13:16:39 - Exiting Stripe webhook - membership not found. Customer ID: cus_PEaosEcHOvFgLa.
2025-4-22 13:25:15 - Starting to process Stripe webhook.
2025-4-22 13:25:15 - Starting to process Stripe webhook.
2025-4-22 13:25:15 - Event ID: evt_3RGlVLDrMO37mudt3NELkykn; Event Type: payment_intent.succeeded
2025-4-22 13:25:15 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-22 13:25:15 - Event ID: evt_3RGlVLDrMO37mudt3j9uWxOr; Event Type: charge.succeeded
2025-4-22 13:25:15 - Starting to process Stripe webhook.
2025-4-22 13:25:16 - Event ID: evt_3RGlVLDrMO37mudt3Bi9vnhl; Event Type: payment_intent.created
2025-4-22 13:25:16 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 13:25:16 - Exiting Stripe webhook - membership not found. Customer ID: cus_BTJzabu7wqXf1L.
2025-4-22 13:25:16 - Starting to process Stripe webhook.
2025-4-22 13:25:16 - Event ID: evt_1RGlVPDrMO37mudtIeuo1Sfw; Event Type: invoice.updated
2025-4-22 13:25:16 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 13:25:16 - Starting to process Stripe webhook.
2025-4-22 13:25:16 - Starting to process Stripe webhook.
2025-4-22 13:25:17 - Event ID: evt_1RGlVPDrMO37mudtlxgATsHh; Event Type: invoice.finalized
2025-4-22 13:25:17 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-22 13:25:17 - Event ID: evt_1RGlVPDrMO37mudt0S6NAKJz; Event Type: invoice.paid
2025-4-22 13:25:17 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-22 13:25:17 - Starting to process Stripe webhook.
2025-4-22 13:25:17 - Event ID: evt_1RGlVPDrMO37mudtxNgMJCS5; Event Type: invoice.payment_succeeded
2025-4-22 13:25:17 - Exiting Stripe webhook - membership not found. Customer ID: cus_BTJzabu7wqXf1L.
2025-4-22 13:29:45 - Starting to process Stripe webhook.
2025-4-22 13:29:46 - Event ID: evt_1RGlZjDrMO37mudtYZNY8ONX; Event Type: customer.subscription.trial_will_end
2025-4-22 13:29:46 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.trial_will_end" is not currently handled.
2025-4-22 13:36:51 - Starting to process Stripe webhook.
2025-4-22 13:36:52 - Starting to process Stripe webhook.
2025-4-22 13:36:52 - Event ID: evt_3RGlgYDrMO37mudt0rbkGQM8; Event Type: payment_intent.succeeded
2025-4-22 13:36:52 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-22 13:36:52 - Starting to process Stripe webhook.
2025-4-22 13:36:52 - Event ID: evt_3RGlgYDrMO37mudt0N6YRBQp; Event Type: charge.succeeded
2025-4-22 13:36:52 - Event ID: evt_3RGlgYDrMO37mudt0YgINoMu; Event Type: payment_intent.created
2025-4-22 13:36:52 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 13:36:52 - Exiting Stripe webhook - membership not found. Customer ID: cus_QtdDnDUrKBzNPK.
2025-4-22 13:36:52 - Starting to process Stripe webhook.
2025-4-22 13:36:52 - Starting to process Stripe webhook.
2025-4-22 13:36:53 - Event ID: evt_1RGlgdDrMO37mudtM1X6Ilf1; Event Type: invoice.updated
2025-4-22 13:36:53 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 13:36:53 - Event ID: evt_1RGlgdDrMO37mudtIyEEDQ7x; Event Type: invoice.finalized
2025-4-22 13:36:53 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-22 13:36:53 - Starting to process Stripe webhook.
2025-4-22 13:36:53 - Event ID: evt_1RGlgdDrMO37mudt240m14T0; Event Type: invoice.paid
2025-4-22 13:36:53 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-22 13:36:53 - Starting to process Stripe webhook.
2025-4-22 13:36:53 - Event ID: evt_1RGlgeDrMO37mudtJTH5BO6y; Event Type: invoice.payment_succeeded
2025-4-22 13:36:54 - Exiting Stripe webhook - membership not found. Customer ID: cus_QtdDnDUrKBzNPK.
2025-4-22 13:39:22 - Starting to process Stripe webhook.
2025-4-22 13:39:22 - Starting to process Stripe webhook.
2025-4-22 13:39:22 - Event ID: evt_3RGlj3DrMO37mudt2jD2gyx2; Event Type: payment_intent.created
2025-4-22 13:39:22 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 13:39:23 - Event ID: evt_1RGlj3DrMO37mudtcuYk0E9e; Event Type: customer.created
2025-4-22 13:39:23 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.created" is not currently handled.
2025-4-22 13:39:25 - Starting to process Stripe webhook.
2025-4-22 13:39:25 - Starting to process Stripe webhook.
2025-4-22 13:39:25 - Event ID: evt_3RGlj3DrMO37mudt2vgylWj8; Event Type: payment_intent.succeeded
2025-4-22 13:39:25 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-22 13:39:25 - Event ID: evt_3RGlj3DrMO37mudt2DFM4EU0; Event Type: charge.succeeded
2025-4-22 13:39:25 - Stripe Webhook: Getting membership by subscription key: f78f780a924fe04029bb98ac8897b312
2025-4-22 13:39:25 - Starting to process Stripe webhook.
2025-4-22 13:39:25 - Exiting Stripe webhook - membership not found. Customer ID: cus_SB7zndqL0NJPrT.
2025-4-22 13:39:25 - Event ID: evt_1RGlj6DrMO37mudtilsOvcfa; Event Type: payment_method.attached
2025-4-22 13:39:25 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_method.attached" is not currently handled.
2025-4-22 13:39:26 - Starting to process Stripe webhook.
2025-4-22 13:39:26 - Event ID: evt_1RGlj7DrMO37mudtIbKutZh1; Event Type: customer.updated
2025-4-22 13:39:26 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.updated" is not currently handled.
2025-4-22 13:39:27 - Starting to process Stripe webhook.
2025-4-22 13:39:27 - Event ID: evt_1RGlj8DrMO37mudtOTZe44Xb; Event Type: customer.updated
2025-4-22 13:39:27 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.updated" is not currently handled.
2025-4-22 13:39:29 - Starting to process Stripe webhook.
2025-4-22 13:39:29 - Starting to process Stripe webhook.
2025-4-22 13:39:29 - Event ID: evt_1RGljADrMO37mudtrdaMtVnv; Event Type: customer.updated
2025-4-22 13:39:29 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.updated" is not currently handled.
2025-4-22 13:39:29 - Starting to process Stripe webhook.
2025-4-22 13:39:29 - Starting to process Stripe webhook.
2025-4-22 13:39:29 - Event ID: evt_1RGljADrMO37mudtaQ2n2w7O; Event Type: setup_intent.succeeded
2025-4-22 13:39:29 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "setup_intent.succeeded" is not currently handled.
2025-4-22 13:39:29 - Starting to process Stripe webhook.
2025-4-22 13:39:29 - Event ID: evt_1RGljADrMO37mudtkMLmcZDA; Event Type: customer.subscription.created
2025-4-22 13:39:29 - Event ID: evt_1RGljADrMO37mudtx21AveJO; Event Type: setup_intent.created
2025-4-22 13:39:29 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "setup_intent.created" is not currently handled.
2025-4-22 13:39:30 - Event ID: evt_1RGljADrMO37mudtEsaB7xMc; Event Type: invoice.created
2025-4-22 13:39:30 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-22 13:39:30 - Exiting Stripe webhook - membership not found. Customer ID: cus_SB7zndqL0NJPrT.
2025-4-22 13:39:30 - Starting to process Stripe webhook.
2025-4-22 13:39:30 - Starting to process Stripe webhook.
2025-4-22 13:39:30 - Event ID: evt_1RGljADrMO37mudtTQufTuO4; Event Type: invoice.finalized
2025-4-22 13:39:30 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-22 13:39:30 - Starting to process Stripe webhook.
2025-4-22 13:39:30 - Event ID: evt_1RGljADrMO37mudtm3cN6CDq; Event Type: invoice.paid
2025-4-22 13:39:30 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-22 13:39:31 - Event ID: evt_1RGljADrMO37mudt8ElJQitk; Event Type: invoice.payment_succeeded
2025-4-22 13:39:31 - Exiting Stripe webhook - membership not found. Customer ID: cus_SB7zndqL0NJPrT.
2025-4-22 13:51:53 - "Can cancel" status for membership #31729: false. Reason: membership not recurring.
2025-4-22 13:51:53 - "Can cancel" status for membership #31729: false. Reason: membership not recurring.
2025-4-22 13:56:59 - "Can cancel" status for membership #31729: false. Reason: membership not recurring.
2025-4-22 13:56:59 - "Can cancel" status for membership #31729: false. Reason: membership not recurring.
2025-4-22 14:03:25 - Starting to process Stripe webhook.
2025-4-22 14:03:25 - Starting to process Stripe webhook.
2025-4-22 14:03:25 - Event ID: evt_1RGm6KDrMO37mudtFFeGYiyi; Event Type: invoice.created
2025-4-22 14:03:25 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.created" is not currently handled.
2025-4-22 14:03:25 - Event ID: evt_1RGm6KDrMO37mudtPHlayG2U; Event Type: customer.subscription.updated
2025-4-22 14:03:25 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "customer.subscription.updated" is not currently handled.
2025-4-22 15:04:01 - Starting to process Stripe webhook.
2025-4-22 15:04:02 - Starting to process Stripe webhook.
2025-4-22 15:04:02 - Event ID: evt_3RGn2vDrMO37mudt2xBQYxek; Event Type: payment_intent.succeeded
2025-4-22 15:04:02 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.succeeded" is not currently handled.
2025-4-22 15:04:02 - Event ID: evt_3RGn2vDrMO37mudt2Ra4bVYV; Event Type: charge.succeeded
2025-4-22 15:04:02 - Exiting Stripe webhook - membership not found. Customer ID: cus_H92AGcnAkSIEtd.
2025-4-22 15:04:03 - Starting to process Stripe webhook.
2025-4-22 15:04:03 - Event ID: evt_3RGn2vDrMO37mudt27YnKetw; Event Type: payment_intent.created
2025-4-22 15:04:03 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "payment_intent.created" is not currently handled.
2025-4-22 15:04:03 - Starting to process Stripe webhook.
2025-4-22 15:04:03 - Starting to process Stripe webhook.
2025-4-22 15:04:03 - Event ID: evt_1RGn30DrMO37mudto8M0RNW9; Event Type: invoice.updated
2025-4-22 15:04:03 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.updated" is not currently handled.
2025-4-22 15:04:03 - Event ID: evt_1RGn30DrMO37mudtojRTjjZz; Event Type: invoice.finalized
2025-4-22 15:04:03 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.finalized" is not currently handled.
2025-4-22 15:04:03 - Starting to process Stripe webhook.
2025-4-22 15:04:04 - Starting to process Stripe webhook.
2025-4-22 15:04:04 - Event ID: evt_1RGn30DrMO37mudtyb1oBObL; Event Type: invoice.paid
2025-4-22 15:04:04 - Exiting Stripe webhook - Unregistered Stripe webhook. The webhook "invoice.paid" is not currently handled.
2025-4-22 15:04:04 - Event ID: evt_1RGn31DrMO37mudt4nl3duWP; Event Type: invoice.payment_succeeded
2025-4-22 15:04:04 - Exiting Stripe webhook - membership not found. Customer ID: cus_H92AGcnAkSIEtd.
2025-5-13 00:51:04 - Updating customer #25137. New data: array (
  'ips' => 
  array (
    0 => '**************',
    1 => '***************',
    2 => '**************',
    3 => '**************',
    4 => '*************',
    5 => '**************',
    6 => '***************',
    7 => '**************',
    8 => '**************',
    9 => '**************',
    10 => '*************',
    11 => '127.0.0.1',
  ),
).
2025-5-13 00:51:04 - Updating customer #25137. New data: array (
  'last_login' => '2025-05-13 00:51:04',
).
2025-5-21 12:56:21 - Updating membership #31728. New data: array (
  'status' => 'expired',
).
2025-5-24 20:17:25 - Starting rcp_check_for_expired_users() cron job.
2025-5-24 20:17:25 - Expiring membership #31727 via cron job.
2025-5-24 20:17:25 - Updating membership #31727. New data: array (
  'status' => 'expired',
).
2025-6-03 13:57:35 - Starting rcp_check_for_soon_to_expire_users() cron job.
2025-6-03 13:57:35 - Processing expiration reminder. ID: 0; Period: -2weeks; Levels: all.
2025-6-03 13:57:35 - Reminder is not enabled - exiting.
2025-6-03 13:57:35 - Processing expiration reminder. ID: 1; Period: today; Levels: all.
2025-6-03 13:57:35 - Reminder is not enabled - exiting.
2025-6-03 13:58:02 - Starting rcp_check_member_counts() cron job.
2025-6-03 13:58:02 - Starting rcp_mark_abandoned_payments() cron job.
2025-6-03 14:13:00 - Starting rcp_check_for_expired_users() cron job.
2025-6-03 14:13:01 - Expiring membership #31721 via cron job.
2025-6-03 14:13:01 - Updating membership #31721. New data: array (
  'status' => 'expired',
).
2025-6-04 23:42:19 - Starting rcp_check_member_counts() cron job.
2025-6-04 23:42:19 - Starting rcp_mark_abandoned_payments() cron job.
2025-6-04 23:43:27 - Starting rcp_check_for_soon_to_expire_users() cron job.
2025-6-04 23:43:27 - Processing expiration reminder. ID: 0; Period: -2weeks; Levels: all.
2025-6-04 23:43:27 - Reminder is not enabled - exiting.
2025-6-04 23:43:27 - Processing expiration reminder. ID: 1; Period: today; Levels: all.
2025-6-04 23:43:27 - Reminder is not enabled - exiting.
2025-6-04 23:43:27 - Starting rcp_check_for_expired_users() cron job.
2025-6-04 23:43:27 - Expiring membership #31720 via cron job.
2025-6-04 23:43:27 - Updating membership #31720. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:28 - Expired email sent to user #25269 for membership #31720.
2025-6-04 23:43:28 - Expired email sent to admin(s) regarding membership #31720.
2025-6-04 23:43:28 - Expiring membership #31719 via cron job.
2025-6-04 23:43:28 - Updating membership #31719. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:29 - Expired email sent to user #25268 for membership #31719.
2025-6-04 23:43:29 - Expired email sent to admin(s) regarding membership #31719.
2025-6-04 23:43:29 - Expiring membership #31716 via cron job.
2025-6-04 23:43:29 - Updating membership #31716. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:30 - Expired email sent to user #25265 for membership #31716.
2025-6-04 23:43:30 - Expired email sent to admin(s) regarding membership #31716.
2025-6-04 23:43:30 - Expiring membership #31715 via cron job.
2025-6-04 23:43:30 - Updating membership #31715. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:31 - Expired email sent to user #25264 for membership #31715.
2025-6-04 23:43:31 - Expired email sent to admin(s) regarding membership #31715.
2025-6-04 23:43:31 - Expiring membership #31714 via cron job.
2025-6-04 23:43:31 - Updating membership #31714. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:32 - Expired email sent to user #25263 for membership #31714.
2025-6-04 23:43:32 - Expired email sent to admin(s) regarding membership #31714.
2025-6-04 23:43:32 - Expiring membership #31712 via cron job.
2025-6-04 23:43:32 - Updating membership #31712. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:34 - Expired email sent to user #25261 for membership #31712.
2025-6-04 23:43:34 - Expired email sent to admin(s) regarding membership #31712.
2025-6-04 23:43:34 - Expiring membership #31709 via cron job.
2025-6-04 23:43:34 - Updating membership #31709. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:35 - Expired email sent to user #25258 for membership #31709.
2025-6-04 23:43:35 - Expired email sent to admin(s) regarding membership #31709.
2025-6-04 23:43:35 - Expiring membership #31708 via cron job.
2025-6-04 23:43:35 - Updating membership #31708. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:37 - Expired email sent to user #25257 for membership #31708.
2025-6-04 23:43:37 - Expired email sent to admin(s) regarding membership #31708.
2025-6-04 23:43:37 - Expiring membership #31707 via cron job.
2025-6-04 23:43:37 - Updating membership #31707. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:37 - Expired email sent to user #25256 for membership #31707.
2025-6-04 23:43:37 - Expired email sent to admin(s) regarding membership #31707.
2025-6-04 23:43:37 - Expiring membership #31699 via cron job.
2025-6-04 23:43:37 - Updating membership #31699. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:38 - Expired email sent to user #25249 for membership #31699.
2025-6-04 23:43:38 - Expired email sent to admin(s) regarding membership #31699.
2025-6-04 23:43:38 - Expiring membership #31694 via cron job.
2025-6-04 23:43:38 - Updating membership #31694. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:39 - Expired email sent to user #25244 for membership #31694.
2025-6-04 23:43:39 - Expired email sent to admin(s) regarding membership #31694.
2025-6-04 23:43:39 - Expiring membership #31690 via cron job.
2025-6-04 23:43:39 - Updating membership #31690. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:40 - Expired email sent to user #25240 for membership #31690.
2025-6-04 23:43:40 - Expired email sent to admin(s) regarding membership #31690.
2025-6-04 23:43:40 - Expiring membership #31686 via cron job.
2025-6-04 23:43:40 - Updating membership #31686. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:41 - Expired email sent to user #25236 for membership #31686.
2025-6-04 23:43:41 - Expired email sent to admin(s) regarding membership #31686.
2025-6-04 23:43:41 - Expiring membership #31683 via cron job.
2025-6-04 23:43:41 - Updating membership #31683. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:42 - Expired email sent to user #25233 for membership #31683.
2025-6-04 23:43:42 - Expired email sent to admin(s) regarding membership #31683.
2025-6-04 23:43:42 - Expiring membership #31681 via cron job.
2025-6-04 23:43:42 - Updating membership #31681. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:43 - Expired email sent to user #25232 for membership #31681.
2025-6-04 23:43:43 - Expired email sent to admin(s) regarding membership #31681.
2025-6-04 23:43:43 - Expiring membership #31680 via cron job.
2025-6-04 23:43:43 - Updating membership #31680. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:44 - Expired email sent to user #25231 for membership #31680.
2025-6-04 23:43:44 - Expired email sent to admin(s) regarding membership #31680.
2025-6-04 23:43:44 - Expiring membership #31679 via cron job.
2025-6-04 23:43:44 - Updating membership #31679. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:45 - Expired email sent to user #25230 for membership #31679.
2025-6-04 23:43:45 - Expired email sent to admin(s) regarding membership #31679.
2025-6-04 23:43:45 - Expiring membership #31677 via cron job.
2025-6-04 23:43:45 - Updating membership #31677. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:46 - Expired email sent to user #25228 for membership #31677.
2025-6-04 23:43:46 - Expired email sent to admin(s) regarding membership #31677.
2025-6-04 23:43:46 - Expiring membership #31674 via cron job.
2025-6-04 23:43:46 - Updating membership #31674. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:46 - Expired email sent to user #25225 for membership #31674.
2025-6-04 23:43:46 - Expired email sent to admin(s) regarding membership #31674.
2025-6-04 23:43:46 - Expiring membership #31671 via cron job.
2025-6-04 23:43:46 - Updating membership #31671. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:47 - Expired email sent to user #25223 for membership #31671.
2025-6-04 23:43:47 - Expired email sent to admin(s) regarding membership #31671.
2025-6-04 23:43:47 - Expiring membership #31666 via cron job.
2025-6-04 23:43:47 - Updating membership #31666. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:48 - Expired email sent to user #25219 for membership #31666.
2025-6-04 23:43:48 - Expired email sent to admin(s) regarding membership #31666.
2025-6-04 23:43:48 - Expiring membership #31662 via cron job.
2025-6-04 23:43:48 - Updating membership #31662. New data: array (
  'status' => 'expired',
).
2025-6-04 23:43:49 - Expired email sent to user #25217 for membership #31662.
2025-6-04 23:43:49 - Expired email sent to admin(s) regarding membership #31662.
2025-6-04 23:52:12 - Updating customer #25137. New data: array (
  'ips' => 
  array (
    0 => '**************',
    1 => '***************',
    2 => '**************',
    3 => '**************',
    4 => '*************',
    5 => '**************',
    6 => '***************',
    7 => '**************',
    8 => '**************',
    9 => '**************',
    10 => '*************',
    11 => '127.0.0.1',
  ),
).
2025-6-04 23:52:12 - Updating customer #25137. New data: array (
  'last_login' => '2025-06-04 23:52:12',
).
2025-6-05 23:19:56 - Starting rcp_check_for_soon_to_expire_users() cron job.
2025-6-05 23:19:56 - Processing expiration reminder. ID: 0; Period: -2weeks; Levels: all.
2025-6-05 23:19:56 - Reminder is not enabled - exiting.
2025-6-05 23:19:56 - Processing expiration reminder. ID: 1; Period: today; Levels: all.
2025-6-05 23:19:56 - Reminder is not enabled - exiting.
2025-6-05 23:19:56 - Starting rcp_check_for_expired_users() cron job.
2025-6-05 23:19:56 - No expired memberships found.
2025-6-05 23:20:06 - Starting rcp_check_member_counts() cron job.
2025-6-05 23:28:20 - Starting rcp_mark_abandoned_payments() cron job.
2025-6-06 08:14:42 - Starting rcp_check_for_soon_to_expire_users() cron job.
2025-6-06 08:14:42 - Processing expiration reminder. ID: 0; Period: -2weeks; Levels: all.
2025-6-06 08:14:42 - Reminder is not enabled - exiting.
2025-6-06 08:14:42 - Processing expiration reminder. ID: 1; Period: today; Levels: all.
2025-6-06 08:14:42 - Reminder is not enabled - exiting.
2025-6-06 08:14:42 - Starting rcp_check_for_expired_users() cron job.
2025-6-06 08:14:42 - No expired memberships found.
